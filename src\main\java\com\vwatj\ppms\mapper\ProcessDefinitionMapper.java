package com.vwatj.ppms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.vwatj.ppms.entity.ProcessDefinition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 流程定义Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Mapper
public interface ProcessDefinitionMapper extends BaseMapper<ProcessDefinition> {

    /**
     * 分页查询流程定义
     */
    IPage<ProcessDefinition> selectProcessDefinitionPage(
            Page<ProcessDefinition> page,
            @Param("name") String name,
            @Param("processKey") String processKey,
            @Param("status") String status,
            @Param("createdBy") String createdBy
    );

    /**
     * 根据流程Key查询
     */
    ProcessDefinition selectByProcessKey(@Param("processKey") String processKey);
}
