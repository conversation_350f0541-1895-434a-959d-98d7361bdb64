package com.vwatj.ppms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateProjectCategoryDTO;
import com.vwatj.ppms.dto.ProjectCategoryQueryDTO;
import com.vwatj.ppms.dto.UpdateProjectCategoryDTO;
import com.vwatj.ppms.entity.ProjectCategory;
import com.vwatj.ppms.mapper.ProjectCategoryMapper;
import com.vwatj.ppms.service.ProjectCategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 项目分类服务实现
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Service
@RequiredArgsConstructor
public class ProjectCategoryServiceImpl extends ServiceImpl<ProjectCategoryMapper, ProjectCategory> implements ProjectCategoryService {

    private final ProjectCategoryMapper projectCategoryMapper;

    @Override
    public List<Map<String, String>> getProjectCategoryOptions() {
        return projectCategoryMapper.getProjectCategoryOptions();
    }

    @Override
    public PageResult<ProjectCategory> getProjectCategoryPage(ProjectCategoryQueryDTO queryDTO) {
        Page<ProjectCategory> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());

        LambdaQueryWrapper<ProjectCategory> queryWrapper = new LambdaQueryWrapper<>();

        // 关键词搜索（分类名称或代码）
        if (StringUtils.hasText(queryDTO.getKeyword())) {
            queryWrapper.and(wrapper -> wrapper
                .like(ProjectCategory::getName, queryDTO.getKeyword())
                .or()
                .like(ProjectCategory::getCode, queryDTO.getKeyword())
            );
        }

        // 启用状态过滤
        if (queryDTO.getEnabled() != null) {
            queryWrapper.eq(ProjectCategory::getEnabled, queryDTO.getEnabled());
        }

        // 排序
        queryWrapper.orderByAsc(ProjectCategory::getSortOrder)
                   .orderByAsc(ProjectCategory::getId);

        IPage<ProjectCategory> result = this.page(page, queryWrapper);

        return PageResult.from(result);
    }

    @Override
    public ProjectCategory createProjectCategory(CreateProjectCategoryDTO createProjectCategoryDTO) {
        // 检查分类代码是否已存在
        LambdaQueryWrapper<ProjectCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectCategory::getCode, createProjectCategoryDTO.getCode());

        if (this.count(queryWrapper) > 0) {
            throw new IllegalArgumentException("分类代码已存在");
        }

        ProjectCategory projectCategory = new ProjectCategory();
        BeanUtils.copyProperties(createProjectCategoryDTO, projectCategory);

        this.save(projectCategory);
        return projectCategory;
    }

    @Override
    public ProjectCategory updateProjectCategory(UpdateProjectCategoryDTO updateProjectCategoryDTO) {
        ProjectCategory existingCategory = this.getById(updateProjectCategoryDTO.getId());
        if (existingCategory == null) {
            throw new IllegalArgumentException("项目分类不存在");
        }

        // 检查分类代码是否已存在（排除当前记录）
        LambdaQueryWrapper<ProjectCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectCategory::getCode, updateProjectCategoryDTO.getCode())
                   .ne(ProjectCategory::getId, updateProjectCategoryDTO.getId());

        if (this.count(queryWrapper) > 0) {
            throw new IllegalArgumentException("分类代码已存在");
        }

        BeanUtils.copyProperties(updateProjectCategoryDTO, existingCategory);
        this.updateById(existingCategory);
        return existingCategory;
    }

    @Override
    public void deleteProjectCategory(Long id) {
        ProjectCategory projectCategory = this.getById(id);
        if (projectCategory == null) {
            throw new IllegalArgumentException("项目分类不存在");
        }

        this.removeById(id);
    }
}
