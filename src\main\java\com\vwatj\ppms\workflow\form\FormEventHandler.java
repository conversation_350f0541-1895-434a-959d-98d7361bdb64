package com.vwatj.ppms.workflow.form;

import java.util.Map;

/**
 * 表单事件处理器接口
 * 用于处理不同表单的提交和编辑事件
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface FormEventHandler {

    /**
     * 获取支持的表单Key
     *
     * @return 表单Key
     */
    String getSupportedFormKey();

    /**
     * 处理表单提交事件
     *
     * @param projectId 项目ID
     * @param stageKey 阶段Key
     * @param formKey 表单Key
     * @param formData 表单数据
     * @param operation 操作类型
     */
    void handleFormSubmit(Long projectId, String stageKey, String formKey, Map<String, Object> formData, String operation);

    /**
     * 处理表单编辑事件
     *
     * @param projectId 项目ID
     * @param stageKey 阶段Key
     * @param formKey 表单Key
     * @param formData 表单数据
     */
    void handleFormEdit(Long projectId, String stageKey, String formKey, Map<String, Object> formData);

    /**
     * 处理表单更新事件
     * 与编辑事件不同，更新事件专门用于处理表单数据的持久化更新
     *
     * @param projectId 项目ID
     * @param stageKey 阶段Key
     * @param formKey 表单Key
     * @param formData 新的表单数据
     * @param previousFormData 之前的表单数据，用于对比变化
     */
    default void handleFormUpdate(Long projectId, String stageKey, String formKey,
                                  Map<String, Object> formData, Map<String, Object> previousFormData) {
        // 默认实现：调用编辑事件处理
        handleFormEdit(projectId, stageKey, formKey, formData);
    }

    /**
     * 表单提交前的验证
     *
     * @param projectId 项目ID
     * @param stageKey 阶段Key
     * @param formKey 表单Key
     * @param formData 表单数据
     * @param operation 操作类型
     * @throws RuntimeException 验证失败时抛出异常
     */
    default void validateFormData(Long projectId, String stageKey, String formKey, Map<String, Object> formData, String operation) {
        // 默认不做验证，子类可以重写
    }

    /**
     * 表单提交后的后置处理
     *
     * @param projectId 项目ID
     * @param stageKey 阶段Key
     * @param formKey 表单Key
     * @param formData 表单数据
     * @param operation 操作类型
     */
    default void afterFormSubmit(Long projectId, String stageKey, String formKey, Map<String, Object> formData, String operation) {
        // 默认不做处理，子类可以重写
    }
}
