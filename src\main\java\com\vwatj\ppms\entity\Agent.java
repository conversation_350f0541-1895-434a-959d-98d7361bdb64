package com.vwatj.ppms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.vwatj.ppms.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Agent实体类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("agent")
public class Agent extends BaseEntity {
    
    /**
     * Agent ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * Agent名称
     */
    private String name;
    
    /**
     * Agent描述
     */
    private String description;
    
    /**
     * Agent状态 (0-禁用, 1-启用)
     */
    private Integer status;
    
    /**
     * Agent类型
     */
    private String type;
    
    /**
     * Agent配置信息 (JSON格式)
     */
    private String config;
    
    /**
     * 创建者ID
     */
    private Long creatorId;
    
    /**
     * 创建者名称
     */
    private String creatorName;
}
