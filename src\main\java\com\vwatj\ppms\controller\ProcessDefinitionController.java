package com.vwatj.ppms.controller;

import com.vwatj.ppms.common.ApiResponse;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateProcessDefinitionDTO;
import com.vwatj.ppms.dto.ProcessDefinitionQueryDTO;
import com.vwatj.ppms.dto.PublishProcessDTO;
import com.vwatj.ppms.entity.ProcessDefinition;
import com.vwatj.ppms.service.ProcessDefinitionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 流程定义控制器
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Slf4j
@RestController
@RequestMapping("/process-definitions")
@RequiredArgsConstructor
public class ProcessDefinitionController {

    private final ProcessDefinitionService processDefinitionService;

    /**
     * 分页查询流程定义
     */
    @GetMapping
    public ApiResponse<PageResult<ProcessDefinition>> getProcessDefinitions(ProcessDefinitionQueryDTO queryDTO) {
        PageResult<ProcessDefinition> result = processDefinitionService.getProcessDefinitionPage(queryDTO);
        return ApiResponse.success(result);
    }

    /**
     * 根据ID查询流程定义
     */
    @GetMapping("/{id}")
    public ApiResponse<ProcessDefinition> getProcessDefinition(@PathVariable Long id) {
        ProcessDefinition processDefinition = processDefinitionService.getById(id);
        if (processDefinition == null) {
            return ApiResponse.notFound("流程定义不存在");
        }
        return ApiResponse.success(processDefinition);
    }

    /**
     * 创建流程定义
     */
    @PostMapping
    public ApiResponse<ProcessDefinition> createProcessDefinition(@Validated @ModelAttribute CreateProcessDefinitionDTO createDTO) {
        try {
            log.info("接收到创建流程定义请求: processKey={}, name={}, bpmnFile={}",
                    createDTO.getProcessKey(), createDTO.getName(),
                    createDTO.getBpmnFile() != null ? createDTO.getBpmnFile().getOriginalFilename() : "null");

            ProcessDefinition processDefinition = processDefinitionService.createProcessDefinition(createDTO);
            return ApiResponse.success("流程定义创建成功", processDefinition);
        } catch (Exception e) {
            log.error("创建流程定义失败", e);
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 更新流程定义基本信息
     */
    @PutMapping("/{id}")
    public ApiResponse<ProcessDefinition> updateProcessDefinition(
            @PathVariable Long id,
            @RequestParam String name,
            @RequestParam(required = false) String description) {
        try {
            ProcessDefinition processDefinition = processDefinitionService.updateProcessDefinition(id, name, description);
            return ApiResponse.success("流程定义更新成功", processDefinition);
        } catch (Exception e) {
            log.error("更新流程定义失败", e);
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 发布流程（创建新版本并发布）
     */
    @PostMapping("/{id}/publish")
    public ApiResponse<String> publishProcess(@PathVariable Long id, @Validated @ModelAttribute PublishProcessDTO publishDTO) {
        try {
            log.info("接收到发布流程请求: processDefinitionId={}, description={}, bpmnFile={}",
                    id, publishDTO.getDescription(),
                    publishDTO.getBpmnFile() != null ? publishDTO.getBpmnFile().getOriginalFilename() : "null");

            // 设置流程定义ID（从路径参数获取）
            publishDTO.setProcessDefinitionId(id);

            // 手动验证必要参数
            if (publishDTO.getProcessDefinitionId() == null) {
                return ApiResponse.badRequest("流程定义ID不能为空");
            }
            if (publishDTO.getBpmnFile() == null || publishDTO.getBpmnFile().isEmpty()) {
                return ApiResponse.badRequest("BPMN文件不能为空");
            }

            processDefinitionService.publishProcessWithNewVersion(publishDTO);
            return ApiResponse.success("流程发布成功");
        } catch (Exception e) {
            log.error("发布流程失败", e);
            return ApiResponse.badRequest(e.getMessage());
        }
    }



    /**
     * 根据流程Key查询
     */
    @GetMapping("/by-key/{processKey}")
    public ApiResponse<ProcessDefinition> getByProcessKey(@PathVariable String processKey) {
        ProcessDefinition processDefinition = processDefinitionService.getByProcessKey(processKey);
        if (processDefinition == null) {
            return ApiResponse.notFound("流程定义不存在");
        }
        return ApiResponse.success(processDefinition);
    }
}