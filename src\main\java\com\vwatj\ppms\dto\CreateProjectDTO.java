package com.vwatj.ppms.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.vwatj.ppms.config.TimestampToLocalDateTimeDeserializer;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 创建项目DTO
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
public class CreateProjectDTO {
    
    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不能为空")
    private String name;
    
    /**
     * 项目描述
     */
    private String description;
    
    /**
     * 项目封面
     */
    private String cover;
    
    /**
     * 项目版本
     */
    private String version;
    
    /**
     * 项目经理
     */
    @NotBlank(message = "项目经理不能为空")
    private String manager;
    
    /**
     * 项目类型
     */
    @NotBlank(message = "项目类型不能为空")
    private String category;
    
    /**
     * 项目优先级
     */
    @NotBlank(message = "项目优先级不能为空")
    private String priority;
    
    /**
     * 关联系统资产ID
     */
    @NotNull(message = "关联系统资产ID不能为空")
    private Long relatedAssetId;
    
    /**
     * 系统资产版本号
     */
    private String assetVersion;
    
    /**
     * 开始时间
     */
    @JsonDeserialize(using = TimestampToLocalDateTimeDeserializer.class)
    private LocalDateTime startDate;

    /**
     * 结束时间
     */
    @JsonDeserialize(using = TimestampToLocalDateTimeDeserializer.class)
    private LocalDateTime endDate;
    
    /**
     * 团队成员ID列表
     */
    private List<Long> teamMembers;
    
    /**
     * 标签列表
     */
    private List<String> tags;
    
    /**
     * 扩展字段
     */
    private Map<String, Object> customFields;
}
