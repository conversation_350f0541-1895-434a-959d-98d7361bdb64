<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vwatj.ppms.mapper.ProjectMapper">

    <!-- 分页查询项目 -->
    <select id="selectProjectPage" resultType="com.vwatj.ppms.entity.Project">
        SELECT * FROM project
        <where>
            <if test="keyword != null and keyword != ''">
                AND (name LIKE CONCAT('%', #{keyword}, '%') 
                OR description LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="category != null and category != ''">
                AND category = #{category}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="priority != null and priority != ''">
                AND priority = #{priority}
            </if>
            <if test="manager != null and manager != ''">
                AND manager = #{manager}
            </if>
            <if test="starred != null">
                AND starred = #{starred}
            </if>
            <if test="archived != null">
                AND archived = #{archived}
            </if>
            <if test="tags != null and tags.size() &gt; 0">
                AND JSON_OVERLAPS(tags, #{tags})
            </if>
            <if test="startDate != null">
                AND start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND end_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 根据项目名称查询项目 -->
    <select id="selectByName" resultType="com.vwatj.ppms.entity.Project">
        SELECT * FROM project WHERE name = #{name}
    </select>

    <!-- 查询用户关注的项目 -->
    <select id="selectStarredProjects" resultType="com.vwatj.ppms.entity.Project">
        SELECT * FROM project 
        WHERE starred = true 
        AND (manager = #{userId} OR JSON_CONTAINS(team_members, JSON_QUOTE(#{userId})))
        ORDER BY updated_at DESC
    </select>

    <!-- 查询用户参与的项目 -->
    <select id="selectUserProjects" resultType="com.vwatj.ppms.entity.Project">
        SELECT * FROM project 
        WHERE manager = #{userId} OR JSON_CONTAINS(team_members, JSON_QUOTE(#{userId}))
        ORDER BY updated_at DESC
    </select>

    <!-- 统计项目数量按状态 -->
    <select id="countProjectsByStatus" resultType="map">
        SELECT status, COUNT(*) as count FROM project GROUP BY status
    </select>

    <!-- 统计项目数量按分类 -->
    <select id="countProjectsByCategory" resultType="map">
        SELECT category, COUNT(*) as count FROM project GROUP BY category
    </select>

    <!-- 统计项目数量按优先级 -->
    <select id="countProjectsByPriority" resultType="map">
        SELECT priority, COUNT(*) as count FROM project GROUP BY priority
    </select>

</mapper>
