package com.vwatj.ppms.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.vwatj.ppms.entity.ProcessForm;

import java.util.List;
import java.util.Map;

/**
 * 流程表单服务接口
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
public interface ProcessFormService extends IService<ProcessForm> {

    /**
     * 根据表单Key获取表单信息
     *
     * @param formKey 表单Key
     * @return 表单信息
     */
    ProcessForm getByFormKey(String formKey);

    /**
     * 根据表单Key列表批量获取表单信息
     *
     * @param formKeys 表单Key列表
     * @return 表单信息列表
     */
    List<ProcessForm> getByFormKeys(List<String> formKeys);

    /**
     * 获取所有启用的表单模板
     *
     * @return 表单模板列表
     */
    List<ProcessForm> getAllEnabled();

    /**
     * 获取表单结构模板
     *
     * @param formKey 表单Key
     * @return 表单结构JSON字符串
     */
    JSONObject getFormStruct(String formKey);

    /**
     * 检查表单模板是否存在
     *
     * @param formKey 表单Key
     * @return 是否存在
     */
    boolean existsByFormKey(String formKey);
}
