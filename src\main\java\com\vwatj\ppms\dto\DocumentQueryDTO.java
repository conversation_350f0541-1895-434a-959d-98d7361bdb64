package com.vwatj.ppms.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文档查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
public class DocumentQueryDTO {
    
    /**
     * 页码
     */
    private Long page = 1L;
    
    /**
     * 每页大小
     */
    private Long pageSize = 10L;
    
    /**
     * 排序字段
     */
    private String sortBy;
    
    /**
     * 排序方向
     */
    private String sortOrder;
    
    /**
     * 关键词
     */
    private String keyword;
    
    /**
     * 项目ID
     */
    private Long projectId;
    
    /**
     * 文档类别
     */
    private String category;
    
    /**
     * 文档状态
     */
    private String status;
    
    /**
     * 文件类型
     */
    private String fileType;
    
    /**
     * 作者
     */
    private String author;
    
    /**
     * 审核人
     */
    private String reviewer;
    
    /**
     * 批准人
     */
    private String approver;
    
    /**
     * 标签列表
     */
    private List<String> tags;
    
    /**
     * 上传开始时间
     */
    private LocalDateTime uploadDateStart;
    
    /**
     * 上传结束时间
     */
    private LocalDateTime uploadDateEnd;
}
