package com.vwatj.ppms.dto;

import lombok.Data;

/**
 * 流程定义查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
public class ProcessDefinitionQueryDTO {

    /**
     * 页码
     */
    private Long page = 1L;

    /**
     * 每页大小
     */
    private Long pageSize = 10L;

    /**
     * 排序字段
     */
    private String sortBy;

    /**
     * 排序方向
     */
    private String sortOrder;

    /**
     * 流程名称（模糊查询）
     */
    private String name;

    /**
     * 流程Key（模糊查询）
     */
    private String processKey;

    /**
     * 流程状态
     */
    private String status;

    /**
     * 创建人
     */
    private String createdBy;
}
