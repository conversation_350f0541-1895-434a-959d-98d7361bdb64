-- Agent表建表语句
CREATE TABLE `agent` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'Agent ID',
  `name` varchar(50) NOT NULL COMMENT 'Agent名称',
  `description` varchar(200) DEFAULT NULL COMMENT 'Agent描述',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT 'Agent状态 (0-禁用, 1-启用)',
  `type` varchar(50) DEFAULT NULL COMMENT 'Agent类型',
  `config` text COMMENT 'Agent配置信息 (JSON格式)',
  `creator_id` bigint DEFAULT NULL COMMENT '创建者ID',
  `creator_name` varchar(50) DEFAULT NULL COMMENT '创建者名称',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agent_name` (`name`),
  KEY `idx_agent_status` (`status`),
  KEY `idx_agent_type` (`type`),
  KEY `idx_agent_creator` (`creator_id`),
  KEY `idx_agent_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Agent表';

-- 插入示例数据
INSERT INTO `agent` (`name`, `description`, `status`, `type`, `creator_id`, `creator_name`) VALUES
('智能客服Agent', '用于处理客户咨询的智能Agent', 1, 'customer_service', 1, '系统管理员'),
('代码审查Agent', '自动进行代码审查和质量检测', 1, 'code_review', 1, '系统管理员'),
('文档生成Agent', '根据代码自动生成技术文档', 0, 'documentation', 1, '系统管理员'),
('测试用例Agent', '自动生成测试用例和执行测试', 1, 'testing', 1, '系统管理员'),
('数据分析Agent', '进行数据分析和报告生成', 1, 'data_analysis', 1, '系统管理员');
