package com.vwatj.ppms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateProcessDefinitionDTO;
import com.vwatj.ppms.dto.ProcessDefinitionQueryDTO;
import com.vwatj.ppms.dto.PublishProcessDTO;
import com.vwatj.ppms.entity.ProcessDefinition;

/**
 * 流程定义服务接口
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
public interface ProcessDefinitionService extends IService<ProcessDefinition> {

    /**
     * 分页查询流程定义
     */
    PageResult<ProcessDefinition> getProcessDefinitionPage(ProcessDefinitionQueryDTO queryDTO);

    /**
     * 创建流程定义（包含第一个版本）
     */
    ProcessDefinition createProcessDefinition(CreateProcessDefinitionDTO createDTO);

    /**
     * 更新流程定义基本信息
     */
    ProcessDefinition updateProcessDefinition(Long id, String name, String description);

    /**
     * 发布流程（创建新版本并发布到Camunda）
     */
    void publishProcessWithNewVersion(PublishProcessDTO publishDTO);

    /**
     * 根据流程Key查询
     */
    ProcessDefinition getByProcessKey(String processKey);
}
