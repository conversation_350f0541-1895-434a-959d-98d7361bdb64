package com.vwatj.ppms.workflow.delegate;

import com.vwatj.ppms.entity.Project;
import com.vwatj.ppms.service.ProjectService;
import com.vwatj.ppms.service.ProjectProcessService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.Expression;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

/**
 * 项目边界事件委托类
 * 用于监听项目工作流边界事件并自动更新项目阶段状态
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Slf4j
@Component("projectBoundaryEventDelegate")
public class ProjectBoundaryEventDelegate implements JavaDelegate {

    @Resource
    private ProjectService projectService;

    @Resource
    private ProjectProcessService projectProcessService;

    private Expression targetStatus;

    @Override
    public void execute(DelegateExecution execution) throws Exception {
        try {
            log.info("项目边界事件触发: activityId={}, eventName={}, processInstanceId={}",
                    execution.getCurrentActivityId(),
                    execution.getEventName(),
                    execution.getProcessInstanceId());

            // 获取流程变量中的项目ID
            Object projectIdObj = execution.getVariable("projectId");
            if (projectIdObj == null) {
                log.warn("流程变量中未找到projectId: processInstanceId={}", execution.getProcessInstanceId());
                return;
            }

            Long projectId = Long.valueOf(projectIdObj.toString());

            // 获取当前项目
            Project project = projectService.getById(projectId);
            if (project == null) {
                log.warn("未找到项目: projectId={}", projectId);
                return;
            }

            // 获取目标阶段状态
            String targetStageKey = getTargetStageKey(execution);
            if (targetStageKey != null) {
                log.info("项目边界事件激活阶段: projectId={}, stageKey={}", projectId, targetStageKey);
                
                // 激活对应的项目阶段
                projectProcessService.activateStage(projectId, targetStageKey);

                log.info("项目阶段激活成功: projectId={}, stageKey={}", projectId, targetStageKey);
            }

        } catch (Exception e) {
            log.error("项目边界事件处理失败: processInstanceId={}",
                    execution.getProcessInstanceId(), e);
            // 不抛出异常，避免影响工作流执行
        }
    }

    /**
     * 获取目标阶段Key
     */
    private String getTargetStageKey(DelegateExecution execution) {
        try {
            if (targetStatus != null) {
                Object value = targetStatus.getValue(execution);
                return value != null ? value.toString() : null;
            }
        } catch (Exception e) {
            log.warn("获取目标阶段Key失败", e);
        }
        return null;
    }

    /**
     * 设置目标状态表达式（由Camunda注入）
     */
    public void setTargetStatus(Expression targetStatus) {
        this.targetStatus = targetStatus;
    }
}
