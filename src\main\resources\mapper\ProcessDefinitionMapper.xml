<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vwatj.ppms.mapper.ProcessDefinitionMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.vwatj.ppms.entity.ProcessDefinition">
        <id column="id" property="id" />
        <result column="process_key" property="processKey" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="published_version" property="publishedVersion" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, process_key, name, description, published_version, status,
        created_by, created_time, updated_by, updated_time
    </sql>

    <!-- 分页查询流程定义 -->
    <select id="selectProcessDefinitionPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM process_definition
        <where>
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="processKey != null and processKey != ''">
            AND process_key LIKE CONCAT('%', #{processKey}, '%')
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="createdBy != null and createdBy != ''">
            AND created_by = #{createdBy}
        </if>
        </where>
        ORDER BY created_time DESC
    </select>

    <!-- 根据流程Key查询 -->
    <select id="selectByProcessKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM process_definition
        WHERE process_key = #{processKey}
    </select>

</mapper>
