package com.vwatj.ppms.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 创建系统字典DTO
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Data
public class CreateSystemDictDTO {
    
    /**
     * 字典类型
     */
    @NotBlank(message = "字典类型不能为空")
    private String dictType;
    
    /**
     * 字典代码
     */
    @NotBlank(message = "字典代码不能为空")
    private String dictCode;
    
    /**
     * 字典名称
     */
    @NotBlank(message = "字典名称不能为空")
    private String dictName;
    
    /**
     * 字典描述
     */
    private String description;
    
    /**
     * 排序号
     */
    private Integer sortOrder = 0;
    
    /**
     * 是否启用
     */
    @NotNull(message = "启用状态不能为空")
    private Boolean enabled = true;
    
    /**
     * 父级ID
     */
    private Long parentId;
    
    /**
     * 扩展属性
     */
    private String extraProps;
}
