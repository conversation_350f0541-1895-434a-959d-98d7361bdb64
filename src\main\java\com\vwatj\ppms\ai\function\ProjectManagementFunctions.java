package com.vwatj.ppms.ai.function;

import com.vwatj.ppms.dto.CreateProjectDTO;
import com.vwatj.ppms.dto.ProjectQueryDTO;
import com.vwatj.ppms.dto.UpdateProjectDTO;
import com.vwatj.ppms.entity.Project;
import com.vwatj.ppms.service.ProjectService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 项目管理相关的AI功能函数
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ProjectManagementFunctions {

    private final ProjectService projectService;

    /**
     * 查询项目列表
     */
    public Function<ProjectQueryRequest, String> queryProjects() {
        return request -> {
            try {
                ProjectQueryDTO queryDTO = new ProjectQueryDTO();
                queryDTO.setName(request.getName());
                queryDTO.setStatus(request.getStatus());
                queryDTO.setManager(request.getManager());
                queryDTO.setCategory(request.getCategory());
                queryDTO.setPriority(request.getPriority());
                queryDTO.setPageNum(request.getPageNum() != null ? request.getPageNum() : 1);
                queryDTO.setPageSize(request.getPageSize() != null ? request.getPageSize() : 10);

                var result = projectService.getProjectPage(queryDTO);
                
                StringBuilder response = new StringBuilder();
                response.append(String.format("找到 %d 个项目（共 %d 个）：\n", 
                    result.getRecords().size(), result.getTotal()));
                
                for (Project project : result.getRecords()) {
                    response.append(String.format("- %s (ID: %d, 状态: %s, 经理: %s, 优先级: %s)\n",
                        project.getName(), project.getId(), project.getStatus(), 
                        project.getManager(), project.getPriority()));
                }
                
                return response.toString();
            } catch (Exception e) {
                log.error("查询项目失败", e);
                return "查询项目时发生错误: " + e.getMessage();
            }
        };
    }

    /**
     * 获取项目详情
     */
    public Function<Long, String> getProjectDetails() {
        return projectId -> {
            try {
                Project project = projectService.getById(projectId);
                if (project == null) {
                    return "项目不存在 (ID: " + projectId + ")";
                }
                
                return String.format("""
                    项目详情：
                    - 名称: %s
                    - 描述: %s
                    - 状态: %s
                    - 经理: %s
                    - 类型: %s
                    - 优先级: %s
                    - 开始时间: %s
                    - 结束时间: %s
                    - 团队成员: %s
                    - 总任务数: %d
                    - 已完成任务: %d
                    - 文档数量: %d
                    """,
                    project.getName(),
                    project.getDescription(),
                    project.getStatus(),
                    project.getManager(),
                    project.getCategory(),
                    project.getPriority(),
                    project.getStartDate(),
                    project.getEndDate(),
                    project.getTeamMembers(),
                    project.getTotalTasks(),
                    project.getCompletedTasks(),
                    project.getTotalDocuments()
                );
            } catch (Exception e) {
                log.error("获取项目详情失败", e);
                return "获取项目详情时发生错误: " + e.getMessage();
            }
        };
    }

    /**
     * 创建项目
     */
    public Function<CreateProjectRequest, String> createProject() {
        return request -> {
            try {
                CreateProjectDTO createDTO = new CreateProjectDTO();
                createDTO.setName(request.getName());
                createDTO.setDescription(request.getDescription());
                createDTO.setManager(request.getManager());
                createDTO.setCategory(request.getCategory());
                createDTO.setPriority(request.getPriority());
                createDTO.setRelatedAssetId(request.getRelatedAssetId());
                createDTO.setStartDate(request.getStartDate());
                createDTO.setEndDate(request.getEndDate());
                createDTO.setTeamMembers(request.getTeamMembers());
                createDTO.setTags(request.getTags());

                Project project = projectService.createProject(createDTO);
                
                return String.format("项目创建成功！\n项目ID: %d\n项目名称: %s\n状态: %s",
                    project.getId(), project.getName(), project.getStatus());
            } catch (Exception e) {
                log.error("创建项目失败", e);
                return "创建项目时发生错误: " + e.getMessage();
            }
        };
    }

    /**
     * 更新项目状态
     */
    public Function<UpdateProjectStatusRequest, String> updateProjectStatus() {
        return request -> {
            try {
                projectService.updateStatus(request.getProjectId(), request.getStatus());
                return String.format("项目状态更新成功！项目ID: %d, 新状态: %s", 
                    request.getProjectId(), request.getStatus());
            } catch (Exception e) {
                log.error("更新项目状态失败", e);
                return "更新项目状态时发生错误: " + e.getMessage();
            }
        };
    }

    /**
     * 获取项目统计信息
     */
    public Function<Void, String> getProjectStats() {
        return unused -> {
            try {
                Map<String, Object> stats = projectService.getProjectStats();
                
                StringBuilder response = new StringBuilder("项目统计信息：\n");
                stats.forEach((key, value) -> 
                    response.append(String.format("- %s: %s\n", key, value)));
                
                return response.toString();
            } catch (Exception e) {
                log.error("获取项目统计失败", e);
                return "获取项目统计时发生错误: " + e.getMessage();
            }
        };
    }

    // 请求类定义
    public static class ProjectQueryRequest {
        public String name;
        public String status;
        public String manager;
        public String category;
        public String priority;
        public Integer pageNum;
        public Integer pageSize;
        
        // getters
        public String getName() { return name; }
        public String getStatus() { return status; }
        public String getManager() { return manager; }
        public String getCategory() { return category; }
        public String getPriority() { return priority; }
        public Integer getPageNum() { return pageNum; }
        public Integer getPageSize() { return pageSize; }
    }

    public static class CreateProjectRequest {
        public String name;
        public String description;
        public String manager;
        public String category;
        public String priority;
        public Long relatedAssetId;
        public Long startDate;
        public Long endDate;
        public List<String> teamMembers;
        public List<String> tags;
        
        // getters
        public String getName() { return name; }
        public String getDescription() { return description; }
        public String getManager() { return manager; }
        public String getCategory() { return category; }
        public String getPriority() { return priority; }
        public Long getRelatedAssetId() { return relatedAssetId; }
        public Long getStartDate() { return startDate; }
        public Long getEndDate() { return endDate; }
        public List<String> getTeamMembers() { return teamMembers; }
        public List<String> getTags() { return tags; }
    }

    public static class UpdateProjectStatusRequest {
        public Long projectId;
        public String status;
        
        // getters
        public Long getProjectId() { return projectId; }
        public String getStatus() { return status; }
    }
}
