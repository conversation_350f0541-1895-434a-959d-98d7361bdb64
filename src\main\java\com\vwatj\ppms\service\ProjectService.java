package com.vwatj.ppms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateProjectDTO;
import com.vwatj.ppms.dto.ProjectQueryDTO;
import com.vwatj.ppms.dto.UpdateProjectDTO;
import com.vwatj.ppms.entity.Project;

import java.util.List;
import java.util.Map;

/**
 * 项目服务接口
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface ProjectService extends IService<Project> {
    
    /**
     * 分页查询项目
     */
    PageResult<Project> getProjectPage(ProjectQueryDTO queryDTO);
    
    /**
     * 根据项目名称查询项目
     */
    Project getProjectByName(String name);
    
    /**
     * 创建项目
     */
    Project createProject(CreateProjectDTO createProjectDTO);
    
    /**
     * 更新项目
     */
    Project updateProject(UpdateProjectDTO updateProjectDTO);
    
    /**
     * 删除项目
     */
    void deleteProject(Long id);
    
    /**
     * 切换项目关注状态
     */
    boolean toggleStar(Long id);
    
    /**
     * 归档/取消归档项目
     */
    boolean toggleArchive(Long id);
    
    /**
     * 更新项目状态
     */
    void updateStatus(Long id, String status);
    
    /**
     * 获取项目统计信息
     */
    Map<String, Object> getProjectStats();
    
    /**
     * 获取项目成员列表
     */
    List<String> getProjectMembers(Long id);
    
    /**
     * 更新项目成员
     */
    void updateProjectMembers(Long id, List<String> members);
    
    /**
     * 复制项目
     */
    Project duplicateProject(Long id, String name);
    
    /**
     * 获取用户关注的项目
     */
    List<Project> getStarredProjects(String userId);
    
    /**
     * 获取用户参与的项目
     */
    List<Project> getUserProjects(String userId);

    /**
     * 获取项目详情概览信息
     */
    Map<String, Object> getProjectOverview(Long id);

    /**
     * 获取项目任务统计
     */
    Map<String, Object> getProjectTaskStats(Long id);

    /**
     * 获取项目任务列表
     */
    List<Map<String, Object>> getProjectTasks(Long id);

    /**
     * 获取项目文档列表
     */
    List<Map<String, Object>> getProjectDocuments(Long id);
}
