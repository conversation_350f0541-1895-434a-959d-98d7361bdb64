package com.vwatj.ppms.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Jackson 配置类
 * 用于处理 JSON 序列化和反序列化
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Configuration
public class JacksonConfig {

    /**
     * 日期时间格式
     */
    private static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 配置 ObjectMapper
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        // 创建自定义模块，支持时间戳和字符串格式的LocalDateTime反序列化和序列化
        SimpleModule customModule = new SimpleModule();
        customModule.addDeserializer(LocalDateTime.class, new TimestampToLocalDateTimeDeserializer());
        customModule.addSerializer(LocalDateTime.class, new LocalDateTimeToTimestampSerializer());

        return Jackson2ObjectMapperBuilder.json()
                .modules(new JavaTimeModule(), customModule)
                .featuresToDisable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .simpleDateFormat(DATE_TIME_FORMAT)
                .build();
    }
}
