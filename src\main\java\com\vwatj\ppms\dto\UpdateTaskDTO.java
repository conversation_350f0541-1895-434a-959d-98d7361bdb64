package com.vwatj.ppms.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.vwatj.ppms.config.TimestampToLocalDateTimeDeserializer;
import com.vwatj.ppms.enums.TaskStatusEnum;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 更新任务DTO
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
public class UpdateTaskDTO {
    
    /**
     * 任务ID
     */
    private Long id;

    /**
     * 任务编号
     */
    private String taskNo;

    /**
     * 任务标题
     */
    private String title;
    
    /**
     * 任务描述
     */
    private String description;
    
    /**
     * 问题类型
     */
    private String issueType;
    
    /**
     * 指派人
     */
    private String assignee;
    
    /**
     * 报告人
     */
    private String reporter;
    
    /**
     * 任务状态
     */
    private TaskStatusEnum status;
    
    /**
     * 任务优先级
     */
    private String priority;
    
    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 阶段名称
     */
    private String stageName;

    /**
     * 计划开始时间
     */
    @JsonDeserialize(using = TimestampToLocalDateTimeDeserializer.class)
    private LocalDateTime plannedStartTime;

    /**
     * 计划结束时间
     */
    @JsonDeserialize(using = TimestampToLocalDateTimeDeserializer.class)
    private LocalDateTime plannedEndTime;

    /**
     * 实际开始时间
     */
    @JsonDeserialize(using = TimestampToLocalDateTimeDeserializer.class)
    private LocalDateTime actualStartTime;

    /**
     * 实际结束时间
     */
    @JsonDeserialize(using = TimestampToLocalDateTimeDeserializer.class)
    private LocalDateTime actualEndTime;
    
    /**
     * 时长(小时)
     */
    private BigDecimal duration;
}
