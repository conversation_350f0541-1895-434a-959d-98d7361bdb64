package com.vwatj.ppms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.vwatj.ppms.entity.SystemDict;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 系统字典Mapper
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Mapper
public interface SystemDictMapper extends BaseMapper<SystemDict> {

    /**
     * 根据字典类型获取字典选项列表
     * @param dictType 字典类型
     * @return 选项列表
     */
    @Select("SELECT dict_code as value, dict_name as label FROM system_dict WHERE dict_type = #{dictType} AND enabled = 1 AND deleted = 0 ORDER BY sort_order ASC, id ASC")
    List<Map<String, String>> getDictOptionsByType(@Param("dictType") String dictType);

    /**
     * 获取所有字典类型
     * @return 字典类型列表
     */
    @Select("SELECT DISTINCT dict_type FROM system_dict WHERE enabled = 1 AND deleted = 0 ORDER BY dict_type")
    List<String> getAllDictTypes();

    /**
     * 根据字典类型和代码获取字典项
     * @param dictType 字典类型
     * @param dictCode 字典代码
     * @return 字典项
     */
    @Select("SELECT * FROM system_dict WHERE dict_type = #{dictType} AND dict_code = #{dictCode} AND enabled = 1 AND deleted = 0")
    SystemDict getDictByTypeAndCode(@Param("dictType") String dictType, @Param("dictCode") String dictCode);
}
