package com.vwatj.ppms.workflow.delegate;

import com.vwatj.ppms.entity.Project;
import com.vwatj.ppms.service.ProjectService;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 项目上线邮件发送任务委托类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Slf4j
@Component("projectGoLiveSendTaskDelegate")
public class ProjectGoLiveSendTaskDelegate implements JavaDelegate {

    @Autowired
    private ProjectService projectService;

    @Override
    public void execute(DelegateExecution execution) throws Exception {
        try {
            // 获取项目信息
            Long projectId = getProjectId(execution);
            if (projectId == null) {
                log.warn("项目ID为空，跳过上线邮件发送");
                return;
            }

            Project project = projectService.getById(projectId);
            if (project == null) {
                log.warn("项目不存在: projectId={}", projectId);
                return;
            }

            log.info("发送项目上线邮件: projectId={}, projectName={}", projectId, project.getName());

            // TODO: 实现邮件发送逻辑
            sendGoLiveMail(project);

            log.info("项目上线邮件发送成功: projectId={}", projectId);

        } catch (Exception e) {
            log.error("发送项目上线邮件失败: processInstanceId={}", execution.getProcessInstanceId(), e);
            throw e; // 抛出异常，让工作流处理
        }
    }

    /**
     * 发送上线邮件
     */
    private void sendGoLiveMail(Project project) {
        // TODO: 实现具体的邮件发送逻辑
        log.info("模拟发送上线邮件: projectId={}, projectName={}", project.getId(), project.getName());
        
        // 邮件内容示例：
        // 主题：项目上线通知 - {projectName}
        // 内容：
        // 尊敬的项目相关人员：
        // 
        // 项目 {projectName} 已成功上线，请知悉。
        // 
        // 项目详情：
        // - 项目ID: {projectId}
        // - 项目名称: {projectName}
        // - 项目代码: {projectCode}
        // - 上线时间: {当前时间}
        // 
        // 如有任何问题，请及时联系项目团队。
        // 
        // 此致
        // 项目管理系统
    }

    /**
     * 获取项目ID
     */
    private Long getProjectId(DelegateExecution execution) {
        try {
            Object projectIdObj = execution.getVariable("projectId");
            if (projectIdObj instanceof Long) {
                return (Long) projectIdObj;
            } else if (projectIdObj instanceof String) {
                return Long.parseLong((String) projectIdObj);
            } else if (projectIdObj instanceof Integer) {
                return ((Integer) projectIdObj).longValue();
            }
        } catch (Exception e) {
            log.warn("获取项目ID失败", e);
        }
        return null;
    }
}
