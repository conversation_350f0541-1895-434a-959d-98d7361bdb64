package com.vwatj.ppms.excel.generate;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.vwatj.ppms.dto.SystemAssetQueryDTO;
import com.vwatj.ppms.entity.SystemAsset;
import com.vwatj.ppms.excel.converter.ExcelDataConverter;
import com.vwatj.ppms.excel.converter.impl.SystemAssetExcelConverter;
import com.vwatj.ppms.excel.core.AbstractExcelGenerate;
import com.vwatj.ppms.excel.core.ExcelImportResult;
import com.vwatj.ppms.service.SystemAssetService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统资产Excel服务
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public class SystemAssetExcelGenerate extends AbstractExcelGenerate<SystemAsset> {

    private final SystemAssetService systemAssetService;

    public SystemAssetExcelGenerate(SystemAssetService systemAssetService) {
        this.systemAssetService = systemAssetService;
    }

    @Override
    protected ExcelDataConverter<SystemAsset> getDataConverter() {
        return new SystemAssetExcelConverter();
    }

    @Override
    protected SystemAsset findByUniqueField(Object uniqueValue) {
        QueryWrapper<SystemAsset> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("asset_no", uniqueValue);
        return systemAssetService.getOne(queryWrapper);
    }

    @Override
    protected SystemAsset saveEntity(SystemAsset entity) {
        systemAssetService.save(entity);
        return entity;
    }

    @Override
    protected SystemAsset updateEntity(SystemAsset existingEntity, SystemAsset newEntity) {
        newEntity.setId(existingEntity.getId());
        newEntity.setCreatedAt(existingEntity.getCreatedAt());
        systemAssetService.updateById(newEntity);
        return newEntity;
    }

    @Override
    protected List<SystemAsset> queryDataList(Object queryParams) {
        if (queryParams instanceof SystemAssetQueryDTO) {
            SystemAssetQueryDTO queryDTO = (SystemAssetQueryDTO) queryParams;
            // 设置一个较大的页面大小来获取所有数据
            queryDTO.setPageSize(10000L);
            return systemAssetService.getSystemAssetPage(queryDTO).getData();
        } else {
            // 如果没有查询参数，返回所有系统资产
            return systemAssetService.list();
        }
    }

    /**
     * 将导入结果转换为Map格式
     */
    public Map<String, Object> convertImportResult(Object result) {
        if (result instanceof ExcelImportResult) {
            ExcelImportResult excelResult =
                    (ExcelImportResult) result;

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("total", excelResult.getTotal());
            resultMap.put("success", excelResult.getSuccess());
            resultMap.put("failed", excelResult.getFailed());
            resultMap.put("skipped", excelResult.getSkipped());

            // 转换错误信息
            List<Map<String, Object>> errors = excelResult.getErrors().stream()
                    .map(error -> {
                        Map<String, Object> errorMap = new HashMap<>();
                        errorMap.put("row", error.getRow());
                        errorMap.put("field", error.getField());
                        errorMap.put("message", error.getMessage());
                        return errorMap;
                    })
                    .toList();
            resultMap.put("errors", errors);

            return resultMap;
        }

        return new HashMap<>();
    }
}
