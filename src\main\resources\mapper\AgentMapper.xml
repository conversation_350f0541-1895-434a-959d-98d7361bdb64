<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vwatj.ppms.mapper.AgentMapper">

    <!-- 根据名称查询Agent -->
    <select id="selectByName" resultType="com.vwatj.ppms.entity.Agent">
        SELECT * FROM agent WHERE name = #{name}
    </select>

    <!-- 分页查询Agent -->
    <select id="selectAgentPage" resultType="com.vwatj.ppms.entity.Agent">
        SELECT * FROM agent
        <where>
            <if test="keyword != null and keyword != ''">
                AND (name LIKE CONCAT('%', #{keyword}, '%') 
                OR description LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
            <if test="creatorId != null">
                AND creator_id = #{creatorId}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

</mapper>
