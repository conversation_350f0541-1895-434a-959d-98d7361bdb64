package com.vwatj.ppms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateSystemAssetDTO;
import com.vwatj.ppms.dto.SystemAssetQueryDTO;
import com.vwatj.ppms.dto.UpdateSystemAssetDTO;
import com.vwatj.ppms.entity.SystemAsset;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Map;

/**
 * 系统资产服务接口
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface SystemAssetService extends IService<SystemAsset> {
    
    /**
     * 分页查询系统资产
     */
    PageResult<SystemAsset> getSystemAssetPage(SystemAssetQueryDTO queryDTO);
    
    /**
     * 根据资产编号查询系统资产
     */
    SystemAsset getSystemAssetByAssetNo(String assetNo);
    
    /**
     * 根据项目名称查询系统资产
     */
    SystemAsset getSystemAssetByProjectName(String projectName);
    
    /**
     * 根据短代码查询系统资产
     */
    SystemAsset getSystemAssetByShortCode(String shortCode);
    
    /**
     * 创建系统资产
     */
    SystemAsset createSystemAsset(CreateSystemAssetDTO createSystemAssetDTO);
    
    /**
     * 更新系统资产
     */
    SystemAsset updateSystemAsset(UpdateSystemAssetDTO updateSystemAssetDTO);
    
    /**
     * 删除系统资产
     */
    void deleteSystemAsset(Long id);

    /**
     * 生成导入模板
     */
    Resource generateTemplate();

    /**
     * 导出系统资产数据
     * @param queryDTO 查询条件
     * @return Excel文件资源
     */
    Resource exportAssets(SystemAssetQueryDTO queryDTO);

    /**
     * 导入系统资产（SSE方式）
     * @param file Excel文件
     * @param mode 导入模式：incremental(增量) 或 overwrite(覆盖)
     * @return SSE发射器
     */
    SseEmitter importAssets(MultipartFile file, String mode);


}
