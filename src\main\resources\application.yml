# PCC项目管理系统配置文件
server:
  port: 8080
  domain: http://localhost:8080
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: ppms
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************************
    username: root
    password: pcc@1234
    
    # Druid连接池配置
    druid:
      # 初始连接数
      initial-size: 5
      # 最小连接池数量
      min-idle: 10
      # 最大连接池数量
      max-active: 20
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      max-evictable-idle-time-millis: 900000
      # 配置检测连接是否有效
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      # 配置DruidStatFilter
      web-stat-filter:
        enabled: true
        url-pattern: "/*"
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"
      # 配置DruidStatViewServlet
      stat-view-servlet:
        enabled: true
        url-pattern: "/druid/*"
        # IP白名单(没有配置或者为空，则允许所有访问)
        allow: 127.0.0.1,*************
        # IP黑名单 (存在共同时，deny优先于allow)
        deny: ************
        # 禁用HTML页面上的"Reset All"功能
        reset-enable: false
        # 登录名
        login-username: admin
        # 登录密码
        login-password: 123456

  # Redis配置
  data:
    redis:
      host: *************
      port: 31769
      password: Tianjin@1234
      database: 0
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# MyBatis-Plus配置
mybatis-plus:
  # 配置扫描通用枚举
  type-enums-package: com.vwatj.ppms.enums
  # 配置mapper xml文件的路径
  mapper-locations: classpath*:mapper/**/*.xml
  # 配置MyBatis数据返回类型别名（默认别名是类名）
  type-aliases-package: com.vwatj.ppms.entity
  # 配置TypeHandler扫描包
  type-handlers-package: com.vwatj.ppms.config.typehandler
  configuration:
    # 自动驼峰命名转换
    map-underscore-to-camel-case: true
    # 配置添加数据自动返回数据主键
    use-generated-keys: true
    # 配置默认Executor
    default-executor-type: reuse
    # 配置数据库超时时间
    default-statement-timeout: 25000
    # 打印sql语句,调试用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      # 配置数据库主键类型
      id-type: auto
      # 配置全局表前缀
      table-prefix: 
      # 配置表名下划线命名
      table-underline: true
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
    # 关闭banner
    banner: false

# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: satoken
  # token有效期，单位s 默认30天, -1代表永不过期
  timeout: 2592000
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  activity-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: true
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: false
  # 是否从cookie中读取token
  is-read-cookie: false
  # 是否从header中读取token
  is-read-header: true
  # token前缀
  token-prefix: Bearer

# 日志配置
logging:
  level:
    com.vwatj.ppms: debug
    org.springframework.web: info
    org.springframework.security: info
    org.mybatis: debug
#  pattern:
#    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
#    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
#  file:
#    name: logs/ppms.log
#    max-size: 10MB
#    max-history: 30

# Camunda配置
camunda:
  bpm:
    # 管理员用户配置
    admin-user:
      id: admin
      password: admin
      firstName: Admin
      lastName: User
      email: <EMAIL>
    # 过滤器配置
    filter:
      create: All tasks
    # 授权配置
    authorization:
      enabled: true
    # 数据库配置
    database:
      schema-update: true
    # 作业执行器配置
    job-execution:
      enabled: true
    # 历史级别配置
    history-level: full

# 文件上传配置
file:
  upload:
    # 缓存区路径
    cache-path: ./uploads/cache
    # 项目文档区路径
    project-path: ./uploads/projects



# 应用配置
ppms:
  # 文件上传路径
  upload:
    path: /data/ppms/uploads/
    # 允许上传的文件类型
    allowed-types: jpg,jpeg,png,gif,bmp,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar,bpmn
    # 单个文件最大大小(MB)
    max-file-size: 100
  # 系统配置
  system:
    name: PCC项目管理系统
    version: 1.0.0
    description: 基于Spring Boot + MyBatis-Plus的项目管理系统
