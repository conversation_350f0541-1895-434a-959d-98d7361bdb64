package com.vwatj.ppms.enums;

/**
 * 流程状态枚举
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
public enum ProcessStatus {
    
    /**
     * 草稿状态 - 未发布
     */
    DRAFT("DRAFT", "草稿"),
    
    /**
     * 已发布状态 - 激活中
     */
    PUBLISHED("PUBLISHED", "已发布"),
    
    /**
     * 已挂起状态 - 暂停使用
     */
    SUSPENDED("SUSPENDED", "已挂起");

    private final String code;
    private final String description;

    ProcessStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     */
    public static ProcessStatus fromCode(String code) {
        for (ProcessStatus status : ProcessStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown process status code: " + code);
    }
}
