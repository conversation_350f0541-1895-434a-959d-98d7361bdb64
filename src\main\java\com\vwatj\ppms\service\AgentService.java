package com.vwatj.ppms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.AgentQueryDTO;
import com.vwatj.ppms.dto.CreateAgentDTO;
import com.vwatj.ppms.dto.UpdateAgentDTO;
import com.vwatj.ppms.entity.Agent;

/**
 * Agent服务接口
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface AgentService extends IService<Agent> {
    
    /**
     * 分页查询Agent
     */
    PageResult<Agent> getAgentPage(AgentQueryDTO queryDTO);
    
    /**
     * 创建Agent
     */
    Agent createAgent(CreateAgentDTO createAgentDTO);
    
    /**
     * 更新Agent
     */
    Agent updateAgent(UpdateAgentDTO updateAgentDTO);
    
    /**
     * 删除Agent
     */
    void deleteAgent(Long id);
    
    /**
     * 根据名称查询Agent
     */
    Agent getAgentByName(String name);
    
    /**
     * 切换Agent状态
     */
    void toggleAgentStatus(Long id);
}
