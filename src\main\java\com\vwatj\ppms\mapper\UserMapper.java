package com.vwatj.ppms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.vwatj.ppms.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {
    
    /**
     * 根据用户名查询用户
     */
    User selectByUsername(@Param("username") String username);
    
    /**
     * 根据邮箱查询用户
     */
    User selectByEmail(@Param("email") String email);
    
    /**
     * 分页查询用户
     */
    IPage<User> selectUserPage(Page<User> page, 
                              @Param("keyword") String keyword,
                              @Param("role") String role,
                              @Param("department") String department,
                              @Param("isActive") Boolean isActive);
}
