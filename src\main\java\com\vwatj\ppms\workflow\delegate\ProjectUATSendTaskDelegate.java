package com.vwatj.ppms.workflow.delegate;

import com.vwatj.ppms.entity.Project;
import com.vwatj.ppms.service.ProjectService;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 项目UAT邮件发送任务委托类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Slf4j
@Component("projectUATSendTaskDelegate")
public class ProjectUATSendTaskDelegate implements JavaDelegate {

    @Autowired
    private ProjectService projectService;

    @Override
    public void execute(DelegateExecution execution) throws Exception {
        try {
            // 获取项目信息
            Long projectId = getProjectId(execution);
            if (projectId == null) {
                log.warn("项目ID为空，跳过UAT邮件发送");
                return;
            }

            Project project = projectService.getById(projectId);
            if (project == null) {
                log.warn("项目不存在: projectId={}", projectId);
                return;
            }

            log.info("发送项目UAT邮件: projectId={}, projectName={}", projectId, project.getName());

            // TODO: 实现邮件发送逻辑
            sendUATMail(project);

            log.info("项目UAT邮件发送成功: projectId={}", projectId);

        } catch (Exception e) {
            log.error("发送项目UAT邮件失败: processInstanceId={}", execution.getProcessInstanceId(), e);
            throw e; // 抛出异常，让工作流处理
        }
    }

    /**
     * 发送UAT邮件
     */
    private void sendUATMail(Project project) {
        // TODO: 实现具体的邮件发送逻辑
        log.info("模拟发送UAT邮件: projectId={}, projectName={}", project.getId(), project.getName());
        
        // 邮件内容示例：
        // 主题：UAT测试通知 - {projectName}
        // 内容：
        // 尊敬的UAT测试人员：
        // 
        // 项目 {projectName} 已进入UAT测试阶段，请开始测试工作。
        // 
        // 项目详情：
        // - 项目ID: {projectId}
        // - 项目名称: {projectName}
        // - 项目代码: {projectCode}
        // - UAT开始时间: {当前时间}
        // 
        // 请按照测试计划进行UAT测试，并及时反馈测试结果。
        // 
        // 此致
        // 项目管理系统
    }

    /**
     * 获取项目ID
     */
    private Long getProjectId(DelegateExecution execution) {
        try {
            Object projectIdObj = execution.getVariable("projectId");
            if (projectIdObj instanceof Long) {
                return (Long) projectIdObj;
            } else if (projectIdObj instanceof String) {
                return Long.parseLong((String) projectIdObj);
            } else if (projectIdObj instanceof Integer) {
                return ((Integer) projectIdObj).longValue();
            }
        } catch (Exception e) {
            log.warn("获取项目ID失败", e);
        }
        return null;
    }
}
