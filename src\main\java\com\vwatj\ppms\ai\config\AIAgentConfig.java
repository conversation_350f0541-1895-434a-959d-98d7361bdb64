package com.vwatj.ppms.ai.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * AI Agent配置类
 * 配置DeepSeek V3作为LLM
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Configuration
public class AIAgentConfig {

    @Value("${spring.ai.openai.api-key}")
    private String apiKey;

    @Value("${spring.ai.openai.base-url:https://api.deepseek.com}")
    private String baseUrl;

    @Value("${spring.ai.openai.chat.options.model:deepseek-chat}")
    private String model;

    @Value("${spring.ai.openai.chat.options.temperature:0.7}")
    private Double temperature;

    @Value("${spring.ai.openai.chat.options.max-tokens:4000}")
    private Integer maxTokens;

    /**
     * 配置OpenAI API客户端（用于DeepSeek）
     */
    @Bean
    public OpenAiApi openAiApi() {
        return new OpenAiApi(baseUrl, apiKey);
    }

    /**
     * 配置OpenAI Chat模型（DeepSeek V3）
     */
    @Bean
    public OpenAiChatModel openAiChatModel(OpenAiApi openAiApi) {
        OpenAiChatOptions options = OpenAiChatOptions.builder()
                .withModel(model)
                .withTemperature(temperature.floatValue())
                .withMaxTokens(maxTokens)
                .build();
        
        return new OpenAiChatModel(openAiApi, options);
    }

    /**
     * 配置Chat客户端
     */
    @Bean
    public ChatClient chatClient(OpenAiChatModel chatModel) {
        return ChatClient.builder(chatModel)
                .defaultSystem("""
                    你是一个专业的项目管理系统AI助手，名为PPMS Assistant。
                    你的主要职责是帮助用户管理项目、任务、流程等相关业务。
                    
                    你具备以下能力：
                    1. 项目管理：创建、查询、更新、删除项目
                    2. 任务管理：创建、分配、跟踪任务状态
                    3. 流程管理：处理项目流程、表单提交
                    4. 数据分析：提供项目和任务的统计分析
                    5. 智能建议：基于项目数据提供优化建议
                    
                    请始终以专业、友好的语调回答用户问题，并在需要时主动调用相关的业务功能。
                    如果用户的请求不够明确，请主动询问更多细节。
                    """)
                .build();
    }
}
