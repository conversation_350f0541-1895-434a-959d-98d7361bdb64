package com.vwatj.ppms.excel.util;

import org.junit.jupiter.api.Test;
import java.time.LocalDateTime;
import static org.junit.jupiter.api.Assertions.*;

/**
 * ExcelUtils测试类
 */
public class ExcelUtilsTest {

    @Test
    public void testParseDateTimeWithJavaDateFormat() {
        // 测试Java Date.toString()格式（带时区）
        String dateStr1 = "Mon Aug 05 06:59:59 CST 2024";
        LocalDateTime result1 = ExcelUtils.parseDateTime(dateStr1);

        assertNotNull(result1);
        assertEquals(2024, result1.getYear());
        assertEquals(8, result1.getMonthValue());
        assertEquals(5, result1.getDayOfMonth());

        // 测试Java Date.toString()格式（不带时区）
        String dateStr2 = "Mon Aug 05 06:59:59 2024";
        LocalDateTime result2 = ExcelUtils.parseDateTime(dateStr2);

        assertNotNull(result2);
        assertEquals(2024, result2.getYear());
        assertEquals(8, result2.getMonthValue());
        assertEquals(5, result2.getDayOfMonth());
    }

    @Test
    public void testParseDateTimeWithStandardFormats() {
        // 测试标准格式
        assertEquals(LocalDateTime.of(2024, 8, 5, 14, 30, 0), 
                    ExcelUtils.parseDateTime("2024-08-05 14:30:00"));
        
        assertEquals(LocalDateTime.of(2024, 8, 5, 14, 30, 0), 
                    ExcelUtils.parseDateTime("2024-08-05 14:30"));
        
        assertEquals(LocalDateTime.of(2024, 8, 5, 0, 0, 0), 
                    ExcelUtils.parseDateTime("2024-08-05"));
    }

    @Test
    public void testParseDateTimeWithTimestamp() {
        // 测试时间戳
        long timestamp = 1722834599000L; // 对应 2024-08-05 06:59:59
        LocalDateTime result = ExcelUtils.parseDateTime(String.valueOf(timestamp));
        
        assertNotNull(result);
        assertEquals(2024, result.getYear());
        assertEquals(8, result.getMonthValue());
        assertEquals(5, result.getDayOfMonth());
    }

    @Test
    public void testParseDateTimeWithNullOrEmpty() {
        assertNull(ExcelUtils.parseDateTime(null));
        assertNull(ExcelUtils.parseDateTime(""));
        assertNull(ExcelUtils.parseDateTime("   "));
    }

    @Test
    public void testParseDateTimeWithInvalidFormat() {
        assertThrows(RuntimeException.class, () -> {
            ExcelUtils.parseDateTime("invalid date format");
        });
    }
}
