package com.vwatj.ppms.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.AgentQueryDTO;
import com.vwatj.ppms.dto.CreateAgentDTO;
import com.vwatj.ppms.dto.UpdateAgentDTO;
import com.vwatj.ppms.entity.Agent;
import com.vwatj.ppms.mapper.AgentMapper;
import com.vwatj.ppms.service.AgentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * Agent服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentServiceImpl extends ServiceImpl<AgentMapper, Agent> implements AgentService {

    private final AgentMapper agentMapper;
    
    @Override
    public PageResult<Agent> getAgentPage(AgentQueryDTO queryDTO) {
        Page<Agent> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
        IPage<Agent> result = agentMapper.selectAgentPage(page,
            queryDTO.getKeyword(),
            queryDTO.getStatus(),
            queryDTO.getType(),
            queryDTO.getCreatorId());
        return PageResult.from(result);
    }
    
    @Override
    public Agent createAgent(CreateAgentDTO createAgentDTO) {
        // 检查名称是否已存在
        Agent existingAgent = agentMapper.selectByName(createAgentDTO.getName());
        if (existingAgent != null) {
            throw new RuntimeException("Agent名称已存在");
        }
        
        Agent agent = new Agent();
        BeanUtils.copyProperties(createAgentDTO, agent);
        
        // TODO: 设置创建者信息，这里暂时使用默认值
        agent.setCreatorId(1L);
        agent.setCreatorName("系统管理员");
        
        agentMapper.insert(agent);
        return agent;
    }
    
    @Override
    public Agent updateAgent(UpdateAgentDTO updateAgentDTO) {
        Agent agent = agentMapper.selectById(updateAgentDTO.getId());
        if (agent == null) {
            throw new RuntimeException("Agent不存在");
        }
        
        // 如果名称有变更，检查新名称是否已存在
        if (updateAgentDTO.getName() != null && !updateAgentDTO.getName().equals(agent.getName())) {
            Agent existingAgent = agentMapper.selectByName(updateAgentDTO.getName());
            if (existingAgent != null && !existingAgent.getId().equals(updateAgentDTO.getId())) {
                throw new RuntimeException("Agent名称已存在");
            }
        }
        
        BeanUtils.copyProperties(updateAgentDTO, agent, "id", "creatorId", "creatorName");
        agentMapper.updateById(agent);
        return agent;
    }
    
    @Override
    public void deleteAgent(Long id) {
        Agent agent = agentMapper.selectById(id);
        if (agent == null) {
            throw new RuntimeException("Agent不存在");
        }
        agentMapper.deleteById(id);
    }
    
    @Override
    public Agent getAgentByName(String name) {
        return agentMapper.selectByName(name);
    }
    
    @Override
    public void toggleAgentStatus(Long id) {
        Agent agent = agentMapper.selectById(id);
        if (agent == null) {
            throw new RuntimeException("Agent不存在");
        }
        
        // 切换状态：0变1，1变0
        agent.setStatus(agent.getStatus() == 1 ? 0 : 1);
        agentMapper.updateById(agent);
    }
}
