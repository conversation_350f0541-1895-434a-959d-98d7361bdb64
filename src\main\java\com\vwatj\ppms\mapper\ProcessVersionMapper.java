package com.vwatj.ppms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.vwatj.ppms.entity.ProcessVersion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 流程版本Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Mapper
public interface ProcessVersionMapper extends BaseMapper<ProcessVersion> {

    /**
     * 分页查询流程版本
     */
    IPage<ProcessVersion> selectProcessVersionPage(
            Page<ProcessVersion> page,
            @Param("processDefinitionId") Long processDefinitionId,
            @Param("version") Integer version,
            @Param("status") String status,
            @Param("createdBy") String createdBy
    );

    /**
     * 根据流程定义ID查询最大版本号
     */
    Integer selectMaxVersionByProcessDefinitionId(@Param("processDefinitionId") Long processDefinitionId);

    /**
     * 根据流程定义ID和版本号查询
     */
    ProcessVersion selectByProcessDefinitionIdAndVersion(
            @Param("processDefinitionId") Long processDefinitionId,
            @Param("version") Integer version
    );

    /**
     * 根据流程定义ID查询当前发布版本
     */
    ProcessVersion selectPublishedVersionByProcessDefinitionId(@Param("processDefinitionId") Long processDefinitionId);

    /**
     * 更新发布状态
     */
    int updatePublishStatus(
            @Param("processDefinitionId") Long processDefinitionId,
            @Param("version") Integer version,
            @Param("isPublished") Boolean isPublished
    );
}
