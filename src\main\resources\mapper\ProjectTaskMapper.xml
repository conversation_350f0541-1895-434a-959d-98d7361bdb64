<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vwatj.ppms.mapper.ProjectTaskMapper">

    <!-- 分页查询任务 -->
    <select id="selectTaskPage" resultType="com.vwatj.ppms.entity.ProjectTask">
        SELECT * FROM task
        <where>
            <if test="keyword != null and keyword != ''">
                AND (title LIKE CONCAT('%', #{keyword}, '%') 
                OR description LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="projectId != null">
                AND project_id = #{projectId}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="priority != null and priority != ''">
                AND priority = #{priority}
            </if>
            <if test="stageName != null and stageName != ''">
                AND stage_name = #{stageName}
            </if>
            <if test="issueType != null and issueType != ''">
                AND issue_type = #{issueType}
            </if>
            <if test="assignee != null and assignee != ''">
                AND assignee = #{assignee}
            </if>
            <if test="reporter != null and reporter != ''">
                AND reporter = #{reporter}
            </if>
            <if test="plannedStartTime != null">
                AND planned_start_time &gt;= #{plannedStartTime}
            </if>
            <if test="plannedEndTime != null">
                AND planned_end_time &lt;= #{plannedEndTime}
            </if>
            <if test="actualStartTime != null">
                AND actual_start_time &gt;= #{actualStartTime}
            </if>
            <if test="actualEndTime != null">
                AND actual_end_time &lt;= #{actualEndTime}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 查询项目任务 -->
    <select id="selectProjectTasks" resultType="com.vwatj.ppms.entity.ProjectTask">
        SELECT * FROM task WHERE project_id = #{projectId} ORDER BY created_at DESC
    </select>

    <!-- 查询用户任务 -->
    <select id="selectUserTasks" resultType="com.vwatj.ppms.entity.ProjectTask">
        SELECT * FROM task WHERE assignee = #{assignee} ORDER BY created_at DESC
    </select>

    <!-- 高级搜索任务 -->
    <select id="searchTasks" resultType="com.vwatj.ppms.entity.ProjectTask">
        SELECT * FROM task
        <where>
            <!-- 关键词搜索 -->
            <if test="keyword != null and keyword != ''">
                AND (title LIKE CONCAT('%', #{keyword}, '%')
                OR description LIKE CONCAT('%', #{keyword}, '%')
                OR assignee LIKE CONCAT('%', #{keyword}, '%'))
            </if>

            <!-- 项目筛选 -->
            <if test="projectId != null">
                AND project_id = #{projectId}
            </if>

            <!-- 单选状态筛选 -->
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>

            <!-- 多选状态筛选 -->
            <if test="statusList != null and statusList.size() > 0">
                AND status IN
                <foreach collection="statusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <!-- 优先级筛选 -->
            <if test="priority != null and priority != ''">
                AND priority = #{priority}
            </if>

            <!-- 任务阶段筛选 -->
            <if test="stageName != null and stageName != ''">
                AND stage_name = #{stageName}
            </if>

            <!-- 问题类型筛选 -->
            <if test="issueType != null and issueType != ''">
                AND issue_type = #{issueType}
            </if>

            <!-- 指派人筛选 -->
            <if test="assignee != null and assignee != ''">
                AND assignee = #{assignee}
            </if>

            <!-- 报告人筛选 -->
            <if test="reporter != null and reporter != ''">
                AND reporter = #{reporter}
            </if>

            <!-- 任务范围筛选 -->
            <if test="taskScope != null and taskScope != '' and taskScope != 'all'">
                <choose>
                    <when test="taskScope == 'my'">
                        AND assignee = #{currentUser}
                    </when>
                    <when test="taskScope == 'unassigned'">
                        AND (assignee IS NULL OR assignee = '')
                    </when>
                    <when test="taskScope == 'todo'">
                        AND assignee = #{currentUser} AND status != 'close'
                    </when>
                </choose>
            </if>

            <!-- 时间范围筛选 -->
            <if test="dateRangeStart != null and dateRangeEnd != null">
                AND (
                    (planned_start_time BETWEEN #{dateRangeStart} AND #{dateRangeEnd})
                    OR
                    (actual_start_time BETWEEN #{dateRangeStart} AND #{dateRangeEnd})
                )
            </if>

            <!-- 周范围筛选 -->
            <if test="weekRangeStart != null and weekRangeEnd != null">
                AND (
                    <!-- 条件1：已完成状态 + 完成日期在选择的周内 -->
                    (status = 'close' AND actual_end_time BETWEEN #{weekRangeStart} AND #{weekRangeEnd})
                    OR
                    <!-- 条件2：所有待办/进行中/待验证状态的任务 -->
                    (status IN ('open', 'inprogress', 'toverify'))
                )
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 统计任务数量按状态 -->
    <select id="countTasksByStatus" resultType="map">
        SELECT status, COUNT(*) as count FROM task
        <where>
            <if test="projectId != null">
                AND project_id = #{projectId}
            </if>
        </where>
        GROUP BY status
    </select>

    <!-- 统计任务数量按优先级 -->
    <select id="countTasksByPriority" resultType="map">
        SELECT priority, COUNT(*) as count FROM projectTask
        <where>
            <if test="projectId != null">
                AND project_id = #{projectId}
            </if>
        </where>
        GROUP BY priority
    </select>

    <!-- 统计任务数量按问题类型 -->
    <select id="countTasksByIssueType" resultType="map">
        SELECT issue_type, COUNT(*) as count FROM task
        <where>
            <if test="projectId != null">
                AND project_id = #{projectId}
            </if>
        </where>
        GROUP BY issue_type
    </select>

    <!-- 统计逾期任务数量 -->
    <select id="countOverdueTasks" resultType="long">
        SELECT COUNT(*) FROM task
        WHERE planned_end_time &lt; NOW() 
        AND status != 'done'
        <if test="projectId != null">
            AND project_id = #{projectId}
        </if>
    </select>

    <!-- 统计已完成任务数量 -->
    <select id="countCompletedTasks" resultType="long">
        SELECT COUNT(*) FROM task
        WHERE status = 'done'
        <if test="projectId != null">
            AND project_id = #{projectId}
        </if>
    </select>

    <!-- 统计项目任务总数 -->
    <select id="countTasksByProject" resultType="long">
        SELECT COUNT(*) FROM task WHERE project_id = #{projectId}
    </select>

    <!-- 统计项目任务数量 -->
    <select id="countTasksByProjectAndStatus" resultType="long">
        SELECT COUNT(*) FROM task WHERE project_id = #{projectId} AND status = #{taskStatus}
    </select>

</mapper>
