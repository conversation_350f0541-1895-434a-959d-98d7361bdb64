package com.vwatj.ppms.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
public class TaskQueryDTO {

    /**
     * 页码
     */
    private Long page = 1L;

    /**
     * 每页大小
     */
    private Long pageSize = 10L;

    /**
     * 排序字段
     */
    private String sortBy;

    /**
     * 排序方向
     */
    private String sortOrder;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 任务优先级
     */
    private String priority;

    /**
     * 任务阶段
     */
    private String stageName;

    /**
     * 问题类型
     */
    private String issueType;

    /**
     * 指派人
     */
    private String assignee;

    /**
     * 报告人
     */
    private String reporter;

    /**
     * 任务范围筛选
     * all: 全部任务
     * my: 我的任务
     * unassigned: 未分配的任务
     * todo: 我的待办
     */
    private String taskScope;

    /**
     * 当前用户名（用于任务范围筛选）
     */
    private String currentUser;

    /**
     * 多选状态列表
     */
    private List<String> statusList;
    
    /**
     * 计划开始时间
     */
    private LocalDateTime plannedStartTime;

    /**
     * 计划结束时间
     */
    private LocalDateTime plannedEndTime;

    /**
     * 实际开始时间
     */
    private LocalDateTime actualStartTime;

    /**
     * 实际结束时间
     */
    private LocalDateTime actualEndTime;

    /**
     * 时间范围筛选开始时间
     */
    private LocalDateTime dateRangeStart;

    /**
     * 时间范围筛选结束时间
     */
    private LocalDateTime dateRangeEnd;

    /**
     * 周范围筛选开始时间
     */
    private LocalDateTime weekRangeStart;

    /**
     * 周范围筛选结束时间
     */
    private LocalDateTime weekRangeEnd;
}
