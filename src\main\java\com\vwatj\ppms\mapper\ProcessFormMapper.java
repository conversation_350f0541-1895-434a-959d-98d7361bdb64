package com.vwatj.ppms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.vwatj.ppms.entity.ProcessForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 流程表单Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Mapper
public interface ProcessFormMapper extends BaseMapper<ProcessForm> {

    /**
     * 根据表单Key获取表单信息
     *
     * @param formKey 表单Key
     * @return 表单信息
     */
    @Select("SELECT * FROM process_form WHERE form_key = #{formKey} AND deleted = 0")
    ProcessForm selectByFormKey(@Param("formKey") String formKey);

    /**
     * 根据表单Key列表批量获取表单信息
     *
     * @param formKeys 表单Key列表
     * @return 表单信息列表
     */
    @Select("<script>" +
            "SELECT * FROM process_form WHERE form_key IN " +
            "<foreach collection='formKeys' item='formKey' open='(' separator=',' close=')'>" +
            "#{formKey}" +
            "</foreach>" +
            " AND deleted = 0 ORDER BY order_num ASC" +
            "</script>")
    List<ProcessForm> selectByFormKeys(@Param("formKeys") List<String> formKeys);

    /**
     * 更新表单状态
     *
     * @param formKey 表单Key
     * @param status 状态
     * @return 更新行数
     */
    @Update("UPDATE process_form SET status = #{status}, update_time = NOW() " +
            "WHERE form_key = #{formKey} AND deleted = 0")
    int updateStatusByFormKey(@Param("formKey") String formKey, @Param("status") String status);

    /**
     * 更新表单数据
     *
     * @param formKey 表单Key
     * @param formData 表单数据JSON
     * @return 更新行数
     */
    @Update("UPDATE process_form SET form_data = #{formData}, update_time = NOW() " +
            "WHERE form_key = #{formKey} AND deleted = 0")
    int updateFormDataByFormKey(@Param("formKey") String formKey, @Param("formData") String formData);

    /**
     * 获取所有启用的表单
     *
     * @return 表单列表
     */
    @Select("SELECT * FROM process_form WHERE enabled = 1 AND deleted = 0 ORDER BY order_num ASC")
    List<ProcessForm> selectAllEnabled();

    /**
     * 根据状态获取表单列表
     *
     * @param status 状态
     * @return 表单列表
     */
    @Select("SELECT * FROM process_form WHERE status = #{status} AND deleted = 0 ORDER BY order_num ASC")
    List<ProcessForm> selectByStatus(@Param("status") String status);

    /**
     * 批量更新表单状态
     *
     * @param formKeys 表单Key列表
     * @param status 状态
     * @return 更新行数
     */
    @Update("<script>" +
            "UPDATE process_form SET status = #{status}, update_time = NOW() " +
            "WHERE form_key IN " +
            "<foreach collection='formKeys' item='formKey' open='(' separator=',' close=')'>" +
            "#{formKey}" +
            "</foreach>" +
            " AND deleted = 0" +
            "</script>")
    int batchUpdateStatusByFormKeys(@Param("formKeys") List<String> formKeys, @Param("status") String status);
}
