package com.vwatj.ppms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateProcessVersionDTO;
import com.vwatj.ppms.dto.ProcessVersionQueryDTO;
import com.vwatj.ppms.entity.ProcessVersion;

/**
 * 流程版本服务接口
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
public interface ProcessVersionService extends IService<ProcessVersion> {

    /**
     * 分页查询流程版本
     */
    PageResult<ProcessVersion> getProcessVersionPage(ProcessVersionQueryDTO queryDTO);

    /**
     * 创建流程版本
     */
    ProcessVersion createProcessVersion(CreateProcessVersionDTO createDTO);

    /**
     * 删除流程版本
     */
    void deleteProcessVersion(Long id);

    /**
     * 根据流程定义ID和版本号查询
     */
    ProcessVersion getByProcessDefinitionIdAndVersion(Long processDefinitionId, Integer version);

    /**
     * 根据流程定义ID查询当前发布版本
     */
    ProcessVersion getPublishedVersionByProcessDefinitionId(Long processDefinitionId);

    /**
     * 获取BPMN文件内容
     */
    String getBpmnContent(Long id);

    /**
     * 下载BPMN文件
     */
    byte[] downloadBpmnFile(Long id);

    /**
     * 激活版本
     */
    void activateVersion(Long id);

    /**
     * 挂起版本
     */
    void suspendVersion(Long id);
}
