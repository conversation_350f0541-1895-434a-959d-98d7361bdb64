package com.vwatj.ppms.excel.core;

import com.vwatj.ppms.excel.converter.ExcelDataConverter;
import com.vwatj.ppms.excel.converter.ExcelDataConvertException;
import com.vwatj.ppms.excel.style.ExcelStyleManager;
import com.vwatj.ppms.excel.util.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * Excel服务抽象基类
 * 提供Excel导入导出的通用功能
 *
 * @param <T> 实体类型
 * <AUTHOR>
 * @since 2025-07-09
 */
@Slf4j
public abstract class AbstractExcelGenerate<T> {

    /**
     * 获取数据转换器
     */
    protected abstract ExcelDataConverter<T> getDataConverter();

    /**
     * 根据唯一标识符查找现有实体
     */
    protected abstract T findByUniqueField(Object uniqueValue);

    /**
     * 根据唯一标识符和额外参数查找现有实体（用于任务的TaskNo+ProjectId组合判断）
     */
    protected T findByUniqueFieldWithParams(Object uniqueValue, Map<String, Object> additionalParams) {
        return findByUniqueField(uniqueValue);
    }

    /**
     * 保存实体
     */
    protected abstract T saveEntity(T entity);

    /**
     * 更新实体
     */
    protected abstract T updateEntity(T existingEntity, T newEntity);

    /**
     * 查询数据列表
     */
    protected abstract List<T> queryDataList(Object queryParams);

    /**
     * 设置实体的项目ID和项目名称（可选实现）
     */
    protected void setEntityProjectId(T entity, Long projectId, String projectName) {
        // 默认实现为空，子类可以重写
    }

    /**
     * 生成Excel模板
     */
    public Resource generateTemplate() {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            ExcelStyleManager styleManager = new ExcelStyleManager(workbook);
            ExcelDataConverter<T> converter = getDataConverter();
            
            Sheet sheet = workbook.createSheet("导入模板");
            
            // 创建表头
            createHeaderRow(sheet, converter.getTemplateHeaders(), styleManager);
            
            // 设置列宽
            ExcelUtils.setColumnWidths(sheet, converter.getColumnWidths());
            
            // 冻结首行
            ExcelUtils.freezePane(sheet, 0, 1);
            
            return createResource(workbook);
            
        } catch (IOException e) {
            log.error("生成Excel模板失败", e);
            throw new RuntimeException("生成Excel模板失败: " + e.getMessage());
        }
    }

    /**
     * 导出数据到Excel
     */
    public Resource exportData(Object queryParams) {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            ExcelStyleManager styleManager = new ExcelStyleManager(workbook);
            ExcelDataConverter<T> converter = getDataConverter();
            
            Sheet sheet = workbook.createSheet("数据导出");
            
            // 创建表头
            createHeaderRow(sheet, converter.getExportHeaders(), styleManager);
            
            // 查询数据并填充
            List<T> dataList = queryDataList(queryParams);
            createDataRows(sheet, dataList, converter, styleManager);
            
            // 设置列宽
            ExcelUtils.setColumnWidths(sheet, converter.getColumnWidths());
            
            // 冻结首行
            ExcelUtils.freezePane(sheet, 0, 1);
            
            return createResource(workbook);
            
        } catch (IOException e) {
            log.error("导出Excel数据失败", e);
            throw new RuntimeException("导出Excel数据失败: " + e.getMessage());
        }
    }

    /**
     * 从Excel导入数据
     */
    public ExcelImportResult importData(MultipartFile file, String mode) {
        return importData(file, mode, null);
    }

    /**
     * 从Excel导入数据（带项目ID）
     */
    public ExcelImportResult importData(MultipartFile file, String mode, Long projectId) {
        return importData(file, mode, projectId, null);
    }

    /**
     * SSE方式导入数据
     */
    public SseEmitter importDataWithSSE(MultipartFile file, String mode) {
        return importDataWithSSE(file, mode, null, null);
    }

    /**
     * SSE方式导入数据（带项目ID）
     */
    public SseEmitter importDataWithSSE(MultipartFile file, String mode, Long projectId) {
        return importDataWithSSE(file, mode, projectId, null);
    }

    /**
     * SSE方式导入数据（带项目ID和项目名称）
     */
    public SseEmitter importDataWithSSE(MultipartFile file, String mode, Long projectId, String projectName) {
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时

        // 异步处理导入
        CompletableFuture.runAsync(() -> {
            try {
                processImportWithSSE(file, mode, projectId, projectName, emitter);
            } catch (Exception e) {
                log.error("SSE导入处理失败", e);
                try {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data(Map.of("message", "导入失败: " + e.getMessage())));
                    emitter.complete();
                } catch (IOException ioException) {
                    log.error("发送SSE错误事件失败", ioException);
                    emitter.completeWithError(ioException);
                }
            }
        });

        return emitter;
    }

    /**
     * 从Excel导入数据（带项目ID和项目名称）
     */
    public ExcelImportResult importData(MultipartFile file, String mode, Long projectId, String projectName) {
        ExcelImportResult result = new ExcelImportResult();
        List<ExcelImportError> errors = new ArrayList<>();
        
        try (Workbook workbook = WorkbookFactory.create(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            ExcelDataConverter<T> converter = getDataConverter();
            
            if (sheet.getPhysicalNumberOfRows() <= 1) {
                result.setTotal(0);
                result.setSuccess(0);
                result.setFailed(0);
                result.setSkipped(0);
                errors.add(new ExcelImportError(1, "文件", "文件中没有数据行"));
                result.setErrors(errors);
                return result;
            }

            // 解析表头
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                throw new RuntimeException("文件格式错误：缺少表头行");
            }
            
            Map<String, Integer> headerMap = ExcelUtils.parseHeaders(headerRow);
            
            // 处理数据行
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (ExcelUtils.isEmptyRow(row)) {
                    continue;
                }

                result.incrementTotal();
                
                try {
                    // 解析行数据
                    Map<String, Object> rowData = ExcelUtils.parseRowData(row, headerMap);
                    
                    // 转换为实体
                    T entity = converter.rowDataToEntity(rowData, i + 1);
                    
                    // 处理导入逻辑
                    ImportAction action = processImportEntity(entity, mode, converter, projectId, projectName);
                    switch (action) {
                        case CREATED:
                        case UPDATED:
                            result.incrementSuccess();
                            break;
                        case SKIPPED:
                            result.incrementSkipped();
                            break;
                    }
                    
                } catch (ExcelDataConvertException e) {
                    result.incrementFailed();
                    errors.add(new ExcelImportError(e.getRowIndex(), e.getFieldName(), e.getErrorMessage()));
                    log.warn("导入第{}行数据失败: {}", i + 1, e.getMessage());
                } catch (Exception e) {
                    result.incrementFailed();
                    errors.add(new ExcelImportError(i + 1, "数据", e.getMessage()));
                    log.warn("导入第{}行数据失败: {}", i + 1, e.getMessage());
                }
            }

        } catch (Exception e) {
            log.error("导入Excel文件处理失败", e);
            throw new RuntimeException("导入失败: " + e.getMessage());
        }

        result.setErrors(errors);
        return result;
    }

    /**
     * 创建表头行
     */
    private void createHeaderRow(Sheet sheet, String[] headers, ExcelStyleManager styleManager) {
        Row headerRow = sheet.createRow(0);
        
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            String header = headers[i];
            cell.setCellValue(header);
            
            // 根据是否包含*号选择样式
            boolean required = header.contains("*");
            cell.setCellStyle(styleManager.getHeaderStyle(required));
        }
    }

    /**
     * 创建数据行
     */
    private void createDataRows(Sheet sheet, List<T> dataList, ExcelDataConverter<T> converter, ExcelStyleManager styleManager) {
        for (int i = 0; i < dataList.size(); i++) {
            Row dataRow = sheet.createRow(i + 1);
            Object[] rowData = converter.entityToRowData(dataList.get(i));
            
            for (int j = 0; j < rowData.length; j++) {
                Cell cell = dataRow.createCell(j);
                Object value = rowData[j];
                CellStyle style = styleManager.getStyleByDataType(value);
                ExcelUtils.setCellValue(cell, value, style);
            }
        }
    }

    /**
     * SSE方式处理导入
     */
    private void processImportWithSSE(MultipartFile file, String mode, Long projectId, String projectName, SseEmitter emitter) throws IOException {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> errors = new ArrayList<>();
        int total = 0;
        int success = 0;
        int failed = 0;
        int skipped = 0;

        try (Workbook workbook = WorkbookFactory.create(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            ExcelDataConverter<T> converter = getDataConverter();
            int lastRowNum = sheet.getLastRowNum();

            // 发送开始事件
            emitter.send(SseEmitter.event()
                .name("start")
                .data(Map.of("total", lastRowNum)));

            if (lastRowNum <= 0) {
                emitter.send(SseEmitter.event()
                    .name("error")
                    .data(Map.of("message", "文件中没有数据行")));
                emitter.complete();
                return;
            }

            // 解析表头
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                emitter.send(SseEmitter.event()
                    .name("error")
                    .data(Map.of("message", "文件格式错误：缺少表头行")));
                emitter.complete();
                return;
            }

            Map<String, Integer> headerMap = ExcelUtils.parseHeaders(headerRow);

            // 处理数据行
            for (int i = 1; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                if (ExcelUtils.isEmptyRow(row)) {
                    continue;
                }

                total++;

                try {
                    // 解析行数据
                    Map<String, Object> rowData = ExcelUtils.parseRowData(row, headerMap);

                    // 转换为实体
                    T entity = converter.rowDataToEntity(rowData, i + 1);

                    // 处理导入逻辑
                    Map<String, Object> additionalParams = new HashMap<>();
                    if (projectId != null) {
                        additionalParams.put("projectId", projectId);
                        additionalParams.put("projectName", projectName);
                    }

                    ImportAction action = processImportEntityWithParams(entity, mode, converter, additionalParams);
                    switch (action) {
                        case CREATED:
                        case UPDATED:
                            success++;
                            break;
                        case SKIPPED:
                            skipped++;
                            break;
                    }

                    // 每处理10条记录发送一次进度
                    if (total % 10 == 0) {
                        emitter.send(SseEmitter.event()
                            .name("progress")
                            .data(Map.of(
                                "processed", total,
                                "total", lastRowNum,
                                "success", success,
                                "failed", failed,
                                "skipped", skipped
                            )));
                    }

                } catch (ExcelDataConvertException e) {
                    failed++;
                    Map<String, Object> error = new HashMap<>();
                    error.put("row", e.getRowIndex());
                    error.put("field", e.getFieldName());
                    error.put("message", e.getErrorMessage());
                    errors.add(error);
                    log.warn("导入第{}行数据失败: {}", i + 1, e.getMessage());
                } catch (Exception e) {
                    failed++;
                    Map<String, Object> error = new HashMap<>();
                    error.put("row", i + 1);
                    error.put("field", "数据");
                    error.put("message", e.getMessage());
                    errors.add(error);
                    log.warn("导入第{}行数据失败: {}", i + 1, e.getMessage());
                }
            }

            // 构建最终结果
            result.put("total", total);
            result.put("success", success);
            result.put("failed", failed);
            result.put("skipped", skipped);
            if (!errors.isEmpty()) {
                result.put("errors", errors);
            }

            // 发送完成事件
            emitter.send(SseEmitter.event()
                .name("complete")
                .data(result));

            emitter.complete();

        } catch (Exception e) {
            log.error("SSE导入处理失败", e);
            emitter.send(SseEmitter.event()
                .name("error")
                .data(Map.of("message", "导入失败: " + e.getMessage())));
            emitter.completeWithError(e);
        }
    }

    /**
     * 处理导入实体（带额外参数）
     */
    private ImportAction processImportEntityWithParams(T entity, String mode, ExcelDataConverter<T> converter, Map<String, Object> additionalParams) {
        // 设置项目ID和项目名称（如果提供）
        Long projectId = (Long) additionalParams.get("projectId");
        String projectName = (String) additionalParams.get("projectName");
        if (projectId != null) {
            setEntityProjectId(entity, projectId, projectName);
        }

        Object uniqueValue = converter.getUniqueFieldValue(entity);
        if (uniqueValue == null) {
            throw new RuntimeException("唯一标识符不能为空");
        }

        T existingEntity = findByUniqueFieldWithParams(uniqueValue, additionalParams);

        if (existingEntity == null) {
            // 新增
            saveEntity(entity);
            return ImportAction.CREATED;
        } else {
            // 已存在的记录
            if ("incremental".equals(mode)) {
                // 增量模式：跳过已存在的记录
                return ImportAction.SKIPPED;
            } else if ("overwrite".equals(mode)) {
                // 覆盖模式：更新已存在的记录
                updateEntity(existingEntity, entity);
                return ImportAction.UPDATED;
            }
        }

        return ImportAction.SKIPPED;
    }

    /**
     * 处理导入实体
     */
    private ImportAction processImportEntity(T entity, String mode, ExcelDataConverter<T> converter, Long projectId, String projectName) {
        // 设置项目ID和项目名称（如果提供）
        if (projectId != null) {
            setEntityProjectId(entity, projectId, projectName);
        }

        Object uniqueValue = converter.getUniqueFieldValue(entity);
        if (uniqueValue == null) {
            throw new RuntimeException("唯一标识符不能为空");
        }

        T existingEntity = findByUniqueField(uniqueValue);

        if (existingEntity == null) {
            // 新增
            saveEntity(entity);
            return ImportAction.CREATED;
        } else {
            // 已存在的记录
            if ("incremental".equals(mode)) {
                // 增量模式：跳过已存在的记录
                return ImportAction.SKIPPED;
            } else if ("overwrite".equals(mode)) {
                // 覆盖模式：更新已存在的记录
                updateEntity(existingEntity, entity);
                return ImportAction.UPDATED;
            }
        }
        
        return ImportAction.SKIPPED;
    }

    /**
     * 创建资源对象
     */
    private Resource createResource(Workbook workbook) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        return new ByteArrayResource(outputStream.toByteArray());
    }

    /**
     * 导入操作类型
     */
    private enum ImportAction {
        CREATED, UPDATED, SKIPPED
    }
}

