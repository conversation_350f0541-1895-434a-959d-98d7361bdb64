package com.vwatj.ppms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateSystemDictDTO;
import com.vwatj.ppms.dto.SystemDictQueryDTO;
import com.vwatj.ppms.dto.UpdateSystemDictDTO;
import com.vwatj.ppms.entity.SystemDict;
import com.vwatj.ppms.mapper.SystemDictMapper;
import com.vwatj.ppms.service.SystemDictService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 系统字典服务实现
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Service
@RequiredArgsConstructor
public class SystemDictServiceImpl extends ServiceImpl<SystemDictMapper, SystemDict> implements SystemDictService {

    private final SystemDictMapper systemDictMapper;

    // 字典类型常量
    private static final String DICT_TYPE_REGION = "region";
    private static final String DICT_TYPE_ASSET_STATE = "asset_state";
    private static final String DICT_TYPE_BUSINESS_DEPARTMENT = "business_department";
    private static final String DICT_TYPE_USAGE_STATUS = "usage_status";
    private static final String DICT_TYPE_PRIORITY = "priority";
    private static final String DICT_TYPE_SITE = "site";

    @Override
    public List<Map<String, String>> getDictOptionsByType(String dictType) {
        return systemDictMapper.getDictOptionsByType(dictType);
    }

    @Override
    public List<String> getAllDictTypes() {
        return systemDictMapper.getAllDictTypes();
    }

    @Override
    public SystemDict getDictByTypeAndCode(String dictType, String dictCode) {
        return systemDictMapper.getDictByTypeAndCode(dictType, dictCode);
    }

    @Override
    public List<Map<String, String>> getRegionOptions() {
        return getDictOptionsByType(DICT_TYPE_REGION);
    }

    @Override
    public List<Map<String, String>> getAssetStateOptions() {
        return getDictOptionsByType(DICT_TYPE_ASSET_STATE);
    }

    @Override
    public List<Map<String, String>> getBusinessDepartmentOptions() {
        return getDictOptionsByType(DICT_TYPE_BUSINESS_DEPARTMENT);
    }

    @Override
    public List<Map<String, String>> getUsageStatusOptions() {
        return getDictOptionsByType(DICT_TYPE_USAGE_STATUS);
    }

    @Override
    public List<Map<String, String>> getPriorityOptions() {
        return getDictOptionsByType(DICT_TYPE_PRIORITY);
    }

    @Override
    public List<Map<String, String>> getSiteOptions() {
        return getDictOptionsByType(DICT_TYPE_SITE);
    }

    @Override
    public PageResult<SystemDict> getSystemDictPage(SystemDictQueryDTO queryDTO) {
        Page<SystemDict> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());

        LambdaQueryWrapper<SystemDict> queryWrapper = new LambdaQueryWrapper<>();

        // 关键词搜索（字典名称或代码）
        if (StringUtils.hasText(queryDTO.getKeyword())) {
            queryWrapper.and(wrapper -> wrapper
                .like(SystemDict::getDictName, queryDTO.getKeyword())
                .or()
                .like(SystemDict::getDictCode, queryDTO.getKeyword())
            );
        }

        // 字典类型过滤
        if (StringUtils.hasText(queryDTO.getDictType())) {
            queryWrapper.eq(SystemDict::getDictType, queryDTO.getDictType());
        }

        // 启用状态过滤
        if (queryDTO.getEnabled() != null) {
            queryWrapper.eq(SystemDict::getEnabled, queryDTO.getEnabled());
        }

        // 排序
        queryWrapper.orderByAsc(SystemDict::getDictType)
                   .orderByAsc(SystemDict::getSortOrder)
                   .orderByAsc(SystemDict::getId);

        IPage<SystemDict> result = this.page(page, queryWrapper);

        return PageResult.from(result);
    }

    @Override
    public SystemDict createSystemDict(CreateSystemDictDTO createSystemDictDTO) {
        // 检查字典代码是否已存在
        LambdaQueryWrapper<SystemDict> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemDict::getDictType, createSystemDictDTO.getDictType())
                   .eq(SystemDict::getDictCode, createSystemDictDTO.getDictCode());

        if (this.count(queryWrapper) > 0) {
            throw new IllegalArgumentException("字典代码已存在");
        }

        SystemDict systemDict = new SystemDict();
        BeanUtils.copyProperties(createSystemDictDTO, systemDict);

        this.save(systemDict);
        return systemDict;
    }

    @Override
    public SystemDict updateSystemDict(UpdateSystemDictDTO updateSystemDictDTO) {
        SystemDict existingDict = this.getById(updateSystemDictDTO.getId());
        if (existingDict == null) {
            throw new IllegalArgumentException("系统字典不存在");
        }

        // 检查字典代码是否已存在（排除当前记录）
        LambdaQueryWrapper<SystemDict> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemDict::getDictType, updateSystemDictDTO.getDictType())
                   .eq(SystemDict::getDictCode, updateSystemDictDTO.getDictCode())
                   .ne(SystemDict::getId, updateSystemDictDTO.getId());

        if (this.count(queryWrapper) > 0) {
            throw new IllegalArgumentException("字典代码已存在");
        }

        BeanUtils.copyProperties(updateSystemDictDTO, existingDict);
        this.updateById(existingDict);
        return existingDict;
    }

    @Override
    public void deleteSystemDict(Long id) {
        SystemDict systemDict = this.getById(id);
        if (systemDict == null) {
            throw new IllegalArgumentException("系统字典不存在");
        }

        this.removeById(id);
    }
}
