package com.vwatj.ppms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateProjectDTO;
import com.vwatj.ppms.dto.ProjectQueryDTO;
import com.vwatj.ppms.dto.UpdateProjectDTO;
import com.vwatj.ppms.entity.Document;
import com.vwatj.ppms.entity.Project;
import com.vwatj.ppms.entity.ProjectProcess;
import com.vwatj.ppms.entity.ProjectTask;
import com.vwatj.ppms.entity.SystemAsset;
import com.vwatj.ppms.enums.TaskStatusEnum;
import com.vwatj.ppms.exception.BusinessException;
import com.vwatj.ppms.mapper.DocumentMapper;
import com.vwatj.ppms.mapper.ProjectMapper;
import com.vwatj.ppms.mapper.ProjectTaskMapper;
import com.vwatj.ppms.service.ProjectProcessService;
import com.vwatj.ppms.service.ProjectService;
import com.vwatj.ppms.service.SystemAssetService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.RuntimeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectServiceImpl extends ServiceImpl<ProjectMapper, Project> implements ProjectService {

    private final ProjectMapper projectMapper;
    private final ProjectTaskMapper projectTaskMapper;
    private final DocumentMapper documentMapper;
    private final ProjectProcessService projectProcessService;
    private final RuntimeService runtimeService;
    private final SystemAssetService systemAssetService;

    @Override
    public PageResult<Project> getProjectPage(ProjectQueryDTO queryDTO) {
        Page<Project> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
        IPage<Project> result = projectMapper.selectProjectPage(page,
                queryDTO.getKeyword(),
                queryDTO.getCategory(),
                queryDTO.getStatus(),
                queryDTO.getPriority(),
                queryDTO.getManager(),
                queryDTO.getStarred(),
                queryDTO.getArchived(),
                queryDTO.getTags(),
                queryDTO.getStartDate(),
                queryDTO.getEndDate());
        return PageResult.from(result);
    }

    @Override
    public Project getProjectByName(String name) {
        return projectMapper.selectByName(name);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Project createProject(CreateProjectDTO createProjectDTO) {
        // 检查项目名称是否已存在
        if (getProjectByName(createProjectDTO.getName()) != null) {
            throw new RuntimeException("项目名称已存在");
        }

        Project project = new Project();
        BeanUtil.copyProperties(createProjectDTO, project);

        // 根据关联资产ID填充资产号和资产名称
        if (createProjectDTO.getRelatedAssetId() != null) {
            SystemAsset systemAsset = systemAssetService.getById(createProjectDTO.getRelatedAssetId());
            if (systemAsset == null) {
                throw new RuntimeException("关联的系统资产不存在");
            }
            project.setAssetNo(systemAsset.getAssetNo());
            project.setAssetName(systemAsset.getProjectName()); // 使用项目名称作为资产名称
        }

        // 设置默认值
        project.setStatus("request collect");
        project.setStarred(false);
        project.setArchived(false);
        project.setTotalTasks(0);
        project.setCompletedTasks(0);
        project.setTotalDocuments(0);

        // 确保数组字段不为 null
        if (project.getTags() == null) {
            project.setTags(new ArrayList<>());
        }
        if (project.getTeamMembers() == null) {
            project.setTeamMembers(new ArrayList<>());
        }

        save(project);

        // 根据项目分类初始化项目阶段
        if (createProjectDTO.getCategory() != null) {
            try {
                projectProcessService.initializeProjectStagesByCategory(project.getId(), createProjectDTO.getCategory());
                log.info("项目阶段初始化成功: projectId={}, category={}", project.getId(), createProjectDTO.getCategory());
            } catch (Exception e) {
                log.warn("初始化项目阶段失败: projectId={}, category={}", project.getId(), createProjectDTO.getCategory(), e);
                throw BusinessException.of("初始化项目阶段失败: " + e.getMessage());
            }
        }

        // 启动项目工作流（仅对新项目开发类型）
        if ("新项目开发".equals(createProjectDTO.getCategory())) {
            log.info("启动项目工作流: projectId={}, projectName={}", project.getId(), project.getName());

            // 准备流程变量
            Map<String, Object> variables = new HashMap<>();
            variables.put("projectId", project.getId());

            // 启动项目工作流
            String processInstanceId = runtimeService.startProcessInstanceByKey(
                    "project_new_flow",
                    "project_" + project.getId(),
                    variables
            ).getId();

            log.info("项目工作流启动成功: projectId={}, processInstanceId={}", project.getId(), processInstanceId);
        }
        // 启动项目工作流（仅对需求变更开发类型）
        else if ("需求变更".equals(createProjectDTO.getCategory())) {
            log.info("启动项目工作流: projectId={}, projectName={}", project.getId(), project.getName());

            // 准备流程变量
            Map<String, Object> variables = new HashMap<>();
            variables.put("projectId", project.getId());

            // 启动项目工作流
            String processInstanceId = runtimeService.startProcessInstanceByKey(
                    "project_cr_flow",
                    "project_" + project.getId(),
                    variables
            ).getId();

            log.info("项目工作流启动成功: projectId={}, processInstanceId={}", project.getId(), processInstanceId);
        }

        return project;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Project updateProject(UpdateProjectDTO updateProjectDTO) {
        Project project = getById(updateProjectDTO.getId());
        if (project == null) {
            throw new RuntimeException("项目不存在");
        }

        // 检查项目名称是否被其他项目使用
        if (updateProjectDTO.getName() != null) {
            Project existProject = getProjectByName(updateProjectDTO.getName());
            if (existProject != null && !existProject.getId().equals(updateProjectDTO.getId())) {
                throw new RuntimeException("项目名称已被其他项目使用");
            }
        }

        // 如果更新了关联资产ID，需要重新填充资产号和资产名称
        if (updateProjectDTO.getRelatedAssetId() != null &&
            !updateProjectDTO.getRelatedAssetId().equals(project.getRelatedAssetId())) {
            SystemAsset systemAsset = systemAssetService.getById(updateProjectDTO.getRelatedAssetId());
            if (systemAsset == null) {
                throw new RuntimeException("关联的系统资产不存在");
            }
            project.setAssetNo(systemAsset.getAssetNo());
            project.setAssetName(systemAsset.getProjectName()); // 使用项目名称作为资产名称
        }

        BeanUtil.copyProperties(updateProjectDTO, project, "id");
        updateById(project);
        return project;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProject(Long id) {
        Project project = getById(id);
        if (project == null) {
            throw new RuntimeException("项目不存在");
        }

        // 级联删除项目阶段
        try {
            List<ProjectProcess> stages = projectProcessService.getStagesByProjectId(id);
            if (!stages.isEmpty()) {
                // 使用逻辑删除
                for (ProjectProcess stage : stages) {
                    projectProcessService.removeById(stage.getId());
                }
                log.info("级联删除项目阶段成功: projectId={}, stageCount={}", id, stages.size());
            }
        } catch (Exception e) {
            log.error("级联删除项目阶段失败: projectId={}", id, e);
            throw new RuntimeException("删除项目阶段失败: " + e.getMessage(), e);
        }

        // 删除项目
        removeById(id);
        log.info("项目删除成功: projectId={}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleStar(Long id) {
        Project project = getById(id);
        if (project == null) {
            throw new RuntimeException("项目不存在");
        }
        boolean newStarredStatus = !project.getStarred();
        project.setStarred(newStarredStatus);
        updateById(project);
        return newStarredStatus;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleArchive(Long id) {
        Project project = getById(id);
        if (project == null) {
            throw new RuntimeException("项目不存在");
        }
        boolean newArchivedStatus = !project.getArchived();
        project.setArchived(newArchivedStatus);
        updateById(project);
        return newArchivedStatus;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(Long id, String status) {
        Project project = getById(id);
        if (project == null) {
            throw new RuntimeException("项目不存在");
        }
        project.setStatus(status);
        updateById(project);
    }

    @Override
    public Map<String, Object> getProjectStats() {
        Map<String, Object> stats = new HashMap<>();

        // 总项目数
        stats.put("total", count());

        // 按状态统计
        List<Map<String, Object>> statusStats = projectMapper.countProjectsByStatus();
        stats.put("byStatus", statusStats);

        // 按分类统计
        List<Map<String, Object>> categoryStats = projectMapper.countProjectsByCategory();
        stats.put("byCategory", categoryStats);

        // 按优先级统计
        List<Map<String, Object>> priorityStats = projectMapper.countProjectsByPriority();
        stats.put("byPriority", priorityStats);

        return stats;
    }

    @Override
    public List<String> getProjectMembers(Long id) {
        Project project = getById(id);
        if (project == null) {
            throw new RuntimeException("项目不存在");
        }
        return project.getTeamMembers();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProjectMembers(Long id, List<String> members) {
        Project project = getById(id);
        if (project == null) {
            throw new RuntimeException("项目不存在");
        }
        project.setTeamMembers(members);
        updateById(project);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Project duplicateProject(Long id, String name) {
        Project originalProject = getById(id);
        if (originalProject == null) {
            throw new RuntimeException("项目不存在");
        }

        // 检查新项目名称是否已存在
        if (getProjectByName(name) != null) {
            throw new RuntimeException("项目名称已存在");
        }

        Project newProject = new Project();
        BeanUtil.copyProperties(originalProject, newProject, "id", "createdAt", "updatedAt");
        newProject.setName(name);
        newProject.setStarred(false);
        newProject.setArchived(false);

        save(newProject);
        return newProject;
    }

    @Override
    public List<Project> getStarredProjects(String userId) {
        return projectMapper.selectStarredProjects(userId);
    }

    @Override
    public List<Project> getUserProjects(String userId) {
        return projectMapper.selectUserProjects(userId);
    }

    @Override
    public Map<String, Object> getProjectOverview(Long id) {
        Project project = getById(id);
        if (project == null) {
            throw new RuntimeException("项目不存在");
        }

        Map<String, Object> overview = new HashMap<>();

        // 项目基本信息（包含成员信息）
        overview.put("projectInfo", project);

        // 任务统计信息 - 直接查询数据库
        Map<String, Object> taskStats = new HashMap<>();
        Long totalTasks = projectTaskMapper.countTasksByProject(id);
        Long completedTasks = projectTaskMapper.countTasksByProjectAndStatus(id, TaskStatusEnum.CLOSE);
        Long inProgressTasks = projectTaskMapper.countTasksByProjectAndStatus(id, TaskStatusEnum.IN_PROGRESS);
        Long notStartedTasks = projectTaskMapper.countTasksByProjectAndStatus(id, TaskStatusEnum.OPEN);
        Long toVerifyTasks = projectTaskMapper.countTasksByProjectAndStatus(id, TaskStatusEnum.TO_VERIFY);

        taskStats.put("total", totalTasks);
        taskStats.put("completed", completedTasks);
        taskStats.put("inProgress", inProgressTasks);
        taskStats.put("notStarted", notStartedTasks);
        taskStats.put("toVerify", toVerifyTasks);
        overview.put("taskStats", taskStats);

        // 最近任务列表（取前5个）- 使用分页查询
        Page<ProjectTask> taskPage = new Page<>(1, 5);
        IPage<ProjectTask> recentTasks = projectTaskMapper.selectProjectTasks(taskPage, id);
        overview.put("taskList", recentTasks.getRecords());

        // 项目成员（从项目信息中获取，避免重复查询）
        overview.put("members", project.getTeamMembers());

        // 流程步骤信息
        overview.put("processSteps", projectProcessService.getStagesByProjectId(id));

        return overview;
    }

    @Override
    public Map<String, Object> getProjectTaskStats(Long id) {
        if (getById(id) == null) {
            throw new RuntimeException("项目不存在");
        }

        Map<String, Object> stats = new HashMap<>();

        // 直接查询数据库获取任务统计
        stats.put("total", projectTaskMapper.countTasksByProject(id));
        stats.put("completed", projectTaskMapper.countTasksByProjectAndStatus(id, TaskStatusEnum.CLOSE));
        stats.put("inProgress", projectTaskMapper.countTasksByProjectAndStatus(id, TaskStatusEnum.IN_PROGRESS));
        stats.put("notStarted", projectTaskMapper.countTasksByProjectAndStatus(id, TaskStatusEnum.OPEN));
        stats.put("toVerify", projectTaskMapper.countTasksByProjectAndStatus(id, TaskStatusEnum.TO_VERIFY));

        return stats;
    }

    @Override
    public List<Map<String, Object>> getProjectTasks(Long id) {
        if (getById(id) == null) {
            throw new RuntimeException("项目不存在");
        }

        // 使用分页查询获取项目任务列表，默认获取前20个
        Page<ProjectTask> page = new Page<>(1, 20);
        IPage<ProjectTask> taskPage = projectTaskMapper.selectProjectTasks(page, id);

        // 转换为Map格式返回
        List<Map<String, Object>> tasks = new java.util.ArrayList<>();
        for (ProjectTask projectTask : taskPage.getRecords()) {
            Map<String, Object> taskMap = new HashMap<>();
            taskMap.put("id", projectTask.getId());
            taskMap.put("title", projectTask.getTitle());
            taskMap.put("issueType", projectTask.getIssueType());
            taskMap.put("assignee", projectTask.getAssignee());
            taskMap.put("reporter", projectTask.getReporter());
            taskMap.put("description", projectTask.getDescription());
            taskMap.put("status", projectTask.getStatus());
            taskMap.put("priority", projectTask.getPriority());
            taskMap.put("plannedStartTime", projectTask.getPlannedStartTime());
            taskMap.put("plannedEndTime", projectTask.getPlannedEndTime());
            taskMap.put("actualStartTime", projectTask.getActualStartTime());
            taskMap.put("actualEndTime", projectTask.getActualEndTime());
            taskMap.put("duration", projectTask.getDuration());
            tasks.add(taskMap);
        }

        return tasks;
    }

    @Override
    public List<Map<String, Object>> getProjectDocuments(Long id) {
        if (getById(id) == null) {
            throw new RuntimeException("项目不存在");
        }

        // 使用分页查询获取项目文档列表，默认获取前20个
        Page<Document> page = new Page<>(1, 20);
        IPage<Document> documentPage = documentMapper.selectProjectDocuments(page, id);

        // 转换为Map格式返回
        List<Map<String, Object>> documents = new java.util.ArrayList<>();
        for (Document document : documentPage.getRecords()) {
            Map<String, Object> docMap = new HashMap<>();
            docMap.put("id", document.getId());
            docMap.put("name", document.getTitle()); // 使用title字段作为name
            docMap.put("title", document.getTitle());
            docMap.put("updateTime", document.getUpdatedAt());
            docMap.put("uploadTime", document.getCreatedAt());
            docMap.put("size", formatFileSize(document.getFileSize())); // 格式化文件大小
            docMap.put("uploader", document.getAuthorName() != null ? document.getAuthorName() : document.getAuthor());
            docMap.put("author", document.getAuthor());
            docMap.put("category", document.getCategory());
            docMap.put("phase", document.getCategory()); // 使用category作为phase
            docMap.put("avatar", ""); // 默认头像，可以后续从用户信息中获取
            documents.add(docMap);
        }

        return documents;
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(Long fileSize) {
        if (fileSize == null || fileSize == 0) {
            return "0 B";
        }

        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = fileSize.doubleValue();

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return String.format("%.1f %s", size, units[unitIndex]);
    }
}
