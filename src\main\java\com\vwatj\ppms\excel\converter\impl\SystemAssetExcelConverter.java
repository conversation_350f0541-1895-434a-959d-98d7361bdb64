package com.vwatj.ppms.excel.converter.impl;

import com.vwatj.ppms.entity.SystemAsset;
import com.vwatj.ppms.excel.converter.ExcelDataConverter;
import com.vwatj.ppms.excel.converter.ExcelDataConvertException;
import com.vwatj.ppms.excel.util.ExcelUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Map;

/**
 * 系统资产Excel数据转换器
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public class SystemAssetExcelConverter implements ExcelDataConverter<SystemAsset> {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public Object[] entityToRowData(SystemAsset entity) {
        return new Object[]{
            entity.getAssetNo(),
            entity.getProjectName(),
            entity.getShortCode(),
            entity.getCiName(),
            entity.getBusinessDepartment(),
            entity.getPmOwner(),
            entity.getDescription(),
            entity.getAssetState(),
            entity.getRegion(),
            formatDate(entity.getRetireDate()),
            entity.getAppId(),
            formatDate(entity.getOnlineDate()),
            entity.getMail(),
            entity.getVersion(),
            entity.getLanguage(),
            entity.getUserCount(),
            entity.getUsageStatus(),
            entity.getBusinessUser(),
            formatDateTime(entity.getCreatedAt()),
            formatDateTime(entity.getUpdatedAt())
        };
    }

    @Override
    public SystemAsset rowDataToEntity(Map<String, Object> rowData, int rowIndex) throws ExcelDataConvertException {
        // 验证必填字段
        validateRequiredFields(rowData, rowIndex);
        
        SystemAsset asset = new SystemAsset();
        
        try {
            // 必填字段
            asset.setAssetNo(getRequiredString(rowData, "资产编号", rowIndex));
            
            // 可选字段
            asset.setProjectName(getString(rowData, "项目名称"));
            asset.setShortCode(getString(rowData, "短代码"));
            asset.setCiName(getString(rowData, "CI名称"));
            asset.setBusinessDepartment(getString(rowData, "业务部门"));
            asset.setPmOwner(getString(rowData, "PM负责人"));
            asset.setDescription(getString(rowData, "描述"));
            asset.setAssetState(getString(rowData, "资产状态"));
            asset.setRegion(getString(rowData, "区域"));
            asset.setAppId(getString(rowData, "应用ID"));
            asset.setMail(getString(rowData, "邮箱"));
            asset.setVersion(getString(rowData, "版本"));
            asset.setLanguage(getString(rowData, "语言"));
            asset.setUsageStatus(getString(rowData, "使用状态"));
            asset.setBusinessUser(getString(rowData, "业务用户"));
            
            // 日期字段
            asset.setRetireDate(parseDate(rowData, "退役日期"));
            asset.setOnlineDate(parseDate(rowData, "上线日期"));
            
            // 数值字段
            asset.setUserCount(ExcelUtils.parseInteger(rowData.get("用户数量")));

            return asset;
            
        } catch (Exception e) {
            if (e instanceof ExcelDataConvertException) {
                throw e;
            }
            throw new ExcelDataConvertException(rowIndex, "数据转换失败: " + e.getMessage());
        }
    }

    @Override
    public String[] getExportHeaders() {
        return new String[]{
            "资产编号", "项目名称", "短代码", "CI名称", "业务部门", "PM负责人",
            "描述", "资产状态", "区域", "退役日期", "应用ID", "上线日期",
            "邮箱", "版本", "语言", "用户数量", "使用状态", "业务用户",
            "创建时间", "更新时间"
        };
    }

    @Override
    public String[] getTemplateHeaders() {
        return new String[]{
            "资产编号*", "项目名称", "短代码", "CI名称", "业务部门", "PM负责人",
            "描述", "资产状态", "区域", "退役日期", "应用ID", "上线日期",
            "邮箱", "版本", "语言", "用户数量", "使用状态", "业务用户"
        };
    }

    @Override
    public String getUniqueField() {
        return "assetNo";
    }

    @Override
    public Object getUniqueFieldValue(SystemAsset entity) {
        return entity.getAssetNo();
    }

    @Override
    public int[] getColumnWidths() {
        return new int[]{
            15, // 资产编号
            25, // 项目名称
            15, // 短代码
            20, // CI名称
            15, // 业务部门
            15, // PM负责人
            35, // 描述
            12, // 资产状态
            12, // 区域
            15, // 退役日期
            15, // 应用ID
            15, // 上线日期
            25, // 邮箱
            12, // 版本
            12, // 语言
            12, // 用户数量
            15, // 使用状态
            15, // 业务用户
            20, // 创建时间
            20  // 更新时间
        };
    }

    // 辅助方法
    private String formatDate(LocalDate date) {
        return date != null ? date.format(DATE_FORMATTER) : "";
    }

    private String formatDateTime(java.time.LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "";
    }

    private String getRequiredString(Map<String, Object> rowData, String fieldName, int rowIndex) 
            throws ExcelDataConvertException {
        String value = getString(rowData, fieldName);
        if (value == null || value.trim().isEmpty()) {
            throw new ExcelDataConvertException(rowIndex, fieldName, "必填字段不能为空");
        }
        return value.trim();
    }

    private String getString(Map<String, Object> rowData, String fieldName) {
        Object value = rowData.get(fieldName);
        return value != null && !value.toString().trim().isEmpty() ? value.toString().trim() : null;
    }

    private LocalDate parseDate(Map<String, Object> rowData, String fieldName) {
        Object value = rowData.get(fieldName);
        if (value == null) {
            return null;
        }
        
        String dateStr = value.toString().trim();
        if (dateStr.isEmpty()) {
            return null;
        }
        
        try {
            // 尝试多种日期格式
            DateTimeFormatter[] formatters = {
                DateTimeFormatter.ofPattern("yyyy-MM-dd"),
                DateTimeFormatter.ofPattern("yyyy/MM/dd"),
                DateTimeFormatter.ofPattern("MM/dd/yyyy"),
                DateTimeFormatter.ofPattern("dd/MM/yyyy")
            };
            
            for (DateTimeFormatter formatter : formatters) {
                try {
                    return LocalDate.parse(dateStr, formatter);
                } catch (DateTimeParseException ignored) {
                    // 继续尝试下一个格式
                }
            }
            
            throw new RuntimeException("无法解析日期格式: " + dateStr);
            
        } catch (Exception e) {
            throw new RuntimeException(fieldName + "格式错误: " + e.getMessage());
        }
    }
}
