package com.vwatj.ppms.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.DocumentQueryDTO;
import com.vwatj.ppms.dto.UpdateDocumentDTO;
import com.vwatj.ppms.dto.UploadDocumentDTO;
import com.vwatj.ppms.entity.Document;

import java.util.List;
import java.util.Map;

/**
 * 文档服务接口
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface DocumentService extends IService<Document> {
    
    /**
     * 分页查询文档
     */
    PageResult<Document> getDocumentPage(DocumentQueryDTO queryDTO);
    
    /**
     * 查询项目文档
     */
    PageResult<Document> getProjectDocuments(Long projectId, DocumentQueryDTO queryDTO);
    
    /**
     * 根据标题查询文档
     */
    Document getDocumentByTitle(String title, Long projectId);
    
    /**
     * 上传文档
     */
    Document uploadDocument(UploadDocumentDTO uploadDocumentDTO);
    
    /**
     * 更新文档信息
     */
    Document updateDocument(UpdateDocumentDTO updateDocumentDTO);
    
    /**
     * 删除文档
     */
    void deleteDocument(String id);
    
    /**
     * 下载文档
     */
    Map<String, String> downloadDocument(String id);
    
    /**
     * 预览文档
     */
    Map<String, String> previewDocument(String id);

    /**
     * 发布文档
     */
    void publishDocument(String id);
    
    /**
     * 归档文档
     */
    void archiveDocument(String id);
    
    /**
     * 获取文档版本历史
     */
    List<Map<String, Object>> getDocumentVersions(String id);
    
    /**
     * 获取文档统计信息
     */
    Map<String, Object> getDocumentStats(Long projectId);
    
    /**
     * 批量删除文档
     */
    void batchDeleteDocuments(List<String> ids);
    
    /**
     * 复制文档
     */
    Document duplicateDocument(String id, String title, Long projectId);
    
    /**
     * 移动文档到其他项目
     */
    void moveDocument(String id, Long targetProjectId);
    
    /**
     * 增加文档查看次数
     */
    void incrementViewCount(String id);
    
    /**
     * 增加文档下载次数
     */
    void incrementDownloadCount(String id);

    /**
     * 处理表单中的文件
     * 将临时文件转移到正式目录并添加到文档数据表
     *
     * @param projectId 项目ID
     * @param stageKey 阶段Key
     * @param formKey 表单Key
     * @param formData 表单数据
     * @param operation 操作类型
     * @return 处理后的文档信息列表
     */
    List<Map<String, Object>> processFormFiles(Long projectId, String stageKey, String formKey,
                                               JSONObject formData, String operation);

    /**
     * 检查表单数据中是否包含文件
     *
     * @param formData 表单数据
     * @return 是否包含文件
     */
    boolean containsFiles(JSONObject formData);

    /**
     * 从表单数据中提取文件信息
     *
     * @param formData 表单数据
     * @return 文件信息列表
     */
    List<Map<String, Object>> extractFileInfo(JSONObject formData);

    /**
     * 将临时文件转移到正式目录
     *
     * @param projectId 项目ID
     * @param tempFilePath 临时文件路径
     * @param fileName 文件名
     * @return 正式文件路径
     */
    String moveFileFromTempToProject(Long projectId, String tempFilePath, String fileName);

    /**
     * 添加表单文档记录到数据库
     *
     * @param projectId 项目ID
     * @param stageKey 阶段Key
     * @param formKey 表单Key
     * @param filePath 文件路径
     * @param fileName 文件名
     * @param fileSize 文件大小
     * @param operation 操作类型
     * @return 文档ID
     */
    Long addFormDocumentRecord(Long projectId, String stageKey, String formKey,
                              String filePath, String fileName, Long fileSize, String operation);

    /**
     * 处理表单文件变化（包括新增、删除）
     * 比较当前文件列表与原有文件列表，处理删除的文件
     *
     * @param projectId 项目ID
     * @param stageKey 阶段Key
     * @param formKey 表单Key
     * @param currentFormData 当前提交的表单数据
     * @param previousFormData 提交前的表单数据（用于对比变化）
     * @param operation 操作类型
     * @return 处理后的文档信息列表
     */
    List<Map<String, Object>> processFormFilesWithChanges(Long projectId, String stageKey, String formKey,
                                                          JSONObject currentFormData,
                                                          JSONObject previousFormData,
                                                          String operation);

    /**
     * 删除文档记录和文件
     *
     * @param documentId 文档ID
     */
    void deleteDocumentAndFile(Long documentId);
}
