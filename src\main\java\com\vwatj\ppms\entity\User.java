package com.vwatj.ppms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.vwatj.ppms.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户实体类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
public class User extends BaseEntity {
    
    /**
     * 用户ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 密码
     */
    private String password;
    
    /**
     * 头像
     */
    private String avatar;
    
    /**
     * 全名
     */
    private String fullName;
    
    /**
     * 角色
     */
    private String role;
    
    /**
     * 部门
     */
    private String department;
    
    /**
     * 职位
     */
    private String position;
    
    /**
     * 电话
     */
    private String phone;
    
    /**
     * 是否激活
     */
    private Boolean isActive;
    
    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginAt;
}
