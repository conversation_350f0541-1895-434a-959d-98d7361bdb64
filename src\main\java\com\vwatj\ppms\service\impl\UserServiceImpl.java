package com.vwatj.ppms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateUserDTO;
import com.vwatj.ppms.dto.UpdateUserDTO;
import com.vwatj.ppms.dto.UserQueryDTO;
import com.vwatj.ppms.entity.User;
import com.vwatj.ppms.mapper.UserMapper;
import com.vwatj.ppms.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
    
    private final UserMapper userMapper;
    
    @Override
    public PageResult<User> getUserPage(UserQueryDTO queryDTO) {
        Page<User> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
        IPage<User> result = userMapper.selectUserPage(page, 
            queryDTO.getKeyword(), 
            queryDTO.getRole(), 
            queryDTO.getDepartment(), 
            queryDTO.getIsActive());
        return PageResult.from(result);
    }
    
    @Override
    public User getUserByUsername(String username) {
        return userMapper.selectByUsername(username);
    }
    
    @Override
    public User getUserByEmail(String email) {
        return userMapper.selectByEmail(email);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public User createUser(CreateUserDTO createUserDTO) {
        // 检查用户名是否已存在
        if (getUserByUsername(createUserDTO.getUsername()) != null) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (getUserByEmail(createUserDTO.getEmail()) != null) {
            throw new RuntimeException("邮箱已存在");
        }
        
        User user = new User();
        BeanUtil.copyProperties(createUserDTO, user);
        
        // 加密密码
        user.setPassword(BCrypt.hashpw(createUserDTO.getPassword()));
        user.setIsActive(true);
        
        save(user);
        return user;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public User updateUser(UpdateUserDTO updateUserDTO) {
        User user = getById(updateUserDTO.getId());
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 检查邮箱是否被其他用户使用
        if (StrUtil.isNotBlank(updateUserDTO.getEmail())) {
            User existUser = getUserByEmail(updateUserDTO.getEmail());
            if (existUser != null && !existUser.getId().equals(updateUserDTO.getId())) {
                throw new RuntimeException("邮箱已被其他用户使用");
            }
        }
        
        BeanUtil.copyProperties(updateUserDTO, user, "id");
        updateById(user);
        return user;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUser(String id) {
        User user = getById(id);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        removeById(id);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void toggleUserStatus(String id) {
        User user = getById(id);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        user.setIsActive(!user.getIsActive());
        updateById(user);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetPassword(String userId, String newPassword) {
        User user = getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        user.setPassword(BCrypt.hashpw(newPassword));
        updateById(user);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changePassword(String userId, String oldPassword, String newPassword) {
        User user = getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 验证旧密码
        if (!BCrypt.checkpw(oldPassword, user.getPassword())) {
            throw new RuntimeException("原密码不正确");
        }
        
        user.setPassword(BCrypt.hashpw(newPassword));
        updateById(user);
    }

    @Override
    public List<Map<String, String>> getUserOptions() {
        // 获取所有激活的用户
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getIsActive, true)
               .orderByAsc(User::getFullName);

        List<User> users = list(wrapper);
        List<Map<String, String>> options = new ArrayList<>();

        for (User user : users) {
            Map<String, String> option = new HashMap<>();
            option.put("label", user.getFullName());
            option.put("value", user.getFullName());
            options.add(option);
        }

        return options;
    }
}
