package com.vwatj.ppms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.vwatj.ppms.common.BaseEntity;
import com.vwatj.ppms.config.TimestampToLocalDateTimeDeserializer;
import com.vwatj.ppms.enums.TaskStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 任务实体类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("task")
public class ProjectTask extends BaseEntity {
    
    /**
     * 任务ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务编号
     */
    private String taskNo;

    /**
     * 任务标题
     */
    private String title;
    
    /**
     * 问题类型 (task, qa issue, qa cr, uat issue, uat cr, ma cr, ma issue)
     */
    private String issueType;
    
    /**
     * 指派人
     */
    private String assignee;
    
    /**
     * 指派人头像
     */
    private String assigneeAvatar;
    
    /**
     * 报告人
     */
    private String reporter;
    
    /**
     * 报告人头像
     */
    private String reporterAvatar;
    
    /**
     * 任务描述
     */
    private String description;
    
    /**
     * 任务状态 (open, doing, toverify, done)
     */
    private TaskStatusEnum status;
    
    /**
     * 计划开始时间
     */
    @JsonDeserialize(using = TimestampToLocalDateTimeDeserializer.class)
    private LocalDateTime plannedStartTime;

    /**
     * 计划结束时间
     */
    @JsonDeserialize(using = TimestampToLocalDateTimeDeserializer.class)
    private LocalDateTime plannedEndTime;

    /**
     * 实际开始时间
     */
    @JsonDeserialize(using = TimestampToLocalDateTimeDeserializer.class)
    private LocalDateTime actualStartTime;

    /**
     * 实际结束时间
     */
    @JsonDeserialize(using = TimestampToLocalDateTimeDeserializer.class)
    private LocalDateTime actualEndTime;
    
    /**
     * 时长(小时)
     */
    private BigDecimal duration;
    
    /**
     * 优先级 (low, medium, high, urgent)
     */
    private String priority;
    
    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 阶段名称
     */
    private String stageName;
}
