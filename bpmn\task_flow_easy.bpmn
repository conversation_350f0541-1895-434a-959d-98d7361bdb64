<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1xj2zxl" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.37.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.23.0">
  <bpmn:process id="task_flow" name="task_flow" isExecutable="true" camunda:historyTimeToLive="0">
    <bpmn:startEvent id="StartEvent_1" name="start">
      <bpmn:outgoing>Flow_1ukwdjn</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_StartTask" sourceRef="OpenTask" targetRef="Gateway_0qvhhf1" />
    <bpmn:sequenceFlow id="Flow_1ukwdjn" sourceRef="StartEvent_1" targetRef="Event_0lkqwtz" />
    <bpmn:sequenceFlow id="Flow_0qc4fpj" name="Resolve" sourceRef="Gateway_0qvhhf1" targetRef="Event_1rt4h0j">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "resolve"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_08jabdx" name="Close" sourceRef="Gateway_0qvhhf1" targetRef="Event_0jijpb8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "close"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="OpenTask" name="Operation Task" camunda:formKey="task-operation">
      <bpmn:incoming>Flow_0d06vsh</bpmn:incoming>
      <bpmn:incoming>Flow_1jpliop</bpmn:incoming>
      <bpmn:incoming>Flow_1eglmkb</bpmn:incoming>
      <bpmn:incoming>Flow_069d02z</bpmn:incoming>
      <bpmn:outgoing>Flow_StartTask</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0d06vsh" sourceRef="Event_0lkqwtz" targetRef="OpenTask" />
    <bpmn:sequenceFlow id="Flow_1jpliop" sourceRef="Event_1rt4h0j" targetRef="OpenTask" />
    <bpmn:exclusiveGateway id="Gateway_0qvhhf1">
      <bpmn:incoming>Flow_StartTask</bpmn:incoming>
      <bpmn:outgoing>Flow_0qc4fpj</bpmn:outgoing>
      <bpmn:outgoing>Flow_08jabdx</bpmn:outgoing>
      <bpmn:outgoing>Flow_0bk5tkc</bpmn:outgoing>
      <bpmn:outgoing>Flow_1mhfora</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1eglmkb" sourceRef="Event_01j1e9e" targetRef="OpenTask" />
    <bpmn:sequenceFlow id="Flow_0bk5tkc" sourceRef="Gateway_0qvhhf1" targetRef="Event_01j1e9e">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "progress"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_069d02z" sourceRef="Event_0jijpb8" targetRef="OpenTask" />
    <bpmn:sequenceFlow id="Flow_1mhfora" sourceRef="Gateway_0qvhhf1" targetRef="Event_0lkqwtz">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "reopen"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:intermediateThrowEvent id="Event_0lkqwtz" name="OPEN" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{taskBoundaryEventListener}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>open</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1mhfora</bpmn:incoming>
      <bpmn:incoming>Flow_1ukwdjn</bpmn:incoming>
      <bpmn:outgoing>Flow_0d06vsh</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateThrowEvent id="Event_01j1e9e" name="IN PROGRESS">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{taskBoundaryEventListener}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>inprogress</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0bk5tkc</bpmn:incoming>
      <bpmn:outgoing>Flow_1eglmkb</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateThrowEvent id="Event_1rt4h0j" name="TO VERIFY">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{taskBoundaryEventListener}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>toverify</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0qc4fpj</bpmn:incoming>
      <bpmn:outgoing>Flow_1jpliop</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateThrowEvent id="Event_0jijpb8" name="CLOSED" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{taskBoundaryEventListener}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>close</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_08jabdx</bpmn:incoming>
      <bpmn:outgoing>Flow_069d02z</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="task_flow">
      <bpmndi:BPMNShape id="StartEvent_1_di" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="359" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="159" y="402" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0qvhhf1_di" bpmnElement="Gateway_0qvhhf1" isMarkerVisible="true">
        <dc:Bounds x="546" y="352" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_01r7vpk_di" bpmnElement="OpenTask">
        <dc:Bounds x="380" y="337" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1iwg9x9" bpmnElement="Event_1rt4h0j">
        <dc:Bounds x="412" y="172" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="401" y="148" width="58" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_12ywj8s" bpmnElement="Event_0jijpb8">
        <dc:Bounds x="682" y="359" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="677" y="402" width="46" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0f9jz25" bpmnElement="Event_01j1e9e">
        <dc:Bounds x="412" y="542" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="324" y="553" width="78" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0es7yrm" bpmnElement="Event_0lkqwtz">
        <dc:Bounds x="282" y="359" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="284" y="335" width="32" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1ukwdjn_di" bpmnElement="Flow_1ukwdjn">
        <di:waypoint x="188" y="377" />
        <di:waypoint x="282" y="377" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_069d02z_di" bpmnElement="Flow_069d02z">
        <di:waypoint x="700" y="359" />
        <di:waypoint x="700" y="80" />
        <di:waypoint x="330" y="80" />
        <di:waypoint x="330" y="360" />
        <di:waypoint x="380" y="360" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mhfora_di" bpmnElement="Flow_1mhfora">
        <di:waypoint x="571" y="402" />
        <di:waypoint x="571" y="620" />
        <di:waypoint x="300" y="620" />
        <di:waypoint x="300" y="395" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0d06vsh_di" bpmnElement="Flow_0d06vsh">
        <di:waypoint x="318" y="377" />
        <di:waypoint x="380" y="377" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_StartTask_di" bpmnElement="Flow_StartTask">
        <di:waypoint x="480" y="377" />
        <di:waypoint x="546" y="377" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="337" y="222" width="46" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qc4fpj_di" bpmnElement="Flow_0qc4fpj">
        <di:waypoint x="571" y="352" />
        <di:waypoint x="571" y="190" />
        <di:waypoint x="448" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="490" y="172" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08jabdx_di" bpmnElement="Flow_08jabdx">
        <di:waypoint x="596" y="377" />
        <di:waypoint x="682" y="377" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="624" y="359" width="29" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bk5tkc_di" bpmnElement="Flow_0bk5tkc">
        <di:waypoint x="571" y="402" />
        <di:waypoint x="571" y="560" />
        <di:waypoint x="448" y="560" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jpliop_di" bpmnElement="Flow_1jpliop">
        <di:waypoint x="430" y="208" />
        <di:waypoint x="430" y="337" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1eglmkb_di" bpmnElement="Flow_1eglmkb">
        <di:waypoint x="430" y="542" />
        <di:waypoint x="430" y="417" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
