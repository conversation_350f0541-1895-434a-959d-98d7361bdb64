package com.vwatj.ppms.entity;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.vwatj.ppms.config.typehandler.JSONObjectTypeHandler;
import com.vwatj.ppms.enums.FormTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 流程表单实体类
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("process_form")
public class ProcessForm {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 表单Key（唯一标识）
     */
    @TableField("form_key")
    private String formKey;

    /**
     * 表单名称
     */
    @TableField("form_name")
    private String formName;

    /**
     * 排序号
     */
    @TableField("order_num")
    private Integer orderNum;

    /**
     * 表单结构JSON配置
     */
    @TableField(value = "form_struct", typeHandler = JSONObjectTypeHandler.class)
    private JSONObject formStruct;

    /**
     * 表单描述
     */
    @TableField("description")
    private String description;

    /**
     * 是否启用
     */
    @TableField("enabled")
    private Boolean enabled;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField("deleted")
    @TableLogic
    private Boolean deleted;

    /**
     * 表单类型：0通用表单 1自定义表单（前端动态组件）
     */
    @TableField("form_type")
    private FormTypeEnum formType;
}
