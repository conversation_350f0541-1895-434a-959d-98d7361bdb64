package com.vwatj.ppms.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 创建Agent DTO
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
public class CreateAgentDTO {
    
    /**
     * Agent名称
     */
    @NotBlank(message = "Agent名称不能为空")
    @Size(min = 2, max = 50, message = "Agent名称长度必须在2-50个字符之间")
    private String name;
    
    /**
     * Agent描述
     */
    @Size(max = 200, message = "描述不能超过200个字符")
    private String description;
    
    /**
     * Agent状态 (0-禁用, 1-启用)
     */
    @NotNull(message = "Agent状态不能为空")
    private Integer status;
    
    /**
     * Agent类型
     */
    private String type;
    
    /**
     * Agent配置信息 (JSON格式)
     */
    private String config;
}
