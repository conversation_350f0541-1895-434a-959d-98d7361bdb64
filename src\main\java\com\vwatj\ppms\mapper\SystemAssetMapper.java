package com.vwatj.ppms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.vwatj.ppms.entity.SystemAsset;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 系统资产Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Mapper
public interface SystemAssetMapper extends BaseMapper<SystemAsset> {
    
    /**
     * 分页查询系统资产
     */
    IPage<SystemAsset> selectSystemAssetPage(Page<SystemAsset> page,
                                           @Param("keyword") String keyword,
                                           @Param("region") String region,
                                           @Param("businessDepartment") String businessDepartment,
                                           @Param("assetState") String assetState,
                                           @Param("pmOwner") String pmOwner);
    
    /**
     * 根据资产编号查询系统资产
     */
    SystemAsset selectByAssetNo(@Param("assetNo") String assetNo);
    
    /**
     * 根据项目名称查询系统资产
     */
    SystemAsset selectByProjectName(@Param("projectName") String projectName);
    
    /**
     * 根据短代码查询系统资产
     */
    SystemAsset selectByShortCode(@Param("shortCode") String shortCode);
}
