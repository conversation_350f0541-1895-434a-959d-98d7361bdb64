package com.vwatj.ppms.excel.converter;

/**
 * Excel数据转换异常
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public class ExcelDataConvertException extends Exception {

    private final int rowIndex;
    private final String fieldName;
    private final String errorMessage;

    public ExcelDataConvertException(int rowIndex, String fieldName, String errorMessage) {
        super(String.format("第%d行，字段[%s]：%s", rowIndex, fieldName, errorMessage));
        this.rowIndex = rowIndex;
        this.fieldName = fieldName;
        this.errorMessage = errorMessage;
    }

    public ExcelDataConvertException(int rowIndex, String errorMessage) {
        this(rowIndex, "数据", errorMessage);
    }

    public int getRowIndex() {
        return rowIndex;
    }

    public String getFieldName() {
        return fieldName;
    }

    public String getErrorMessage() {
        return errorMessage;
    }
}
