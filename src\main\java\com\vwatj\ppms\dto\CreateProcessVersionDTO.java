package com.vwatj.ppms.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotNull;

/**
 * 创建流程版本DTO
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
public class CreateProcessVersionDTO {

    /**
     * 流程定义ID
     */
    @NotNull(message = "流程定义ID不能为空")
    private Long processDefinitionId;

    /**
     * 版本描述
     */
    private String description;

    /**
     * BPMN文件
     */
    @NotNull(message = "BPMN文件不能为空")
    private MultipartFile bpmnFile;
}
