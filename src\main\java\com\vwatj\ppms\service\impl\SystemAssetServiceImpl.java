package com.vwatj.ppms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateSystemAssetDTO;
import com.vwatj.ppms.dto.SystemAssetQueryDTO;
import com.vwatj.ppms.dto.UpdateSystemAssetDTO;
import com.vwatj.ppms.entity.SystemAsset;
import com.vwatj.ppms.excel.generate.SystemAssetExcelGenerate;
import com.vwatj.ppms.mapper.SystemAssetMapper;
import com.vwatj.ppms.service.SystemAssetService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.*;

/**
 * 系统资产服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SystemAssetServiceImpl extends ServiceImpl<SystemAssetMapper, SystemAsset> implements SystemAssetService {

    private final SystemAssetMapper systemAssetMapper;
    
    @Override
    public PageResult<SystemAsset> getSystemAssetPage(SystemAssetQueryDTO queryDTO) {
        Page<SystemAsset> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
        IPage<SystemAsset> result = systemAssetMapper.selectSystemAssetPage(page,
            queryDTO.getKeyword(),
            queryDTO.getRegion(),
            queryDTO.getBusinessDepartment(),
            queryDTO.getAssetState(),
            queryDTO.getPmOwner());
        return PageResult.from(result);
    }
    
    @Override
    public SystemAsset getSystemAssetByAssetNo(String assetNo) {
        return systemAssetMapper.selectByAssetNo(assetNo);
    }
    
    @Override
    public SystemAsset getSystemAssetByProjectName(String projectName) {
        return systemAssetMapper.selectByProjectName(projectName);
    }
    
    @Override
    public SystemAsset getSystemAssetByShortCode(String shortCode) {
        return systemAssetMapper.selectByShortCode(shortCode);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SystemAsset createSystemAsset(CreateSystemAssetDTO createSystemAssetDTO) {
        // 检查资产编号是否已存在
        if (getSystemAssetByAssetNo(createSystemAssetDTO.getAssetNo()) != null) {
            throw new RuntimeException("资产编号已存在");
        }
        
        // 检查项目名称是否已存在（只有当项目名称不为空时才检查）
        if (createSystemAssetDTO.getProjectName() != null &&
            !createSystemAssetDTO.getProjectName().trim().isEmpty() &&
            getSystemAssetByProjectName(createSystemAssetDTO.getProjectName()) != null) {
            throw new RuntimeException("项目名称已存在");
        }

        // 检查短代码是否已存在（只有当短代码不为空时才检查）
        if (createSystemAssetDTO.getShortCode() != null &&
            !createSystemAssetDTO.getShortCode().trim().isEmpty() &&
            getSystemAssetByShortCode(createSystemAssetDTO.getShortCode()) != null) {
            throw new RuntimeException("短代码已存在");
        }
        
        SystemAsset systemAsset = new SystemAsset();
        BeanUtil.copyProperties(createSystemAssetDTO, systemAsset);
        
        save(systemAsset);
        return systemAsset;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SystemAsset updateSystemAsset(UpdateSystemAssetDTO updateSystemAssetDTO) {
        SystemAsset systemAsset = getById(updateSystemAssetDTO.getId());
        if (systemAsset == null) {
            throw new RuntimeException("系统资产不存在");
        }
        
        // 检查资产编号是否被其他资产使用
        if (updateSystemAssetDTO.getAssetNo() != null) {
            SystemAsset existAsset = getSystemAssetByAssetNo(updateSystemAssetDTO.getAssetNo());
            if (existAsset != null && !existAsset.getId().equals(updateSystemAssetDTO.getId())) {
                throw new RuntimeException("资产编号已被其他资产使用");
            }
        }
        
        // 检查项目名称是否被其他资产使用（只有当项目名称不为空时才检查）
        if (updateSystemAssetDTO.getProjectName() != null &&
            !updateSystemAssetDTO.getProjectName().trim().isEmpty()) {
            SystemAsset existAsset = getSystemAssetByProjectName(updateSystemAssetDTO.getProjectName());
            if (existAsset != null && !existAsset.getId().equals(updateSystemAssetDTO.getId())) {
                throw new RuntimeException("项目名称已被其他资产使用");
            }
        }

        // 检查短代码是否被其他资产使用（只有当短代码不为空时才检查）
        if (updateSystemAssetDTO.getShortCode() != null &&
            !updateSystemAssetDTO.getShortCode().trim().isEmpty()) {
            SystemAsset existAsset = getSystemAssetByShortCode(updateSystemAssetDTO.getShortCode());
            if (existAsset != null && !existAsset.getId().equals(updateSystemAssetDTO.getId())) {
                throw new RuntimeException("短代码已被其他资产使用");
            }
        }
        
        BeanUtil.copyProperties(updateSystemAssetDTO, systemAsset, "id");
        updateById(systemAsset);
        return systemAsset;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSystemAsset(Long id) {
        SystemAsset systemAsset = getById(id);
        if (systemAsset == null) {
            throw new RuntimeException("系统资产不存在");
        }
        removeById(id);
    }

    @Override
    public Resource generateTemplate() {
        SystemAssetExcelGenerate systemAssetExcelGenerate = new SystemAssetExcelGenerate(this);
        return systemAssetExcelGenerate.generateTemplate();
    }

    @Override
    public Resource exportAssets(SystemAssetQueryDTO queryDTO) {
        SystemAssetExcelGenerate systemAssetExcelGenerate = new SystemAssetExcelGenerate(this);
        return systemAssetExcelGenerate.exportData(queryDTO);
    }

    @Override
    public SseEmitter importAssets(MultipartFile file, String mode) {
        SystemAssetExcelGenerate systemAssetExcelGenerate = new SystemAssetExcelGenerate(this);
        return systemAssetExcelGenerate.importDataWithSSE(file, mode);
    }


}
