image: 10.120.140.98/pcc.proxy-image/alpine:3.13.5

#部署前准备

#自定义编译Dockerfile和下面的参数
#需要先建立k8s namespace 如果是需pvc模板 ，手动建立pvc，没有不需要
#namespace  group缩写-projectName-test 或者 prod

#触发逻辑是
#1、test部署方式：test分支 不需要打标签 直接提交到test触发 latest部署 ，但是我还没改完这块，不会更新覆盖镜像，需要自己手动重启拉取镜像
#2、prod部署方式：master/main分支 需要打标签，才会触发部署
variables:
  LOCATION:
    description: atj 天津 atd 大连 ics 苏州 ac 安徽
    value: atj
  SERVER_TYPE:
    description: 无调用外网接口 office 调用外网接口 dmz （多offic少用dmz）
    value: office
  # K8S模板可选：
  #     a.存储文件+通用（包括Springboot/前端/Go/Python其他语言）
  #     https://gitlab.vwatj.ap.vwg/cloud-native/devops-docker/-/raw/master/devops-temp/k8s/deploy-default-need-pvc-template-v1.yaml
  #
  #     此选项有存储路径，对应是 /data 会自动挂载到pvc上，需要先手动建立${LOCATION}-${PROJECT_NAME}-pvc
  #     b.无存储+通用 deploy-default-template.yaml
  #     https://gitlab.vwatj.ap.vwg/cloud-native/devops-docker/-/raw/master/devops-temp/k8s/deploy-default-template-v1.yaml
  #
  #     c.自定义模板，需要基于提供的模板去改，命名规范是 deploy-{project}-template-{版本号，例如v1}.yaml
  #     需要去 https://gitlab.vwatj.ap.vwg/cloud-native/devops-docker/-/tree/master/devops-temp/k8s 上传模板
  #
  # 提示，a和b的K8S模板里，给Pod运行默认提供了3个环境变量，各个项目可自行读取来区别
  # BUILD_ENV： 对应部署方式 test/prod
  # LOCATION: 基于上面你提供的参数
  # SPRING_PROFILES_ACTIVE: 基于上面2个参数拼装  ${LOCATION}-${BUILD-ENV}
  K8S_TEMPLATE_NAME:
    description: deploy-default-template-v1 or deploy-default-need-pvc-template-v1
    options:
      - deploy-default-template-v1.yaml
      - deploy-default-need-pvc-template-v1.yaml
    value: deploy-default-template-v1.yaml
  PROJECT_PORT:
    description: 运行端口
    value: 8080
  # 安全自检路径
  # a.其他语言: /
  # b.springboot 是/api/actuator/health
  SECURITY_CHECK:
    description: 安全自检路径 a.其他语言 "/" b.springboot "/api/actuator/health"
    value: /api/health
  BUILD_PATH:
    description: 编译产物路径，写好了提供给Dockerfile用来作为运行程序 例如前端是 dist/ 后端是 target/
    value: target/
  MOUNT_PATH:
    description: 如果需要挂载，这块是必填，默认/data
    value: /data
  BUILD_AUTO:
    description: 是否需要支持test分支自动打包
    value: "true"
  IMAGE_PULL_POLICY:
    description: 镜像拉取策略（Always、IfNotPresent、Never），建议prod用Always，开发可用IfNotPresent
    value: Always
  # K8S资源限制相关变量，供模板动态引用
  LIMITS_CPU:
    description: k8s limits.cpu，最大可用CPU，例如 1
    value: "1"
  LIMITS_MEMORY:
    description: k8s limits.memory，最大可用内存，例如 512Mi
    value: "1024Mi"
  REQUESTS_CPU:
    description: k8s requests.cpu，初始分配CPU，例如 100m
    value: "100m"
  REQUESTS_MEMORY:
    description: k8s requests.memory，初始分配内存，例如 128Mi
    value: "128Mi"


#固定模板
# gitlab模板可选：https://gitlab.vwatj.ap.vwg/cloud-native/devops-docker/-/blob/master/README.md?ref_type=heads

include:
  - remote: 'https://gitlab.vwatj.ap.vwg/cloud-native/devops-docker/-/raw/master/devops-temp/gitlab/maven/.gitlab-ci-maven-openjdk17.yaml'
