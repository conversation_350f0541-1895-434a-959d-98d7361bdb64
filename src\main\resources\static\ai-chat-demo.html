<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPMS AI Assistant Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .chat-messages {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            max-height: 500px;
        }

        .message {
            margin-bottom: 1rem;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message.assistant {
            justify-content: flex-start;
        }

        .message-content {
            max-width: 70%;
            padding: 0.75rem 1rem;
            border-radius: 18px;
            word-wrap: break-word;
        }

        .message.user .message-content {
            background: #007bff;
            color: white;
        }

        .message.assistant .message-content {
            background: #f1f3f4;
            color: #333;
        }

        .chat-input {
            padding: 1rem;
            border-top: 1px solid #e0e0e0;
            display: flex;
            gap: 0.5rem;
        }

        .input-field {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 20px;
            outline: none;
            font-size: 14px;
        }

        .send-button {
            padding: 0.75rem 1.5rem;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .send-button:hover {
            background: #0056b3;
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .quick-actions {
            padding: 1rem;
            border-top: 1px solid #e0e0e0;
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .quick-action {
            padding: 0.5rem 1rem;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }

        .quick-action:hover {
            background: #e9ecef;
            transform: translateY(-1px);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 1rem;
            color: #666;
        }

        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 0.75rem;
            border-radius: 5px;
            margin: 0.5rem 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 PPMS AI Assistant</h1>
        <p>您的智能项目管理助手</p>
    </div>

    <div class="chat-container">
        <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
                <div class="message-content">
                    您好！我是PPMS AI助手，可以帮助您管理项目、任务，提供数据分析和智能建议。请问有什么可以帮助您的吗？
                </div>
            </div>
        </div>

        <div class="loading" id="loading">
            <div>AI正在思考中...</div>
        </div>

        <div class="quick-actions">
            <div class="quick-action" onclick="sendQuickMessage('查看所有项目')">查看所有项目</div>
            <div class="quick-action" onclick="sendQuickMessage('项目统计信息')">项目统计</div>
            <div class="quick-action" onclick="sendQuickMessage('未完成的任务')">未完成任务</div>
            <div class="quick-action" onclick="sendQuickMessage('分析项目1')">分析项目1</div>
            <div class="quick-action" onclick="sendQuickMessage('生成项目报告')">生成报告</div>
        </div>

        <div class="chat-input">
            <input type="text" class="input-field" id="messageInput" placeholder="输入您的问题..." onkeypress="handleKeyPress(event)">
            <button class="send-button" id="sendButton" onclick="sendMessage()">发送</button>
        </div>
    </div>

    <script>
        let sessionId = 'web-session-' + Date.now();
        let currentProjectId = null;

        function addMessage(content, isUser = false) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'assistant'}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.innerHTML = content.replace(/\n/g, '<br>');
            
            messageDiv.appendChild(contentDiv);
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
            document.getElementById('sendButton').disabled = show;
        }

        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            document.getElementById('chatMessages').appendChild(errorDiv);
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            addMessage(message, true);
            input.value = '';
            showLoading(true);

            try {
                const response = await fetch('/ai/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        sessionId: sessionId,
                        userId: 'web-user',
                        projectId: currentProjectId,
                        enableFunctionCalling: true
                    })
                });

                const data = await response.json();
                
                if (data.code === 200) {
                    addMessage(data.data.message);
                    
                    // 显示建议操作
                    if (data.data.suggestedActions && data.data.suggestedActions.length > 0) {
                        let suggestions = '<br><strong>建议操作：</strong><br>';
                        data.data.suggestedActions.forEach(action => {
                            suggestions += `• ${action.description}<br>`;
                        });
                        addMessage(suggestions);
                    }
                } else {
                    showError('请求失败: ' + data.message);
                }
            } catch (error) {
                console.error('Error:', error);
                showError('网络错误，请稍后重试');
            } finally {
                showLoading(false);
            }
        }

        function sendQuickMessage(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('PPMS AI Assistant 已加载');
            
            // 检查AI服务状态
            fetch('/ai/status')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        console.log('AI服务状态:', data.data);
                    }
                })
                .catch(error => {
                    console.warn('无法获取AI服务状态:', error);
                });
        });
    </script>
</body>
</html>
