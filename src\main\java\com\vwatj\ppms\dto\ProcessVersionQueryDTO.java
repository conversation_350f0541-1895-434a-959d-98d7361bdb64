package com.vwatj.ppms.dto;

import lombok.Data;

/**
 * 流程版本查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
public class ProcessVersionQueryDTO {

    /**
     * 页码
     */
    private Long page = 1L;
    
    /**
     * 每页大小
     */
    private Long pageSize = 10L;
    
    /**
     * 排序字段
     */
    private String sortBy;
    
    /**
     * 排序方向
     */
    private String sortOrder;

    /**
     * 流程定义ID
     */
    private Long processDefinitionId;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 版本状态
     */
    private String status;

    /**
     * 创建人
     */
    private String createdBy;
}
