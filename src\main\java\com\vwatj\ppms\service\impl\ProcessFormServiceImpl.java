package com.vwatj.ppms.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vwatj.ppms.exception.BusinessException;
import com.vwatj.ppms.entity.ProcessForm;
import com.vwatj.ppms.mapper.ProcessFormMapper;
import com.vwatj.ppms.service.ProcessFormService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 流程表单服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProcessFormServiceImpl extends ServiceImpl<ProcessFormMapper, ProcessForm> implements ProcessFormService {

    private final ObjectMapper objectMapper;

    @Override
    public ProcessForm getByFormKey(String formKey) {
        if (formKey == null || formKey.trim().isEmpty()) {
            throw BusinessException.of("表单Key不能为空");
        }
        return baseMapper.selectByFormKey(formKey);
    }

    @Override
    public List<ProcessForm> getByFormKeys(List<String> formKeys) {
        if (formKeys == null || formKeys.isEmpty()) {
            throw BusinessException.of("表单Key列表不能为空");
        }
        return baseMapper.selectByFormKeys(formKeys);
    }



    @Override
    public List<ProcessForm> getAllEnabled() {
        return baseMapper.selectAllEnabled();
    }



    @Override
    public JSONObject getFormStruct(String formKey) {
        ProcessForm form = getByFormKey(formKey);
        return form != null ? form.getFormStruct() : null;
    }



    @Override
    public boolean existsByFormKey(String formKey) {
        return getByFormKey(formKey) != null;
    }
}
