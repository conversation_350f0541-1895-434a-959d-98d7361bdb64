package com.vwatj.ppms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.vwatj.ppms.entity.Document;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 文档Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Mapper
public interface DocumentMapper extends BaseMapper<Document> {
    
    /**
     * 分页查询文档
     */
    IPage<Document> selectDocumentPage(Page<Document> page,
                                     @Param("keyword") String keyword,
                                     @Param("projectId") Long projectId,
                                     @Param("category") String category,
                                     @Param("status") String status,
                                     @Param("fileType") String fileType,
                                     @Param("author") String author,
                                     @Param("reviewer") String reviewer,
                                     @Param("approver") String approver,
                                     @Param("tags") List<String> tags,
                                     @Param("uploadDateStart") LocalDateTime uploadDateStart,
                                     @Param("uploadDateEnd") LocalDateTime uploadDateEnd);
    
    /**
     * 查询项目文档
     */
    IPage<Document> selectProjectDocuments(Page<Document> page, @Param("projectId") Long projectId);
    
    /**
     * 根据标题查询文档
     */
    Document selectByTitle(@Param("title") String title, @Param("projectId") Long projectId);
    
    /**
     * 统计文档数量按状态
     */
    List<Map<String, Object>> countDocumentsByStatus(@Param("projectId") Long projectId);
    
    /**
     * 统计文档数量按类别
     */
    List<Map<String, Object>> countDocumentsByCategory(@Param("projectId") Long projectId);
    
    /**
     * 统计文档数量按文件类型
     */
    List<Map<String, Object>> countDocumentsByFileType(@Param("projectId") Long projectId);
    
    /**
     * 统计文档总大小
     */
    Long sumDocumentFileSize(@Param("projectId") Long projectId);
    
    /**
     * 统计文档总查看次数
     */
    Long sumDocumentViewCount(@Param("projectId") Long projectId);
    
    /**
     * 统计文档总下载次数
     */
    Long sumDocumentDownloadCount(@Param("projectId") Long projectId);
    
    /**
     * 增加文档查看次数
     */
    void incrementViewCount(@Param("id") String id);
    
    /**
     * 增加文档下载次数
     */
    void incrementDownloadCount(@Param("id") String id);
}
