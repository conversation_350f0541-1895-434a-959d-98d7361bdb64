package com.vwatj.ppms.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 创建流程定义DTO
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
public class CreateProcessDefinitionDTO {

    /**
     * 流程Key
     */
    @NotBlank(message = "流程Key不能为空")
    private String processKey;

    /**
     * 流程名称
     */
    @NotBlank(message = "流程名称不能为空")
    private String name;

    /**
     * 流程描述
     */
    private String description;

    /**
     * 版本描述
     */
    private String versionDescription;

    /**
     * BPMN文件
     */
    @NotNull(message = "BPMN文件不能为空")
    private MultipartFile bpmnFile;
}
