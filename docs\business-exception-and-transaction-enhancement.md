# 业务异常和事务完整性增强总结

## 修改概述

根据您的要求，实现了以下重要功能：

1. **updateTask支持状态更新**: 复用updateTaskStatus逻辑
2. **事务完整性保证**: 任务更新失败就报错，确保事务回滚
3. **BusinessException自定义异常**: 统一业务异常处理
4. **全局400错误处理**: 自动将BusinessException转换为400响应

## 详细实现内容

### 1. updateTask方法增强

#### 新增功能
- ✅ **状态更新支持**: 检测状态变更并触发工作流流转
- ✅ **智能操作推导**: 自动推导状态变更对应的操作名
- ✅ **完整事务控制**: 任何步骤失败都会回滚整个事务
- ✅ **异常处理优化**: 使用BusinessException统一异常处理

#### 处理逻辑
```java
@Transactional(rollbackFor = Exception.class)
public ProjectTask updateTask(UpdateTaskDTO updateTaskDTO) {
    // 1. 参数验证
    if (updateTaskDTO.getId() == null) {
        throw BusinessException.of("任务ID不能为空");
    }
    
    // 2. 任务存在性检查
    ProjectTask projectTask = getById(updateTaskDTO.getId());
    if (projectTask == null) {
        throw BusinessException.of("任务不存在");
    }
    
    // 3. 业务规则验证（任务编号重复检查）
    
    // 4. 更新非状态字段
    BeanUtil.copyProperties(updateTaskDTO, projectTask, "id", "status");
    boolean updateResult = updateById(projectTask);
    if (!updateResult) {
        throw BusinessException.of("任务更新失败");
    }
    
    // 5. 状态流转处理（如果状态发生变更）
    if (statusChanged) {
        String operation = deduceOperation(oldStatus, newStatus);
        if (operation != null) {
            updateTaskStatus(taskId, newStatus, "状态更新", operation);
        } else {
            throw BusinessException.of("无法推导操作名进行状态流转");
        }
    }
    
    // 6. 返回最新状态
    return getById(taskId);
}
```

### 2. BusinessException自定义异常

#### 异常类设计
```java
public class BusinessException extends RuntimeException {
    private String code;
    private String message;
    
    // 多种构造方法
    public BusinessException(String message)
    public BusinessException(String code, String message)
    public BusinessException(String message, Throwable cause)
    public BusinessException(String code, String message, Throwable cause)
    
    // 便捷创建方法
    public static BusinessException of(String message)
    public static BusinessException of(String code, String message)
    // ...
}
```

#### 使用场景
- **参数验证失败**: `BusinessException.of("任务ID不能为空")`
- **业务规则违反**: `BusinessException.of("任务编号已存在")`
- **资源不存在**: `BusinessException.of("任务不存在")`
- **操作不允许**: `BusinessException.of("无法推导操作名进行状态流转")`

### 3. 全局异常处理器增强

#### 异常处理层次
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    // 1. 业务异常 -> 400 Bad Request
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Object> handleBusinessException(BusinessException e)
    
    // 2. 参数异常 -> 400 Bad Request  
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Object> handleIllegalArgumentException(IllegalArgumentException e)
    
    // 3. 参数校验异常 -> 400 Bad Request
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Object> handleMethodArgumentNotValidException(MethodArgumentNotValidException e)
    
    // 4. 运行时异常 -> 500 Internal Server Error
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Object> handleRuntimeException(RuntimeException e)
    
    // 5. 系统异常 -> 500 Internal Server Error
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Object> handleException(Exception e)
}
```

### 4. 事务完整性保证

#### 事务边界
- **方法级事务**: `@Transactional(rollbackFor = Exception.class)`
- **异常回滚**: 任何Exception都会触发事务回滚
- **嵌套事务**: updateTask调用updateTaskStatus时保持事务一致性

#### 失败场景处理
1. **数据库更新失败**: `updateById()`返回false时抛出异常
2. **工作流流转失败**: 工作流异常会传播并回滚整个事务
3. **业务规则违反**: 立即抛出BusinessException并回滚
4. **参数验证失败**: 事务开始前就抛出异常

### 5. 代码统一性改进

#### 异常处理统一
将项目中所有的业务异常统一使用BusinessException：

**修改前**:
```java
throw new RuntimeException("任务不存在");
throw new IllegalArgumentException("任务ID不能为空");
```

**修改后**:
```java
throw BusinessException.of("任务不存在");
throw BusinessException.of("任务ID不能为空");
```

#### 涉及的方法
- `createTask()` - 创建任务时的验证
- `updateTask()` - 更新任务时的验证和处理
- `deleteTask()` - 删除任务时的验证
- `updateTaskStatus()` - 状态更新时的验证
- `assignTask()` - 分配任务时的验证
- `getTaskStatusTransitions()` - 获取状态流转时的验证
- 工作流相关方法的异常处理

## 使用示例

### 前端调用示例

#### 成功响应
```javascript
// 更新任务（包含状态变更）
const response = await fetch('/api/tasks/123', {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        id: 123,
        title: '更新后的标题',
        status: 'inprogress',  // 状态变更
        projectId: 1
    })
});

// 200 OK
{
    "success": true,
    "data": {
        "id": 123,
        "title": "更新后的标题",
        "status": "inprogress",  // 工作流更新后的状态
        "actualStartTime": "2025-07-17T10:30:00"  // 自动设置的时间戳
    },
    "message": "操作成功"
}
```

#### 失败响应
```javascript
// 400 Bad Request - 业务异常
{
    "success": false,
    "data": null,
    "message": "无法推导操作名进行状态流转: close -> open"
}

// 400 Bad Request - 参数异常
{
    "success": false,
    "data": null,
    "message": "任务ID不能为空"
}

// 500 Internal Server Error - 系统异常
{
    "success": false,
    "data": null,
    "message": "系统内部错误: 数据库连接失败"
}
```

## 优势总结

### 1. 事务完整性
- **原子性**: 整个更新操作要么全部成功，要么全部回滚
- **一致性**: 数据库状态和工作流状态始终保持一致
- **隔离性**: 并发操作不会相互影响
- **持久性**: 成功的操作会持久化到数据库

### 2. 异常处理统一
- **分层处理**: 业务异常、参数异常、系统异常分别处理
- **响应统一**: 所有异常都转换为标准的ApiResponse格式
- **日志记录**: 不同级别的异常有相应的日志记录
- **用户友好**: 业务异常返回用户可理解的错误信息

### 3. 开发体验
- **类型安全**: BusinessException提供类型安全的异常处理
- **便捷创建**: 静态方法简化异常创建
- **调试友好**: 详细的异常信息和堆栈跟踪
- **维护性**: 统一的异常处理逻辑便于维护

### 4. 系统健壮性
- **故障隔离**: 业务异常不会导致系统崩溃
- **快速失败**: 参数验证失败时立即返回错误
- **资源保护**: 事务回滚避免数据不一致
- **监控友好**: 统一的异常处理便于监控和告警

现在您的系统具备了完整的事务控制和统一的异常处理机制，确保了数据一致性和用户体验！
