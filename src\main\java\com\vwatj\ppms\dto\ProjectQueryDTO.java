package com.vwatj.ppms.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
public class ProjectQueryDTO {
    
    /**
     * 页码
     */
    private Long page = 1L;
    
    /**
     * 每页大小
     */
    private Long pageSize = 10L;
    
    /**
     * 排序字段
     */
    private String sortBy;
    
    /**
     * 排序方向
     */
    private String sortOrder;
    
    /**
     * 关键词
     */
    private String keyword;
    
    /**
     * 项目分类
     */
    private String category;
    
    /**
     * 项目状态
     */
    private String status;
    
    /**
     * 项目优先级
     */
    private String priority;
    
    /**
     * 项目经理
     */
    private String manager;
    
    /**
     * 是否关注
     */
    private Boolean starred;
    
    /**
     * 是否归档
     */
    private Boolean archived;
    
    /**
     * 标签列表
     */
    private List<String> tags;
    
    /**
     * 开始时间
     */
    private LocalDateTime startDate;
    
    /**
     * 结束时间
     */
    private LocalDateTime endDate;
}
