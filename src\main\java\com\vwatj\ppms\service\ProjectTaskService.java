package com.vwatj.ppms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateTaskDTO;
import com.vwatj.ppms.dto.TaskQueryDTO;
import com.vwatj.ppms.dto.UpdateTaskDTO;
import com.vwatj.ppms.entity.ProjectTask;
import com.vwatj.ppms.enums.TaskStatusEnum;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.Map;

/**
 * 任务服务接口
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface ProjectTaskService extends IService<ProjectTask> {
    
    /**
     * 分页查询任务
     */
    PageResult<ProjectTask> getTaskPage(TaskQueryDTO queryDTO);
    
    /**
     * 查询项目任务
     */
    PageResult<ProjectTask> getProjectTasks(Long projectId, TaskQueryDTO queryDTO);
    
    /**
     * 创建任务
     */
    ProjectTask createTask(CreateTaskDTO createTaskDTO);
    
    /**
     * 更新任务
     */
    ProjectTask updateTask(UpdateTaskDTO updateTaskDTO);
    
    /**
     * 删除任务
     */
    void deleteTask(Long id);
    
    /**
     * 更新任务状态
     */
    void updateTaskStatus(Long id, TaskStatusEnum status, String comment);

    /**
     * 更新任务状态（支持操作名）
     * @param id 任务ID
     * @param status 目标状态
     * @param comment 备注
     * @param operation 操作名（可选，存在时进行工作流流转）
     */
    void updateTaskStatus(Long id, TaskStatusEnum status, String comment, String operation);

    /**
     * 分配任务
     */
    void assignTask(Long id, String assignee);

    /**
     * 获取任务统计信息
     */
    Map<String, Object> getTaskStats(Long projectId);
    
    /**
     * 获取我的任务
     */
    PageResult<ProjectTask> getMyTasks(String assignee, TaskQueryDTO queryDTO);

    /**
     * 高级搜索任务（支持所有筛选条件）
     */
    PageResult<ProjectTask> searchTasks(TaskQueryDTO queryDTO);

    /**
     * 生成任务导入模板
     * @return Excel文件资源
     */
    org.springframework.core.io.Resource generateTemplate();

    /**
     * 导出任务数据
     * @param queryDTO 查询条件
     * @return Excel文件资源
     */
    org.springframework.core.io.Resource exportTasks(TaskQueryDTO queryDTO);

    /**
     * 导入任务（SSE方式，带项目名称）
     * @param file Excel文件
     * @param mode 导入模式：incremental(增量) 或 overwrite(覆盖)
     * @param projectId 项目ID（可选，用于指定导入任务的项目）
     * @param projectName 项目名称（可选，用于设置导入任务的项目名称）
     * @return SSE发射器
     */
    SseEmitter importTasks(org.springframework.web.multipart.MultipartFile file, String mode, Long projectId, String projectName);

    /**
     * 获取任务状态流转路径
     * @param taskId 任务ID
     * @return 当前状态下可用的操作和目标状态映射
     */
    Map<String, TaskStatusEnum> getTaskStatusTransitions(Long taskId);

    /**
     * 获取指定状态的可用操作
     * @param status 当前状态
     * @return 可用操作列表
     */
    List<Map<String, Object>> getAvailableOperations(String status);

    /**
     * 根据状态变化推导操作名
     * @param oldTaskStatusEnum 原状态
     * @param newTaskStatusEnum 新状态
     * @return 操作名，如果无法推导则返回null
     */
    String deduceOperation(TaskStatusEnum oldTaskStatusEnum, TaskStatusEnum newTaskStatusEnum);


}
