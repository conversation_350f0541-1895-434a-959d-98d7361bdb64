package com.vwatj.ppms.entity;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.vwatj.ppms.common.BaseEntity;
import com.vwatj.ppms.config.typehandler.JSONObjectTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.Map;

/**
 * 系统资产实体类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "system_asset", autoResultMap = true)
public class SystemAsset extends BaseEntity {
    
    /**
     * 系统资产ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 资产编号
     */
    private String assetNo;
    
    /**
     * 项目名称
     */
    private String projectName;
    
    /**
     * 短代码
     */
    private String shortCode;
    
    /**
     * CI名称
     */
    private String ciName;

    /**
     * 业务部门
     */
    private String businessDepartment;

    /**
     * PM负责人
     */
    private String pmOwner;

    /**
     * 描述
     */
    private String description;

    /**
     * 资产状态
     */
    private String assetState;

    /**
     * 区域
     */
    private String region;

    /**
     * 退役日期
     */
    private LocalDate retireDate;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 上线日期
     */
    private LocalDate onlineDate;

    /**
     * 邮箱
     */
    private String mail;

    /**
     * 版本
     */
    private String version;

    /**
     * 语言
     */
    private String language;

    /**
     * 用户数量
     */
    private Integer userCount;

    /**
     * 使用状态
     */
    private String usageStatus;

    /**
     * 业务用户
     */
    private String businessUser;

    /**
     * 额外属性 (JSON格式存储)
     * 存储其他动态配置信息
     */
    @com.baomidou.mybatisplus.annotation.TableField(value = "extra_properties", typeHandler = JSONObjectTypeHandler.class)
    private JSONObject extraProperties;
}
