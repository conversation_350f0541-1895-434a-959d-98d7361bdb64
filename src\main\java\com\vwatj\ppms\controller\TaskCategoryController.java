package com.vwatj.ppms.controller;

import com.vwatj.ppms.common.ApiResponse;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateTaskCategoryDTO;
import com.vwatj.ppms.dto.TaskCategoryQueryDTO;
import com.vwatj.ppms.dto.UpdateTaskCategoryDTO;
import com.vwatj.ppms.entity.ProjectTaskCategory;
import com.vwatj.ppms.service.ProjectTaskCategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 任务分类控制器
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@RestController
@RequestMapping("/task-categories")
@RequiredArgsConstructor
public class TaskCategoryController {

    private final ProjectTaskCategoryService projectTaskCategoryService;

    /**
     * 获取任务分类选项列表
     */
    @GetMapping("/options")
    public ApiResponse<List<Map<String, String>>> getTaskCategoryOptions() {
        return ApiResponse.success(projectTaskCategoryService.getTaskCategoryOptions());
    }

    /**
     * 分页查询任务分类
     */
    @GetMapping("/page")
    public ApiResponse<PageResult<ProjectTaskCategory>> getTaskCategoryPage(TaskCategoryQueryDTO queryDTO) {
        PageResult<ProjectTaskCategory> result = projectTaskCategoryService.getTaskCategoryPage(queryDTO);
        return ApiResponse.success(result);
    }

    /**
     * 根据ID查询任务分类
     */
    @GetMapping("/{id}")
    public ApiResponse<ProjectTaskCategory> getTaskCategory(@PathVariable Long id) {
        ProjectTaskCategory projectTaskCategory = projectTaskCategoryService.getById(id);
        if (projectTaskCategory == null) {
            return ApiResponse.notFound("任务分类不存在");
        }
        return ApiResponse.success(projectTaskCategory);
    }

    /**
     * 创建任务分类
     */
    @PostMapping
    public ApiResponse<ProjectTaskCategory> createTaskCategory(@Validated @RequestBody CreateTaskCategoryDTO createTaskCategoryDTO) {
        try {
            ProjectTaskCategory projectTaskCategory = projectTaskCategoryService.createTaskCategory(createTaskCategoryDTO);
            return ApiResponse.success("任务分类创建成功", projectTaskCategory);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 更新任务分类
     */
    @PutMapping("/{id}")
    public ApiResponse<ProjectTaskCategory> updateTaskCategory(@PathVariable Long id, @Validated @RequestBody UpdateTaskCategoryDTO updateTaskCategoryDTO) {
        try {
            updateTaskCategoryDTO.setId(id);
            ProjectTaskCategory projectTaskCategory = projectTaskCategoryService.updateTaskCategory(updateTaskCategoryDTO);
            return ApiResponse.success("任务分类更新成功", projectTaskCategory);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 删除任务分类
     */
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteTaskCategory(@PathVariable Long id) {
        try {
            projectTaskCategoryService.deleteTaskCategory(id);
            return ApiResponse.success("任务分类删除成功");
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
}
