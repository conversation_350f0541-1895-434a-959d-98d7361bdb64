package com.vwatj.ppms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateProjectCategoryDTO;
import com.vwatj.ppms.dto.ProjectCategoryQueryDTO;
import com.vwatj.ppms.dto.UpdateProjectCategoryDTO;
import com.vwatj.ppms.entity.ProjectCategory;

import java.util.List;
import java.util.Map;

/**
 * 项目分类服务接口
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public interface ProjectCategoryService extends IService<ProjectCategory> {

    /**
     * 获取项目分类选项列表
     * @return 选项列表
     */
    List<Map<String, String>> getProjectCategoryOptions();

    /**
     * 分页查询项目分类
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<ProjectCategory> getProjectCategoryPage(ProjectCategoryQueryDTO queryDTO);

    /**
     * 创建项目分类
     * @param createProjectCategoryDTO 创建参数
     * @return 创建的项目分类
     */
    ProjectCategory createProjectCategory(CreateProjectCategoryDTO createProjectCategoryDTO);

    /**
     * 更新项目分类
     * @param updateProjectCategoryDTO 更新参数
     * @return 更新的项目分类
     */
    ProjectCategory updateProjectCategory(UpdateProjectCategoryDTO updateProjectCategoryDTO);

    /**
     * 删除项目分类
     * @param id 分类ID
     */
    void deleteProjectCategory(Long id);
}
