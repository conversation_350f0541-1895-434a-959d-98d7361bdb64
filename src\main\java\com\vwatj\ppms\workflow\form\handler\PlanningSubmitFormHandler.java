package com.vwatj.ppms.workflow.form.handler;

import com.vwatj.ppms.entity.Project;
import com.vwatj.ppms.entity.ProjectProcess;
import com.vwatj.ppms.exception.BusinessException;
import com.vwatj.ppms.service.ProjectProcessService;
import com.vwatj.ppms.service.ProjectService;
import com.vwatj.ppms.workflow.form.FormEventHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 项目计划排期表单处理器
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@Component
public class PlanningSubmitFormHandler implements FormEventHandler {

    @Resource
    private ProjectProcessService projectProcessService;

    @Resource
    private ProjectService projectService;

    @Override
    public String getSupportedFormKey() {
        return "planning_submit_form";
    }

    @Override
    public void handleFormSubmit(Long projectId, String stageKey, String formKey, Map<String, Object> formData, String operation) {
        log.info("处理项目计划排期表单: projectId={}, stageKey={}, operation={}", projectId, stageKey, operation);

        switch (operation) {
            case "submit":
                handlePlanningSubmit(projectId, formData);
                break;
            case "approve":
                handlePlanningApprove(projectId, formData);
                break;
            case "reject":
                handlePlanningReject(projectId, formData);
                break;
            default:
                log.warn("未知的操作类型: {}", operation);
        }
    }

    @Override
    public void handleFormEdit(Long projectId, String stageKey, String formKey, Map<String, Object> formData) {
        log.info("处理项目计划排期表单编辑: projectId={}, stageKey={}", projectId, stageKey);

        // 项目计划编辑特定的业务逻辑
        validatePlanningData(formData);

        // 实时更新项目进度数据
        updateProjectProcessData(projectId, formData);
    }

    @Override
    public void handleFormUpdate(Long projectId, String stageKey, String formKey,
                                 Map<String, Object> formData, Map<String, Object> previousFormData) {
        log.info("处理项目计划排期表单更新: projectId={}, stageKey={}", projectId, stageKey);

        // 验证新的表单数据
        validatePlanningData(formData);

        // 检查关键字段是否发生变化
        boolean datesChanged = checkIfDatesChanged(formData, previousFormData);
        boolean milestonesChanged = checkIfMilestonesChanged(formData, previousFormData);

        // 更新项目进度数据
        updateProjectProcessData(projectId, formData);

        // 如果项目日期发生变化，更新项目的开始结束日期
        if (datesChanged) {
            updateProjectDates(projectId, formData);
            log.info("项目日期发生变化，已更新项目基本信息: projectId={}", projectId);
        }

        // 如果里程碑发生变化，发送通知
        if (milestonesChanged) {
            sendMilestoneChangeNotification(projectId, formData, previousFormData);
            log.info("项目里程碑发生变化，已发送通知: projectId={}", projectId);
        }

        log.info("项目计划排期表单更新处理完成: projectId={}, datesChanged={}, milestonesChanged={}",
                projectId, datesChanged, milestonesChanged);
    }

    @Override
    public void validateFormData(Long projectId, String stageKey, String formKey, Map<String, Object> formData, String operation) {
        log.debug("验证项目计划排期表单数据");
        validatePlanningData(formData);
    }

    @Override
    public void afterFormSubmit(Long projectId, String stageKey, String formKey, Map<String, Object> formData, String operation) {
        log.info("项目计划排期表单后置处理: projectId={}, operation={}", projectId, operation);

        if ("submit".equals(operation)) {
            // 发送计划提交通知
            sendPlanningSubmitNotification(projectId, formData);
        } else if ("approve".equals(operation)) {
            // 发送计划批准通知
            sendPlanningApproveNotification(projectId, formData);
        }
    }

    /**
     * 处理项目计划提交
     */
    private void handlePlanningSubmit(Long projectId, Map<String, Object> formData) {
        log.info("处理项目计划提交: projectId={}", projectId);

        // 更新项目流程数据和项目开始结束日期
        updateProjectProcessData(projectId, formData);
        updateProjectDates(projectId, formData);
    }

    /**
     * 处理项目计划批准
     */
    private void handlePlanningApprove(Long projectId, Map<String, Object> formData) {
        log.info("处理项目计划批准: projectId={}", projectId);
        throw new BusinessException("不支持批准操作");
    }

    /**
     * 处理项目计划拒绝
     */
    private void handlePlanningReject(Long projectId, Map<String, Object> formData) {
        log.info("处理项目计划拒绝: projectId={}", projectId);
        throw new BusinessException("不支持拒绝操作");
    }

    /**
     * 验证项目计划数据
     */
    private void validatePlanningData(Map<String, Object> formData) {
        log.debug("验证项目计划数据: {}", formData);

        // 验证项目整体时间
        Object projectStartTime = formData.get("projectStartTime");
        Object projectEndTime = formData.get("projectEndTime");

        if (projectStartTime == null) {
            throw new RuntimeException("项目计划开始时间不能为空");
        }

        if (projectEndTime == null) {
            throw new RuntimeException("项目计划结束时间不能为空");
        }

        // 验证阶段时间
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> stages = (List<Map<String, Object>>) formData.get("stages");
        if (stages != null) {
            for (Map<String, Object> stage : stages) {
                Object plannedStartTime = stage.get("plannedStartTime");
                Object plannedEndTime = stage.get("plannedEndTime");

                if (plannedStartTime == null || plannedEndTime == null) {
                    String stageName = (String) stage.get("stageName");
                    throw new RuntimeException("阶段 " + stageName + " 的计划时间不能为空");
                }
            }
        }
    }

    /**
     * 更新项目流程数据
     */
    private void updateProjectProcessData(Long projectId, Map<String, Object> formData) {
        log.info("更新项目流程数据: projectId={}", projectId);

        @SuppressWarnings("unchecked")
        List<Map<String, Object>> stages = (List<Map<String, Object>>) formData.get("stages");

        if (stages != null) {
            for (Map<String, Object> stageData : stages) {
                String stageKey = (String) stageData.get("stageKey");
                Object plannedStartTime = stageData.get("plannedStartTime");
                Object plannedEndTime = stageData.get("plannedEndTime");
                Object actualStartTime = stageData.get("actualStartTime");
                Object actualEndTime = stageData.get("actualEndTime");
                String remarks = (String) stageData.get("remarks");

                // 查找对应的项目流程记录
                List<ProjectProcess> processes = projectProcessService.getStagesByProjectId(projectId);
                ProjectProcess targetProcess = processes.stream()
                        .filter(p -> stageKey.equals(p.getStageKey()))
                        .findFirst()
                        .orElse(null);

                if (targetProcess != null) {
                    // 更新计划时间
                    if (plannedStartTime != null) {
                        targetProcess.setPlannedStartTime(convertToLocalDateTime(plannedStartTime));
                    }
                    if (plannedEndTime != null) {
                        targetProcess.setPlannedEndTime(convertToLocalDateTime(plannedEndTime));
                    }

                    // 更新实际时间
                    if (actualStartTime != null) {
                        targetProcess.setActualStartTime(convertToLocalDateTime(actualStartTime));
                    }
                    if (actualEndTime != null) {
                        targetProcess.setActualEndTime(convertToLocalDateTime(actualEndTime));
                    }

                    // 更新备注
                    if (remarks != null) {
                        targetProcess.setRemarks(remarks);
                    }

                    // 保存更新
                    projectProcessService.updateById(targetProcess);
                    log.info("更新项目流程阶段: projectId={}, stageKey={}", projectId, stageKey);
                }
            }
        }
    }

    /**
     * 更新项目的开始结束日期
     */
    private void updateProjectDates(Long projectId, Map<String, Object> formData) {
        log.info("更新项目开始结束日期: projectId={}", projectId);

        Object projectStartTime = formData.get("projectStartTime");
        Object projectEndTime = formData.get("projectEndTime");

        Project project = projectService.getById(projectId);
        if (project != null) {
            if (projectStartTime != null) {
                project.setStartDate(convertToLocalDateTime(projectStartTime));
            }
            if (projectEndTime != null) {
                project.setEndDate(convertToLocalDateTime(projectEndTime));
            }

            projectService.updateById(project);
            log.info("项目日期更新成功: projectId={}, startDate={}, endDate={}",
                    projectId, project.getStartDate(), project.getEndDate());
        }
    }

    /**
     * 转换时间戳为LocalDateTime
     */
    private LocalDateTime convertToLocalDateTime(Object timestamp) {
        if (timestamp instanceof Number) {
            long ts = ((Number) timestamp).longValue();
            return LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochMilli(ts),
                    ZoneId.systemDefault()
            );
        }
        return null;
    }

    /**
     * 发送计划提交通知
     */
    private void sendPlanningSubmitNotification(Long projectId, Map<String, Object> formData) {
        log.info("发送项目计划提交通知: projectId={}", projectId);
        // TODO: 实现通知逻辑
    }

    /**
     * 检查项目日期是否发生变化
     */
    private boolean checkIfDatesChanged(Map<String, Object> formData, Map<String, Object> previousFormData) {
        if (previousFormData == null) {
            return true; // 如果没有之前的数据，认为是变化
        }

        Object newStartTime = formData.get("projectStartTime");
        Object newEndTime = formData.get("projectEndTime");
        Object oldStartTime = previousFormData.get("projectStartTime");
        Object oldEndTime = previousFormData.get("projectEndTime");

        boolean startTimeChanged = !Objects.equals(newStartTime, oldStartTime);
        boolean endTimeChanged = !Objects.equals(newEndTime, oldEndTime);

        return startTimeChanged || endTimeChanged;
    }

    /**
     * 检查里程碑是否发生变化
     */
    private boolean checkIfMilestonesChanged(Map<String, Object> formData, Map<String, Object> previousFormData) {
        if (previousFormData == null) {
            return true; // 如果没有之前的数据，认为是变化
        }

        Object newMilestones = formData.get("milestones");
        Object oldMilestones = previousFormData.get("milestones");

        return !Objects.equals(newMilestones, oldMilestones);
    }

    /**
     * 发送里程碑变化通知
     */
    private void sendMilestoneChangeNotification(Long projectId, Map<String, Object> formData, Map<String, Object> previousFormData) {
        log.info("发送里程碑变化通知: projectId={}", projectId);

        // 记录变化详情
        Object newMilestones = formData.get("milestones");
        Object oldMilestones = previousFormData.get("milestones");

        log.debug("里程碑变化详情: projectId={}, 新里程碑={}, 旧里程碑={}",
                projectId, newMilestones, oldMilestones);

        // TODO: 实现具体的通知逻辑，如发送邮件、系统消息等
    }

    /**
     * 发送计划批准通知
     */
    private void sendPlanningApproveNotification(Long projectId, Map<String, Object> formData) {
        log.info("发送项目计划批准通知: projectId={}", projectId);
        // TODO: 实现通知逻辑
    }
}
