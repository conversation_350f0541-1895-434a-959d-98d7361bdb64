<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vwatj.ppms.mapper.ProjectProcessMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.vwatj.ppms.entity.ProjectProcess">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="stage_key" property="stageKey" />
        <result column="stage_name" property="stageName" />
        <result column="description" property="description" />
        <result column="sort_order" property="sortOrder" />
        <result column="progress" property="progress" />
        <result column="status" property="status" />
        <result column="is_main_process" property="isMainProcess" />
        <result column="stage_process" property="stageProcess" />
        <result column="planned_start_time" property="plannedStartTime" />
        <result column="planned_end_time" property="plannedEndTime" />
        <result column="actual_start_time" property="actualStartTime" />
        <result column="actual_end_time" property="actualEndTime" />
        <result column="remarks" property="remarks" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, project_id, stage_key, stage_name, description, sort_order, progress, status,
        is_main_process, stage_process, planned_start_time, planned_end_time,
        actual_start_time, actual_end_time, remarks,
        create_time, update_time, create_by, update_by, deleted
    </sql>

    <!-- 批量插入项目阶段 -->
    <insert id="batchInsertProjectStages" parameterType="java.util.List">
        INSERT INTO project_process (
            project_id, stage_key, stage_name, description, sort_order,
            progress, status, is_main_process, stage_process, create_by, update_by
        ) VALUES
        <foreach collection="stages" item="stage" separator=",">
            (
                #{stage.projectId}, #{stage.stageKey}, #{stage.stageName},
                #{stage.description}, #{stage.sortOrder}, #{stage.progress},
                #{stage.status}, #{stage.isMainProcess}, #{stage.stageProcess},
                #{stage.createBy}, #{stage.updateBy}
            )
        </foreach>
    </insert>

    <!-- 根据项目ID查询阶段详情（包含流程信息） -->
    <select id="selectStageDetailWithProcess" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM project_process
        WHERE project_id = #{projectId}
          AND stage_key = #{stageKey}
          AND deleted = 0
    </select>

    <!-- 查询项目所有阶段及其流程状态 -->
    <select id="selectProjectStagesWithProcess" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM project_process
        WHERE project_id = #{projectId}
          AND deleted = 0
        ORDER BY sort_order ASC
    </select>

    <!-- 查询项目已完成的阶段数量 -->
    <select id="selectCompletedStageCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM project_process
        WHERE project_id = #{projectId}
          AND status = 'completed'
          AND deleted = 0
    </select>

    <!-- 查询项目总阶段数量 -->
    <select id="selectTotalStageCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM project_process
        WHERE project_id = #{projectId}
          AND deleted = 0
    </select>

    <!-- 查询下一个待执行的阶段 -->
    <select id="selectNextPendingStage" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM project_process
        WHERE project_id = #{projectId}
          AND status = 'pending'
          AND deleted = 0
        ORDER BY sort_order ASC
        LIMIT 1
    </select>

    <!-- 批量更新阶段状态 -->
    <update id="batchUpdateStageStatus">
        UPDATE project_process
        SET status = #{status},
            actual_start_time = CASE
                WHEN #{status} = 'active' AND actual_start_time IS NULL THEN NOW()
                ELSE actual_start_time
            END,
            actual_end_time = CASE
                WHEN #{status} IN ('completed', 'skipped') THEN NOW()
                ELSE actual_end_time
            END,
            progress = CASE
                WHEN #{status} = 'completed' THEN 100
                WHEN #{status} = 'skipped' THEN 0
                ELSE progress
            END,
            update_time = NOW(),
            update_by = #{updateBy}
        WHERE project_id = #{projectId}
          AND stage_key IN
          <foreach collection="stageKeys" item="stageKey" open="(" separator="," close=")">
              #{stageKey}
          </foreach>
          AND deleted = 0
    </update>

    <!-- 重置项目阶段状态 -->
    <update id="resetProjectStages">
        UPDATE project_process
        SET status = 'pending',
            progress = 0,
            actual_start_time = NULL,
            actual_end_time = NULL,
            update_time = NOW(),
            update_by = #{updateBy}
        WHERE project_id = #{projectId}
          AND deleted = 0
    </update>

    <!-- 查询项目阶段时间线 -->
    <select id="selectProjectStageTimeline" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM project_process
        WHERE project_id = #{projectId}
          AND deleted = 0
          AND (planned_start_time IS NOT NULL OR planned_end_time IS NOT NULL
               OR actual_start_time IS NOT NULL OR actual_end_time IS NOT NULL)
        ORDER BY sort_order ASC
    </select>

    <!-- 根据阶段Key和状态查询阶段 -->
    <select id="selectStagesByKeyAndStatus" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM project_process
        WHERE project_id = #{projectId}
          AND stage_key = #{stageKey}
          AND status = #{status}
          AND deleted = 0
    </select>

    <!-- 查询项目进度概览 -->
    <select id="selectProjectProgressOverview" resultType="java.util.Map">
        SELECT
            ps.stage_key,
            ps.stage_name,
            ps.status,
            ps.progress,
            ps.is_main_process,
            ps.planned_start_time,
            ps.planned_end_time,
            ps.actual_start_time,
            ps.actual_end_time,
            CASE
                WHEN ps.status = 'completed' THEN 100
                WHEN ps.status = 'active' THEN ps.progress
                ELSE 0
            END as actual_progress
        FROM project_process ps
        WHERE ps.project_id = #{projectId}
          AND ps.deleted = 0
        ORDER BY ps.sort_order ASC
    </select>

</mapper>
