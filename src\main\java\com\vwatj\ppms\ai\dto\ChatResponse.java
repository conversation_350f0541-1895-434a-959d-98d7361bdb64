package com.vwatj.ppms.ai.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 聊天响应DTO
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public class ChatResponse {

    /**
     * AI回复消息
     */
    private String message;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 响应时间戳
     */
    private Long timestamp;

    /**
     * 是否调用了业务功能
     */
    private Boolean functionCalled = false;

    /**
     * 调用的功能列表
     */
    private List<FunctionCall> functionCalls;

    /**
     * 建议的后续操作
     */
    private List<SuggestedAction> suggestedActions;

    /**
     * 额外的响应数据
     */
    private Map<String, Object> data;

    /**
     * 错误信息（如果有）
     */
    private String error;

    /**
     * 功能调用信息
     */
    @Data
    public static class FunctionCall {
        private String functionName;
        private Map<String, Object> parameters;
        private Object result;
        private Boolean success;
        private String error;
    }

    /**
     * 建议操作
     */
    @Data
    public static class SuggestedAction {
        private String action;
        private String description;
        private Map<String, Object> parameters;
    }
}
