package com.vwatj.ppms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.vwatj.ppms.entity.ProjectCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 项目分类Mapper
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Mapper
public interface ProjectCategoryMapper extends BaseMapper<ProjectCategory> {

    /**
     * 获取项目分类选项列表
     * @return 选项列表
     */
    @Select("SELECT code as value, name as label FROM project_category WHERE enabled = 1 AND deleted = 0 ORDER BY sort_order ASC, id ASC")
    List<Map<String, String>> getProjectCategoryOptions();
}
