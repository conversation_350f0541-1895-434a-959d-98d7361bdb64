package com.vwatj.ppms.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.vwatj.ppms.entity.ProjectProcess;

import java.util.List;
import java.util.Map;

/**
 * 项目阶段服务接口
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface ProjectProcessService extends IService<ProjectProcess> {

    /**
     * 根据项目ID获取所有阶段
     *
     * @param projectId 项目ID
     * @return 项目阶段列表
     */
    List<ProjectProcess> getStagesByProjectId(Long projectId);


    ProjectProcess getStageByProjectIdAndKey(Long projectId, String stageKey);

    /**
     * 根据项目分类初始化项目阶段
     * 从ProjectCategory的flowTemplate字段读取模板配置
     *
     * @param projectId    项目ID
     * @param categoryName 项目分类名称
     */
    void initializeProjectStagesByCategory(Long projectId, String categoryName);

    /**
     * 激活阶段
     *
     * @param projectId 项目ID
     * @param stageKey  阶段Key
     */
    void activateStage(Long projectId, String stageKey);

    /**
     * 完成阶段
     *
     * @param projectId 项目ID
     * @param stageKey  阶段Key
     */
    void completeStage(Long projectId, String stageKey);

    /**
     * 跳过阶段
     *
     * @param projectId 项目ID
     * @param stageKey  阶段Key
     */
    void skipStage(Long projectId, String stageKey);


    /**
     * 提交流程表单数据
     *
     * @param projectId 项目ID
     * @param stageKey  阶段Key
     * @param formKey   表单Key
     * @param formData  表单数据（包含operation字段）
     */
    void submitStageForm(Long projectId, String stageKey, String formKey, JSONObject formData);

    /**
     * 更新流程表单数据
     *
     * @param projectId 项目ID
     * @param stageKey  阶段Key
     * @param formKey   表单Key
     * @param formData  表单数据
     */
    void updateStageFormData(Long projectId, String stageKey, String formKey, JSONObject formData);

    /**
     * 获取流程表单数据
     *
     * @param projectId 项目ID
     * @param stageKey  阶段Key
     * @param formKey   表单Key
     * @return 表单数据
     */
    JSONObject getStageFormData(Long projectId, String stageKey, String formKey);
}
