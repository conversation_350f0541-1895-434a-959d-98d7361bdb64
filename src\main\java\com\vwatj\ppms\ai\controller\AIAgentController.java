package com.vwatj.ppms.ai.controller;

import com.vwatj.ppms.ai.dto.ChatRequest;
import com.vwatj.ppms.ai.dto.ChatResponse;
import com.vwatj.ppms.ai.service.AIAgentService;
import com.vwatj.ppms.common.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.Map;

/**
 * AI Agent控制器
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@RestController
@RequestMapping("/ai")
@RequiredArgsConstructor
public class AIAgentController {

    private final AIAgentService aiAgentService;

    /**
     * 聊天接口
     */
    @PostMapping("/chat")
    public ApiResponse<ChatResponse> chat(@RequestBody ChatRequest request) {
        try {
            log.info("收到AI聊天请求: sessionId={}, userId={}", 
                    request.getSessionId(), request.getUserId());
            
            ChatResponse response = aiAgentService.chat(request);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("AI聊天处理失败", e);
            return ApiResponse.badRequest("AI聊天处理失败: " + e.getMessage());
        }
    }

    /**
     * 流式聊天接口
     */
    @PostMapping(value = "/chat/stream", produces = MediaType.TEXT_PLAIN_VALUE)
    public Flux<String> streamChat(@RequestBody ChatRequest request) {
        try {
            log.info("收到AI流式聊天请求: sessionId={}, userId={}", 
                    request.getSessionId(), request.getUserId());
            
            String response = aiAgentService.streamChat(request);
            
            // 模拟流式输出
            return Flux.fromArray(response.split(""))
                    .delayElements(Duration.ofMillis(50));
        } catch (Exception e) {
            log.error("AI流式聊天处理失败", e);
            return Flux.just("抱歉，处理您的请求时遇到了问题。");
        }
    }

    /**
     * 项目分析接口
     */
    @PostMapping("/analyze/project/{projectId}")
    public ApiResponse<String> analyzeProject(@PathVariable Long projectId) {
        try {
            log.info("收到项目分析请求: projectId={}", projectId);
            
            String analysis = aiAgentService.analyzeProject(projectId);
            return ApiResponse.success(analysis);
        } catch (Exception e) {
            log.error("项目分析失败", e);
            return ApiResponse.badRequest("项目分析失败: " + e.getMessage());
        }
    }

    /**
     * 生成项目报告接口
     */
    @PostMapping("/report/project/{projectId}")
    public ApiResponse<String> generateProjectReport(
            @PathVariable Long projectId,
            @RequestParam(defaultValue = "综合") String reportType) {
        try {
            log.info("收到项目报告生成请求: projectId={}, reportType={}", projectId, reportType);
            
            String report = aiAgentService.generateProjectReport(projectId, reportType);
            return ApiResponse.success(report);
        } catch (Exception e) {
            log.error("生成项目报告失败", e);
            return ApiResponse.badRequest("生成项目报告失败: " + e.getMessage());
        }
    }

    /**
     * 任务分配建议接口
     */
    @PostMapping("/suggest/task-assignment/{projectId}")
    public ApiResponse<String> suggestTaskAssignment(@PathVariable Long projectId) {
        try {
            log.info("收到任务分配建议请求: projectId={}", projectId);
            
            String suggestion = aiAgentService.suggestTaskAssignment(projectId);
            return ApiResponse.success(suggestion);
        } catch (Exception e) {
            log.error("任务分配建议生成失败", e);
            return ApiResponse.badRequest("任务分配建议生成失败: " + e.getMessage());
        }
    }

    /**
     * 快速问答接口
     */
    @PostMapping("/quick-ask")
    public ApiResponse<String> quickAsk(@RequestBody Map<String, Object> request) {
        try {
            String question = (String) request.get("question");
            Long projectId = request.get("projectId") != null ? 
                    Long.valueOf(request.get("projectId").toString()) : null;
            
            log.info("收到快速问答请求: question={}, projectId={}", question, projectId);
            
            ChatRequest chatRequest = new ChatRequest();
            chatRequest.setMessage(question);
            chatRequest.setProjectId(projectId);
            chatRequest.setSessionId("quick-" + System.currentTimeMillis());
            
            ChatResponse response = aiAgentService.chat(chatRequest);
            return ApiResponse.success(response.getMessage());
        } catch (Exception e) {
            log.error("快速问答处理失败", e);
            return ApiResponse.badRequest("快速问答处理失败: " + e.getMessage());
        }
    }

    /**
     * 获取AI助手状态
     */
    @GetMapping("/status")
    public ApiResponse<Map<String, Object>> getStatus() {
        try {
            Map<String, Object> status = Map.of(
                "status", "online",
                "version", "1.0.0",
                "model", "deepseek-chat",
                "capabilities", new String[]{
                    "项目管理", "任务管理", "数据分析", "报告生成", "智能建议"
                },
                "timestamp", System.currentTimeMillis()
            );
            
            return ApiResponse.success(status);
        } catch (Exception e) {
            log.error("获取AI助手状态失败", e);
            return ApiResponse.badRequest("获取状态失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ApiResponse<String> health() {
        return ApiResponse.success("AI Agent is healthy");
    }
}
