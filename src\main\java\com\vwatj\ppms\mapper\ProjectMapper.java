package com.vwatj.ppms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.vwatj.ppms.entity.Project;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 项目Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Mapper
public interface ProjectMapper extends BaseMapper<Project> {
    
    /**
     * 分页查询项目
     */
    IPage<Project> selectProjectPage(Page<Project> page,
                                   @Param("keyword") String keyword,
                                   @Param("category") String category,
                                   @Param("status") String status,
                                   @Param("priority") String priority,
                                   @Param("manager") String manager,
                                   @Param("starred") <PERSON>olean starred,
                                   @Param("archived") Boolean archived,
                                   @Param("tags") List<String> tags,
                                   @Param("startDate") LocalDateTime startDate,
                                   @Param("endDate") LocalDateTime endDate);
    
    /**
     * 根据项目名称查询项目
     */
    Project selectByName(@Param("name") String name);
    
    /**
     * 查询用户关注的项目
     */
    List<Project> selectStarredProjects(@Param("userId") String userId);
    
    /**
     * 查询用户参与的项目
     */
    List<Project> selectUserProjects(@Param("userId") String userId);
    
    /**
     * 统计项目数量按状态
     */
    List<Map<String, Object>> countProjectsByStatus();
    
    /**
     * 统计项目数量按分类
     */
    List<Map<String, Object>> countProjectsByCategory();
    
    /**
     * 统计项目数量按优先级
     */
    List<Map<String, Object>> countProjectsByPriority();
}
