package com.vwatj.ppms.controller;

import com.vwatj.ppms.common.ApiResponse;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateTaskDTO;
import com.vwatj.ppms.dto.TaskQueryDTO;
import com.vwatj.ppms.dto.UpdateTaskDTO;
import com.vwatj.ppms.entity.ProjectTask;
import com.vwatj.ppms.enums.TaskStatusEnum;
import com.vwatj.ppms.service.ProjectTaskService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.Map;

/**
 * 任务控制器
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@RestController
@RequestMapping("/tasks")
@RequiredArgsConstructor
public class TaskController {

    private final ProjectTaskService projectTaskService;
    
    /**
     * 分页查询任务
     */
    @GetMapping
    public ApiResponse<PageResult<ProjectTask>> getTasks(TaskQueryDTO queryDTO) {
        PageResult<ProjectTask> result = projectTaskService.getTaskPage(queryDTO);
        return ApiResponse.success(result);
    }
    
    /**
     * 根据ID查询任务
     */
    @GetMapping("/{id}")
    public ApiResponse<ProjectTask> getTask(@PathVariable Long id) {
        ProjectTask projectTask = projectTaskService.getById(id);
        if (projectTask == null) {
            return ApiResponse.notFound("任务不存在");
        }
        return ApiResponse.success(projectTask);
    }
    
    /**
     * 创建任务
     */
    @PostMapping
    public ApiResponse<ProjectTask> createTask(@Validated @RequestBody CreateTaskDTO createTaskDTO) {
        try {
            ProjectTask projectTask = projectTaskService.createTask(createTaskDTO);
            return ApiResponse.success("任务创建成功", projectTask);
        } catch (IllegalArgumentException e) {
            log.warn("创建任务参数错误: {}", e.getMessage());
            return ApiResponse.badRequest(e.getMessage());
        } catch (Exception e) {
            log.error("创建任务失败", e);
            return ApiResponse.error("创建任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新任务
     */
    @PutMapping("/{id}")
    public ApiResponse<ProjectTask> updateTask(@PathVariable Long id, @Validated @RequestBody UpdateTaskDTO updateTaskDTO) {
        try {
            updateTaskDTO.setId(id);
            ProjectTask projectTask = projectTaskService.updateTask(updateTaskDTO);
            return ApiResponse.success("任务更新成功", projectTask);
        } catch (IllegalArgumentException e) {
            log.warn("更新任务参数错误: {}", e.getMessage());
            return ApiResponse.badRequest(e.getMessage());
        } catch (Exception e) {
            log.error("更新任务失败", e);
            return ApiResponse.error("更新任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除任务
     */
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteTask(@PathVariable Long id) {
        try {
            projectTaskService.deleteTask(id);
            return ApiResponse.success("任务删除成功");
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 执行任务操作 (基于form操作，formkey为task-operation)
     */
    @PostMapping("/{id}/operation")
    public ApiResponse<String> executeTaskOperation(@PathVariable Long id, @RequestBody TaskOperationRequest request) {
        try {
            TaskStatusEnum targetStatus = request.getTargetStatus();
            String operation = request.getOperation();

            // 如果没有提供目标状态，根据操作名和当前状态推导
            if (targetStatus == null && operation != null) {
                Map<String, TaskStatusEnum> transitions = projectTaskService.getTaskStatusTransitions(id);
                targetStatus = transitions.get(operation);
                if (targetStatus == null) {
                    return ApiResponse.badRequest("无效的操作: " + operation);
                }
            }

            // 使用统一的updateTaskStatus方法，传入操作名
            projectTaskService.updateTaskStatus(id, targetStatus, request.getComment(), operation);
            return ApiResponse.success("任务操作执行成功");
        } catch (Exception e) {
            log.error("执行任务操作失败: taskId={}, operation={}", id, request.getOperation(), e);
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 获取指定状态的可用操作
     */
    @GetMapping("/status/{status}/operations")
    public ApiResponse<List<Map<String, Object>>> getAvailableOperations(@PathVariable String status) {
        try {
            List<Map<String, Object>> operations = projectTaskService.getAvailableOperations(status);
            return ApiResponse.success(operations);
        } catch (Exception e) {
            log.error("获取可用操作失败: status={}", status, e);
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 分配任务
     */
    @PatchMapping("/{id}/assign")
    public ApiResponse<String> assignTask(@PathVariable Long id, @RequestBody AssignTaskRequest request) {
        try {
            projectTaskService.assignTask(id, request.getAssignee());
            return ApiResponse.success("任务分配成功");
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 获取任务统计信息
     */
    @GetMapping("/stats")
    public ApiResponse<Map<String, Object>> getTaskStats(@RequestParam(required = false) Long projectId) {
        Map<String, Object> stats = projectTaskService.getTaskStats(projectId);
        return ApiResponse.success(stats);
    }
    
    /**
     * 获取我的任务
     */
    @GetMapping("/my")
    public ApiResponse<PageResult<ProjectTask>> getMyTasks(@RequestParam String assignee, TaskQueryDTO queryDTO) {
        PageResult<ProjectTask> result = projectTaskService.getMyTasks(assignee, queryDTO);
        return ApiResponse.success(result);
    }

    /**
     * 高级筛选任务
     */
    @PostMapping("/search")
    public ApiResponse<PageResult<ProjectTask>> searchTasks(@RequestBody Map<String, Object> requestBody) {
        try {
            // 创建TaskQueryDTO并处理时间戳转换
            TaskQueryDTO queryDTO = new TaskQueryDTO();

            // 基本字段
            if (requestBody.get("page") != null) {
                queryDTO.setPage(Long.valueOf(requestBody.get("page").toString()));
            }
            if (requestBody.get("pageSize") != null) {
                queryDTO.setPageSize(Long.valueOf(requestBody.get("pageSize").toString()));
            }
            if (requestBody.get("sortBy") != null) {
                queryDTO.setSortBy(requestBody.get("sortBy").toString());
            }
            if (requestBody.get("sortOrder") != null) {
                queryDTO.setSortOrder(requestBody.get("sortOrder").toString());
            }
            if (requestBody.get("keyword") != null) {
                queryDTO.setKeyword(requestBody.get("keyword").toString());
            }
            if (requestBody.get("projectId") != null) {
                queryDTO.setProjectId(Long.valueOf(requestBody.get("projectId").toString()));
            }
            if (requestBody.get("status") != null) {
                queryDTO.setStatus(requestBody.get("status").toString());
            }
            if (requestBody.get("priority") != null) {
                queryDTO.setPriority(requestBody.get("priority").toString());
            }
            if (requestBody.get("stageName") != null) {
                queryDTO.setStageName(requestBody.get("stageName").toString());
            }
            if (requestBody.get("issueType") != null) {
                queryDTO.setIssueType(requestBody.get("issueType").toString());
            }
            if (requestBody.get("assignee") != null) {
                queryDTO.setAssignee(requestBody.get("assignee").toString());
            }
            if (requestBody.get("reporter") != null) {
                queryDTO.setReporter(requestBody.get("reporter").toString());
            }
            if (requestBody.get("taskScope") != null) {
                queryDTO.setTaskScope(requestBody.get("taskScope").toString());
            }
            if (requestBody.get("currentUser") != null) {
                queryDTO.setCurrentUser(requestBody.get("currentUser").toString());
            }
            if (requestBody.get("statusList") != null) {
                queryDTO.setStatusList((List<String>) requestBody.get("statusList"));
            }

            // 时间戳转换为LocalDateTime
            if (requestBody.get("dateRangeStart") != null) {
                long timestamp = Long.parseLong(requestBody.get("dateRangeStart").toString());
                queryDTO.setDateRangeStart(java.time.LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochMilli(timestamp),
                    java.time.ZoneId.systemDefault()
                ));
            }
            if (requestBody.get("dateRangeEnd") != null) {
                long timestamp = Long.parseLong(requestBody.get("dateRangeEnd").toString());
                queryDTO.setDateRangeEnd(java.time.LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochMilli(timestamp),
                    java.time.ZoneId.systemDefault()
                ));
            }
            if (requestBody.get("weekRangeStart") != null) {
                long timestamp = Long.parseLong(requestBody.get("weekRangeStart").toString());
                queryDTO.setWeekRangeStart(java.time.LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochMilli(timestamp),
                    java.time.ZoneId.systemDefault()
                ));
            }
            if (requestBody.get("weekRangeEnd") != null) {
                long timestamp = Long.parseLong(requestBody.get("weekRangeEnd").toString());
                queryDTO.setWeekRangeEnd(java.time.LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochMilli(timestamp),
                    java.time.ZoneId.systemDefault()
                ));
            }

            log.info("周筛选参数: weekRangeStart={}, weekRangeEnd={}",
                queryDTO.getWeekRangeStart(), queryDTO.getWeekRangeEnd());

            PageResult<ProjectTask> result = projectTaskService.searchTasks(queryDTO);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("高级筛选任务失败", e);
            return ApiResponse.error("筛选任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取未分配的任务
     */
    @GetMapping("/unassigned")
    public ApiResponse<PageResult<ProjectTask>> getUnassignedTasks(TaskQueryDTO queryDTO) {
        try {
            queryDTO.setTaskScope("unassigned");
            PageResult<ProjectTask> result = projectTaskService.searchTasks(queryDTO);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取未分配任务失败", e);
            return ApiResponse.error("获取未分配任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取我的待办任务
     */
    @GetMapping("/todo")
    public ApiResponse<PageResult<ProjectTask>> getTodoTasks(@RequestParam String currentUser, TaskQueryDTO queryDTO) {
        try {
            queryDTO.setTaskScope("todo");
            queryDTO.setCurrentUser(currentUser);
            PageResult<ProjectTask> result = projectTaskService.searchTasks(queryDTO);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取待办任务失败", e);
            return ApiResponse.error("获取待办任务失败: " + e.getMessage());
        }
    }
    


    /**
     * 导出任务数据或下载模板
     * @param template 是否为模板下载，true=下载模板，false=导出数据
     * @param keyword 关键词
     * @param projectId 项目ID
     * @param status 任务状态
     * @param priority 优先级
     * @param issueType 问题类型
     * @param assignee 指派人
     * @param reporter 报告人
     * @param page 页码
     * @param pageSize 每页大小
     */
    @GetMapping("/export")
    public ResponseEntity<Resource> exportTasks(
            @RequestParam(value = "template", defaultValue = "false") boolean template,
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "projectId", required = false) Long projectId,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "priority", required = false) String priority,
            @RequestParam(value = "stageName", required = false) String stageName,
            @RequestParam(value = "issueType", required = false) String issueType,
            @RequestParam(value = "assignee", required = false) String assignee,
            @RequestParam(value = "reporter", required = false) String reporter,
            @RequestParam(value = "page", required = false) Long page,
            @RequestParam(value = "pageSize", required = false) Long pageSize) {
        try {
            Resource resource;
            String filename;

            if (template) {
                // 下载模板
                resource = projectTaskService.generateTemplate();
                filename = "任务导入模板.xlsx";
            } else {
                // 构建查询条件
                TaskQueryDTO queryDTO = new TaskQueryDTO();
                queryDTO.setKeyword(keyword);
                queryDTO.setProjectId(projectId);
                queryDTO.setStatus(status);
                queryDTO.setPriority(priority);
                queryDTO.setStageName(stageName);
                queryDTO.setIssueType(issueType);
                queryDTO.setAssignee(assignee);
                queryDTO.setReporter(reporter);
                queryDTO.setPage(page != null ? page : 1L);
                queryDTO.setPageSize(pageSize != null ? pageSize : 10000L); // 导出时使用大的页面大小

                // 导出数据
                resource = projectTaskService.exportTasks(queryDTO);
                filename = "任务数据_" +
                    java.time.LocalDateTime.now().format(
                        java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")
                    ) + ".xlsx";
            }

            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .header(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                .body(resource);
        } catch (Exception e) {
            log.error(template ? "生成模板失败" : "导出任务失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 导入任务（SSE方式）
     */
    @PostMapping("/import")
    public SseEmitter importTasks(
            @RequestParam("file") MultipartFile file,
            @RequestParam("mode") String mode,
            @RequestParam(value = "projectId", required = false) Long projectId,
            @RequestParam(value = "projectName", required = false) String projectName) {
        try {
            if (file.isEmpty()) {
                SseEmitter emitter = new SseEmitter();
                try {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data(Map.of("message", "文件不能为空")));
                    emitter.complete();
                } catch (Exception e) {
                    emitter.completeWithError(e);
                }
                return emitter;
            }

            // 验证文件类型
            String filename = file.getOriginalFilename();
            if (filename == null || (!filename.endsWith(".xlsx") && !filename.endsWith(".xls"))) {
                SseEmitter emitter = new SseEmitter();
                try {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data(Map.of("message", "只支持 .xlsx 和 .xls 格式的Excel文件")));
                    emitter.complete();
                } catch (Exception e) {
                    emitter.completeWithError(e);
                }
                return emitter;
            }

            // 验证导入模式
            if (!"incremental".equals(mode) && !"overwrite".equals(mode)) {
                SseEmitter emitter = new SseEmitter();
                try {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data(Map.of("message", "导入模式只能是 incremental 或 overwrite")));
                    emitter.complete();
                } catch (Exception e) {
                    emitter.completeWithError(e);
                }
                return emitter;
            }

            return projectTaskService.importTasks(file, mode, projectId, projectName);
        } catch (Exception e) {
            log.error("导入任务失败", e);
            SseEmitter emitter = new SseEmitter();
            try {
                emitter.send(SseEmitter.event()
                    .name("error")
                    .data(Map.of("message", "导入失败: " + e.getMessage())));
                emitter.complete();
            } catch (Exception ex) {
                emitter.completeWithError(ex);
            }
            return emitter;
        }
    }
    
    /**
     * 更新任务状态请求
     */
    @Data
    public static class UpdateTaskStatusRequest {
        private String status;
        private String comment;
    }
    
    /**
     * 批量更新任务状态请求
     */
    @Data
    public static class BatchUpdateTaskStatusRequest {
        private List<Map<String, Object>> tasks;
    }
    
    /**
     * 分配任务请求
     */
    @Data
    public static class AssignTaskRequest {
        private String assignee;
    }
    
    /**
     * 完成任务请求
     */
    @Data
    public static class CompleteTaskRequest {
        private String comment;
    }

    /**
     * 任务操作请求
     */
    @Data
    public static class TaskOperationRequest {
        private String operation; // close, reopen, resolve, progress
        private TaskStatusEnum targetStatus; // 目标状态
        private String comment;
    }
    



}
