package com.vwatj.ppms.ai;

import com.vwatj.ppms.ai.dto.ChatRequest;
import com.vwatj.ppms.ai.dto.ChatResponse;
import com.vwatj.ppms.ai.service.AIAgentService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AI Agent集成测试
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class AIAgentIntegrationTest {

    @Autowired(required = false)
    private AIAgentService aiAgentService;

    @Test
    public void testBasicChat() {
        // 如果没有配置API Key，跳过测试
        if (aiAgentService == null) {
            log.info("AI Agent服务未配置，跳过测试");
            return;
        }

        ChatRequest request = new ChatRequest();
        request.setMessage("你好，请介绍一下你的功能");
        request.setSessionId("test-session-1");
        request.setUserId("test-user");
        request.setEnableFunctionCalling(false); // 基础聊天不启用功能调用

        ChatResponse response = aiAgentService.chat(request);

        assertNotNull(response);
        assertNotNull(response.getMessage());
        assertFalse(response.getMessage().isEmpty());
        assertEquals("test-session-1", response.getSessionId());
        
        log.info("AI响应: {}", response.getMessage());
    }

    @Test
    public void testProjectQuery() {
        if (aiAgentService == null) {
            log.info("AI Agent服务未配置，跳过测试");
            return;
        }

        ChatRequest request = new ChatRequest();
        request.setMessage("帮我查询所有项目的信息");
        request.setSessionId("test-session-2");
        request.setUserId("test-user");
        request.setEnableFunctionCalling(true);

        ChatResponse response = aiAgentService.chat(request);

        assertNotNull(response);
        assertNotNull(response.getMessage());
        
        log.info("项目查询响应: {}", response.getMessage());
    }

    @Test
    public void testProjectAnalysis() {
        if (aiAgentService == null) {
            log.info("AI Agent服务未配置，跳过测试");
            return;
        }

        // 假设存在项目ID为1的项目
        Long projectId = 1L;
        
        try {
            String analysis = aiAgentService.analyzeProject(projectId);
            
            assertNotNull(analysis);
            assertFalse(analysis.isEmpty());
            
            log.info("项目分析结果: {}", analysis);
        } catch (Exception e) {
            log.warn("项目分析测试失败，可能是因为项目不存在: {}", e.getMessage());
        }
    }

    @Test
    public void testReportGeneration() {
        if (aiAgentService == null) {
            log.info("AI Agent服务未配置，跳过测试");
            return;
        }

        Long projectId = 1L;
        String reportType = "进度报告";
        
        try {
            String report = aiAgentService.generateProjectReport(projectId, reportType);
            
            assertNotNull(report);
            assertFalse(report.isEmpty());
            
            log.info("项目报告: {}", report);
        } catch (Exception e) {
            log.warn("报告生成测试失败，可能是因为项目不存在: {}", e.getMessage());
        }
    }

    @Test
    public void testTaskAssignmentSuggestion() {
        if (aiAgentService == null) {
            log.info("AI Agent服务未配置，跳过测试");
            return;
        }

        Long projectId = 1L;
        
        try {
            String suggestion = aiAgentService.suggestTaskAssignment(projectId);
            
            assertNotNull(suggestion);
            assertFalse(suggestion.isEmpty());
            
            log.info("任务分配建议: {}", suggestion);
        } catch (Exception e) {
            log.warn("任务分配建议测试失败，可能是因为项目不存在: {}", e.getMessage());
        }
    }

    @Test
    public void testContextualChat() {
        if (aiAgentService == null) {
            log.info("AI Agent服务未配置，跳过测试");
            return;
        }

        ChatRequest request = new ChatRequest();
        request.setMessage("这个项目的进度如何？");
        request.setSessionId("test-session-3");
        request.setUserId("test-user");
        request.setProjectId(1L); // 设置项目上下文
        request.setEnableFunctionCalling(true);

        ChatResponse response = aiAgentService.chat(request);

        assertNotNull(response);
        assertNotNull(response.getMessage());
        
        log.info("上下文聊天响应: {}", response.getMessage());
    }

    @Test
    public void testStreamChat() {
        if (aiAgentService == null) {
            log.info("AI Agent服务未配置，跳过测试");
            return;
        }

        ChatRequest request = new ChatRequest();
        request.setMessage("请详细介绍项目管理的最佳实践");
        request.setSessionId("test-session-4");
        request.setUserId("test-user");

        String response = aiAgentService.streamChat(request);

        assertNotNull(response);
        assertFalse(response.isEmpty());
        
        log.info("流式聊天响应: {}", response);
    }
}
