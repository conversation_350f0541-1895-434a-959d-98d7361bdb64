package com.vwatj.ppms.controller;

import com.vwatj.ppms.common.ApiResponse;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.DocumentQueryDTO;
import com.vwatj.ppms.dto.UpdateDocumentDTO;
import com.vwatj.ppms.dto.UploadDocumentDTO;
import com.vwatj.ppms.entity.Document;
import com.vwatj.ppms.service.DocumentService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 文档控制器
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@RestController
@RequestMapping("/documents")
@RequiredArgsConstructor
public class DocumentController {
    
    private final DocumentService documentService;
    
    /**
     * 分页查询文档
     */
    @GetMapping
    public ApiResponse<PageResult<Document>> getDocuments(DocumentQueryDTO queryDTO) {
        PageResult<Document> result = documentService.getDocumentPage(queryDTO);
        return ApiResponse.success(result);
    }
    
    /**
     * 根据ID查询文档
     */
    @GetMapping("/{id}")
    public ApiResponse<Document> getDocument(@PathVariable String id) {
        Document document = documentService.getById(id);
        if (document == null) {
            return ApiResponse.notFound("文档不存在");
        }
        return ApiResponse.success(document);
    }
    
    /**
     * 上传文档
     */
    @PostMapping
    public ApiResponse<Document> uploadDocument(@Validated @ModelAttribute UploadDocumentDTO uploadDocumentDTO) {
        try {
            Document document = documentService.uploadDocument(uploadDocumentDTO);
            return ApiResponse.success("文档上传成功", document);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 更新文档信息
     */
    @PutMapping("/{id}")
    public ApiResponse<Document> updateDocument(@PathVariable String id, @Validated @RequestBody UpdateDocumentDTO updateDocumentDTO) {
        try {
            updateDocumentDTO.setId(id);
            Document document = documentService.updateDocument(updateDocumentDTO);
            return ApiResponse.success("文档更新成功", document);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 删除文档
     */
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteDocument(@PathVariable String id) {
        try {
            documentService.deleteDocument(id);
            return ApiResponse.success("文档删除成功");
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 下载文档
     */
    @GetMapping("/{id}/download")
    public ApiResponse<Map<String, String>> downloadDocument(@PathVariable String id) {
        try {
            Map<String, String> result = documentService.downloadDocument(id);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 预览文档
     */
    @GetMapping("/{id}/preview")
    public ApiResponse<Map<String, String>> previewDocument(@PathVariable String id) {
        try {
            Map<String, String> result = documentService.previewDocument(id);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 发布文档
     */
    @PatchMapping("/{id}/publish")
    public ApiResponse<String> publishDocument(@PathVariable String id) {
        try {
            documentService.publishDocument(id);
            return ApiResponse.success("文档发布成功");
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 归档文档
     */
    @PatchMapping("/{id}/archive")
    public ApiResponse<String> archiveDocument(@PathVariable String id) {
        try {
            documentService.archiveDocument(id);
            return ApiResponse.success("文档归档成功");
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 获取文档版本历史
     */
    @GetMapping("/{id}/versions")
    public ApiResponse<List<Map<String, Object>>> getDocumentVersions(@PathVariable String id) {
        try {
            List<Map<String, Object>> versions = documentService.getDocumentVersions(id);
            return ApiResponse.success(versions);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 获取文档统计信息
     */
    @GetMapping("/stats")
    public ApiResponse<Map<String, Object>> getDocumentStats(@RequestParam(required = false) Long projectId) {
        Map<String, Object> stats = documentService.getDocumentStats(projectId);
        return ApiResponse.success(stats);
    }
    
    /**
     * 批量删除文档
     */
    @DeleteMapping("/batch")
    public ApiResponse<String> batchDeleteDocuments(@RequestBody BatchDeleteDocumentsRequest request) {
        try {
            documentService.batchDeleteDocuments(request.getIds());
            return ApiResponse.success("批量删除文档成功");
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 复制文档
     */
    @PostMapping("/{id}/duplicate")
    public ApiResponse<Document> duplicateDocument(@PathVariable String id, @RequestBody DuplicateDocumentRequest request) {
        try {
            Document document = documentService.duplicateDocument(id, request.getTitle(), request.getProjectId());
            return ApiResponse.success("文档复制成功", document);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 移动文档到其他项目
     */
    @PatchMapping("/{id}/move")
    public ApiResponse<String> moveDocument(@PathVariable String id, @RequestBody MoveDocumentRequest request) {
        try {
            documentService.moveDocument(id, request.getProjectId());
            return ApiResponse.success("文档移动成功");
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 审核文档请求
     */
    public static class ReviewDocumentRequest {
        private String action;
        private String comments;
        
        public String getAction() {
            return action;
        }
        
        public void setAction(String action) {
            this.action = action;
        }
        
        public String getComments() {
            return comments;
        }
        
        public void setComments(String comments) {
            this.comments = comments;
        }
    }
    
    /**
     * 批量删除文档请求
     */
    public static class BatchDeleteDocumentsRequest {
        private List<String> ids;
        
        public List<String> getIds() {
            return ids;
        }
        
        public void setIds(List<String> ids) {
            this.ids = ids;
        }
    }
    
    /**
     * 复制文档请求
     */
    public static class DuplicateDocumentRequest {
        private String title;
        private Long projectId;
        
        public String getTitle() {
            return title;
        }
        
        public void setTitle(String title) {
            this.title = title;
        }
        
        public Long getProjectId() {
            return projectId;
        }
        
        public void setProjectId(Long projectId) {
            this.projectId = projectId;
        }
    }
    
    /**
     * 移动文档请求
     */
    public static class MoveDocumentRequest {
        private Long projectId;
        
        public Long getProjectId() {
            return projectId;
        }
        
        public void setProjectId(Long projectId) {
            this.projectId = projectId;
        }
    }
}
