package com.vwatj.ppms.controller;

import com.vwatj.ppms.common.ApiResponse;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateUserDTO;
import com.vwatj.ppms.dto.UpdateUserDTO;
import com.vwatj.ppms.dto.UserQueryDTO;
import com.vwatj.ppms.entity.User;
import com.vwatj.ppms.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户控制器
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
public class UserController {
    
    private final UserService userService;
    
    /**
     * 分页查询用户
     */
    @GetMapping
    public ApiResponse<PageResult<User>> getUsers(UserQueryDTO queryDTO) {
        PageResult<User> result = userService.getUserPage(queryDTO);
        return ApiResponse.success(result);
    }
    
    /**
     * 根据ID查询用户
     */
    @GetMapping("/{id}")
    public ApiResponse<User> getUser(@PathVariable String id) {
        User user = userService.getById(id);
        if (user == null) {
            return ApiResponse.notFound("用户不存在");
        }
        return ApiResponse.success(user);
    }
    
    /**
     * 创建用户
     */
    @PostMapping
    public ApiResponse<User> createUser(@Validated @RequestBody CreateUserDTO createUserDTO) {
        try {
            User user = userService.createUser(createUserDTO);
            return ApiResponse.success("用户创建成功", user);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 更新用户
     */
    @PutMapping("/{id}")
    public ApiResponse<User> updateUser(@PathVariable String id, @Validated @RequestBody UpdateUserDTO updateUserDTO) {
        try {
            updateUserDTO.setId(id);
            User user = userService.updateUser(updateUserDTO);
            return ApiResponse.success("用户更新成功", user);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteUser(@PathVariable String id) {
        try {
            userService.deleteUser(id);
            return ApiResponse.success("用户删除成功");
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 启用/禁用用户
     */
    @PatchMapping("/{id}/toggle-status")
    public ApiResponse<String> toggleUserStatus(@PathVariable String id) {
        try {
            userService.toggleUserStatus(id);
            return ApiResponse.success("用户状态切换成功");
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 重置用户密码
     */
    @PatchMapping("/{id}/reset-password")
    public ApiResponse<String> resetPassword(@PathVariable String id, @RequestBody ResetPasswordRequest request) {
        try {
            userService.resetPassword(id, request.getNewPassword());
            return ApiResponse.success("密码重置成功");
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 获取用户选项列表（用于下拉选择）
     */
    @GetMapping("/options")
    public ApiResponse<List<Map<String, String>>> getUserOptions() {
        try {
            List<Map<String, String>> options = userService.getUserOptions();
            return ApiResponse.success(options);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 重置密码请求
     */
    public static class ResetPasswordRequest {
        private String newPassword;
        
        public String getNewPassword() {
            return newPassword;
        }
        
        public void setNewPassword(String newPassword) {
            this.newPassword = newPassword;
        }
    }
}
