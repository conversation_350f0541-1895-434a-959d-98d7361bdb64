package com.vwatj.ppms.ai.service;

import com.vwatj.ppms.ai.dto.ChatRequest;
import com.vwatj.ppms.ai.dto.ChatResponse;

/**
 * AI Agent服务接口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
public interface AIAgentService {

    /**
     * 处理用户聊天请求
     *
     * @param request 聊天请求
     * @return 聊天响应
     */
    ChatResponse chat(ChatRequest request);

    /**
     * 处理流式聊天请求
     *
     * @param request 聊天请求
     * @return 流式响应
     */
    String streamChat(ChatRequest request);

    /**
     * 分析项目数据并提供建议
     *
     * @param projectId 项目ID
     * @return 分析结果和建议
     */
    String analyzeProject(Long projectId);

    /**
     * 生成项目报告
     *
     * @param projectId 项目ID
     * @param reportType 报告类型
     * @return 项目报告
     */
    String generateProjectReport(Long projectId, String reportType);

    /**
     * 智能任务分配建议
     *
     * @param projectId 项目ID
     * @return 任务分配建议
     */
    String suggestTaskAssignment(Long projectId);
}
