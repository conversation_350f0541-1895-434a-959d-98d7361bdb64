package com.vwatj.ppms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateTaskCategoryDTO;
import com.vwatj.ppms.dto.TaskCategoryQueryDTO;
import com.vwatj.ppms.dto.UpdateTaskCategoryDTO;
import com.vwatj.ppms.entity.ProjectTaskCategory;
import com.vwatj.ppms.mapper.ProjectTaskCategoryMapper;
import com.vwatj.ppms.service.ProjectTaskCategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 任务分类服务实现
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Service
@RequiredArgsConstructor
public class ProjectTaskCategoryServiceImpl extends ServiceImpl<ProjectTaskCategoryMapper, ProjectTaskCategory> implements ProjectTaskCategoryService {

    private final ProjectTaskCategoryMapper projectTaskCategoryMapper;

    @Override
    public List<Map<String, String>> getTaskCategoryOptions() {
        return projectTaskCategoryMapper.getTaskCategoryOptions();
    }

    @Override
    public PageResult<ProjectTaskCategory> getTaskCategoryPage(TaskCategoryQueryDTO queryDTO) {
        Page<ProjectTaskCategory> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());

        LambdaQueryWrapper<ProjectTaskCategory> queryWrapper = new LambdaQueryWrapper<>();

        // 关键词搜索（分类名称或代码）
        if (StringUtils.hasText(queryDTO.getKeyword())) {
            queryWrapper.and(wrapper -> wrapper
                .like(ProjectTaskCategory::getName, queryDTO.getKeyword())
                .or()
                .like(ProjectTaskCategory::getCode, queryDTO.getKeyword())
            );
        }

        // 启用状态过滤
        if (queryDTO.getEnabled() != null) {
            queryWrapper.eq(ProjectTaskCategory::getEnabled, queryDTO.getEnabled());
        }

        // 排序
        queryWrapper.orderByAsc(ProjectTaskCategory::getSortOrder)
                   .orderByAsc(ProjectTaskCategory::getId);

        IPage<ProjectTaskCategory> result = this.page(page, queryWrapper);

        return PageResult.from(result);
    }

    @Override
    public ProjectTaskCategory createTaskCategory(CreateTaskCategoryDTO createTaskCategoryDTO) {
        // 检查分类代码是否已存在
        LambdaQueryWrapper<ProjectTaskCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectTaskCategory::getCode, createTaskCategoryDTO.getCode());

        if (this.count(queryWrapper) > 0) {
            throw new IllegalArgumentException("分类代码已存在");
        }

        ProjectTaskCategory projectTaskCategory = new ProjectTaskCategory();
        BeanUtils.copyProperties(createTaskCategoryDTO, projectTaskCategory);

        this.save(projectTaskCategory);
        return projectTaskCategory;
    }

    @Override
    public ProjectTaskCategory updateTaskCategory(UpdateTaskCategoryDTO updateTaskCategoryDTO) {
        ProjectTaskCategory existingCategory = this.getById(updateTaskCategoryDTO.getId());
        if (existingCategory == null) {
            throw new IllegalArgumentException("任务分类不存在");
        }

        // 检查分类代码是否已存在（排除当前记录）
        LambdaQueryWrapper<ProjectTaskCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectTaskCategory::getCode, updateTaskCategoryDTO.getCode())
                   .ne(ProjectTaskCategory::getId, updateTaskCategoryDTO.getId());

        if (this.count(queryWrapper) > 0) {
            throw new IllegalArgumentException("分类代码已存在");
        }

        BeanUtils.copyProperties(updateTaskCategoryDTO, existingCategory);
        this.updateById(existingCategory);
        return existingCategory;
    }

    @Override
    public void deleteTaskCategory(Long id) {
        ProjectTaskCategory projectTaskCategory = this.getById(id);
        if (projectTaskCategory == null) {
            throw new IllegalArgumentException("任务分类不存在");
        }

        this.removeById(id);
    }
}
