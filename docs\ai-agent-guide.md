# PPMS AI Agent 使用指南

## 概述

PPMS AI Agent 是基于 Spring AI 和 DeepSeek V3 开发的智能项目管理助手，能够通过自然语言与用户交互，执行项目管理相关的业务操作，提供智能分析和建议。

## 功能特性

### 1. 智能对话
- 自然语言理解和回复
- 上下文感知对话
- 流式响应支持
- 多轮对话记忆

### 2. 业务功能集成
- **项目管理**: 创建、查询、更新项目信息
- **任务管理**: 创建、分配、跟踪任务状态
- **流程管理**: 处理项目流程和表单
- **数据分析**: 提供项目和任务统计分析

### 3. 智能分析
- 项目进度分析
- 风险识别和预警
- 资源配置建议
- 任务分配优化

### 4. 报告生成
- 项目综合报告
- 进度报告
- 风险报告
- 自定义报告

## API 接口

### 1. 聊天接口

#### 基础聊天
```http
POST /ai/chat
Content-Type: application/json

{
  "message": "帮我查看项目1的详细信息",
  "sessionId": "session-123",
  "userId": "user-456",
  "projectId": 1,
  "enableFunctionCalling": true
}
```

#### 流式聊天
```http
POST /ai/chat/stream
Content-Type: application/json

{
  "message": "分析一下当前所有项目的状态",
  "sessionId": "session-123",
  "userId": "user-456"
}
```

### 2. 项目分析
```http
POST /ai/analyze/project/1
```

### 3. 报告生成
```http
POST /ai/report/project/1?reportType=进度报告
```

### 4. 任务分配建议
```http
POST /ai/suggest/task-assignment/1
```

### 5. 快速问答
```http
POST /ai/quick-ask
Content-Type: application/json

{
  "question": "项目1还有多少个未完成的任务？",
  "projectId": 1
}
```

## 使用示例

### 1. 项目管理对话

**用户**: "帮我创建一个新项目，名称是'移动端APP开发'，经理是张三，优先级是高"

**AI**: "好的，我来帮您创建项目。请问这个项目的详细描述是什么？预计的开始和结束时间是什么时候？"

**用户**: "这是一个电商移动端APP项目，预计3个月完成，从下周一开始"

**AI**: "项目创建成功！
- 项目ID: 15
- 项目名称: 移动端APP开发
- 状态: requirement_collect
- 经理: 张三
- 优先级: high

项目已进入需求收集阶段，建议您接下来：
1. 完善项目需求文档
2. 确定团队成员
3. 制定详细的项目计划"

### 2. 任务管理对话

**用户**: "查看项目1下所有未完成的任务"

**AI**: "项目1下共有8个未完成任务：
- UI设计 (ID: 23, 负责人: 李四, 优先级: high)
- 后端API开发 (ID: 24, 负责人: 王五, 优先级: medium)
- 数据库设计 (ID: 25, 负责人: 赵六, 优先级: high)
...

建议优先处理高优先级任务，是否需要我帮您重新分配任务？"

### 3. 项目分析

**用户**: "分析一下项目1的当前状态"

**AI**: "基于项目数据分析，项目1的情况如下：

**进度分析**:
- 总体进度: 65%
- 按计划进度: 70%
- 进度偏差: -5%

**风险识别**:
- 🔴 高风险: 3个高优先级任务延期
- 🟡 中风险: 团队成员工作负载不均

**建议**:
1. 重新评估延期任务的时间安排
2. 考虑增加资源投入
3. 优化任务分配，平衡团队负载

**下一步行动**:
- 召开项目评审会议
- 调整项目计划
- 加强风险监控"

## 配置说明

### 1. 环境变量配置

```bash
# DeepSeek API Key
export DEEPSEEK_API_KEY=your-deepseek-api-key-here
```

### 2. application.yml 配置

```yaml
spring:
  ai:
    openai:
      api-key: ${DEEPSEEK_API_KEY}
      base-url: https://api.deepseek.com
      chat:
        options:
          model: deepseek-chat
          temperature: 0.7
          max-tokens: 4000
```

### 3. 模型参数说明

- **model**: 使用的模型名称 (deepseek-chat)
- **temperature**: 创造性参数 (0.0-1.0，0.7为推荐值)
- **max-tokens**: 最大输出长度 (建议4000)

## 最佳实践

### 1. 对话设计
- 使用清晰、具体的问题
- 提供必要的上下文信息
- 利用会话ID维持对话连续性

### 2. 功能调用
- 启用 `enableFunctionCalling` 获得更好的业务集成
- 提供项目ID或任务ID获得更精准的响应
- 使用建议操作快速执行后续任务

### 3. 性能优化
- 合理设置 `max-tokens` 避免过长响应
- 使用流式接口提升用户体验
- 缓存常用的分析结果

## 扩展开发

### 1. 添加新的业务功能

```java
@Component
public class CustomBusinessFunctions {
    
    public Function<CustomRequest, String> customFunction() {
        return request -> {
            // 实现自定义业务逻辑
            return "处理结果";
        };
    }
}
```

### 2. 自定义提示词

```java
@Bean
public ChatClient customChatClient(OpenAiChatModel chatModel) {
    return ChatClient.builder(chatModel)
            .defaultSystem("自定义系统提示词")
            .build();
}
```

### 3. 添加新的分析维度

```java
@Override
public String analyzeProject(Long projectId) {
    // 添加自定义分析逻辑
    String customAnalysis = performCustomAnalysis(projectId);
    // 结合AI分析
    return aiAgentService.chat(buildAnalysisRequest(customAnalysis));
}
```

## 故障排除

### 1. 常见问题

**Q: AI响应速度慢**
A: 检查网络连接，调整max-tokens参数，考虑使用流式接口

**Q: 功能调用失败**
A: 检查业务服务是否正常，验证请求参数格式

**Q: API Key无效**
A: 确认DeepSeek API Key配置正确，检查环境变量

### 2. 日志调试

```yaml
logging:
  level:
    com.vwatj.ppms.ai: DEBUG
    org.springframework.ai: DEBUG
```

### 3. 监控指标

- 响应时间
- 成功率
- 功能调用次数
- 错误率

## 安全考虑

1. **API Key保护**: 使用环境变量，不要硬编码
2. **输入验证**: 对用户输入进行验证和过滤
3. **权限控制**: 集成现有的用户权限系统
4. **数据脱敏**: 避免在日志中记录敏感信息

## 更新日志

### v1.0.0 (2025-07-25)
- 初始版本发布
- 基础聊天功能
- 项目和任务管理集成
- 智能分析和报告生成
