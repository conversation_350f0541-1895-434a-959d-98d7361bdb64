package com.vwatj.ppms.controller;

import com.vwatj.ppms.common.ApiResponse;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateProjectCategoryDTO;
import com.vwatj.ppms.dto.ProjectCategoryQueryDTO;
import com.vwatj.ppms.dto.UpdateProjectCategoryDTO;
import com.vwatj.ppms.entity.ProjectCategory;
import com.vwatj.ppms.service.ProjectCategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 项目分类控制器
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@RestController
@RequestMapping("/project-categories")
@RequiredArgsConstructor
public class ProjectCategoryController {

    private final ProjectCategoryService projectCategoryService;

    /**
     * 获取项目分类选项列表
     */
    @GetMapping("/options")
    public ApiResponse<List<Map<String, String>>> getProjectCategoryOptions() {
        return ApiResponse.success(projectCategoryService.getProjectCategoryOptions());
    }

    /**
     * 分页查询项目分类
     */
    @GetMapping("/page")
    public ApiResponse<PageResult<ProjectCategory>> getProjectCategoryPage(ProjectCategoryQueryDTO queryDTO) {
        PageResult<ProjectCategory> result = projectCategoryService.getProjectCategoryPage(queryDTO);
        return ApiResponse.success(result);
    }

    /**
     * 根据ID查询项目分类
     */
    @GetMapping("/{id}")
    public ApiResponse<ProjectCategory> getProjectCategory(@PathVariable Long id) {
        ProjectCategory projectCategory = projectCategoryService.getById(id);
        if (projectCategory == null) {
            return ApiResponse.notFound("项目分类不存在");
        }
        return ApiResponse.success(projectCategory);
    }

    /**
     * 创建项目分类
     */
    @PostMapping
    public ApiResponse<ProjectCategory> createProjectCategory(@Validated @RequestBody CreateProjectCategoryDTO createProjectCategoryDTO) {
        try {
            ProjectCategory projectCategory = projectCategoryService.createProjectCategory(createProjectCategoryDTO);
            return ApiResponse.success("项目分类创建成功", projectCategory);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 更新项目分类
     */
    @PutMapping("/{id}")
    public ApiResponse<ProjectCategory> updateProjectCategory(@PathVariable Long id, @Validated @RequestBody UpdateProjectCategoryDTO updateProjectCategoryDTO) {
        try {
            updateProjectCategoryDTO.setId(id);
            ProjectCategory projectCategory = projectCategoryService.updateProjectCategory(updateProjectCategoryDTO);
            return ApiResponse.success("项目分类更新成功", projectCategory);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 删除项目分类
     */
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteProjectCategory(@PathVariable Long id) {
        try {
            projectCategoryService.deleteProjectCategory(id);
            return ApiResponse.success("项目分类删除成功");
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
}
