package com.vwatj.ppms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.vwatj.ppms.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 文档实体类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "document", autoResultMap = true)
public class Document extends BaseEntity {
    
    /**
     * 文档ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 项目ID
     */
    private Long projectId;
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 流程ID
     */
    private Long processId;
    
    /**
     * 文档标题
     */
    private String title;
    
    /**
     * 文档内容
     */
    private String content;
    
    /**
     * 文档摘要
     */
    private String summary;

    /**
     * 文档描述 (映射到summary字段)
     */
    @com.baomidou.mybatisplus.annotation.TableField("summary")
    private String description;
    
    /**
     * 文档类型 (PDF, PPT, WORD, EXCEL, TXT, MD, IMAGE, VIDEO, AUDIO, ZIP, OTHER)
     */
    private String fileType;
    
    /**
     * 文档类别 (对应项目状态)
     */
    private String category;
    
    /**
     * 文档状态 (draft, review, approved, published, archived)
     */
    private String status;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件大小(字节)
     */
    private Long fileSize;
    
    /**
     * 文件扩展名
     */
    private String fileExtension;
    
    /**
     * 文件下载链接
     */
    private String fileUrl;
    
    /**
     * 缩略图链接
     */
    private String thumbnailUrl;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * 版本历史 (JSON格式存储)
     */
    @com.baomidou.mybatisplus.annotation.TableField(typeHandler = JacksonTypeHandler.class)
    private List<Map<String, Object>> versionHistory;
    
    /**
     * 作者ID
     */
    private Long author;
    
    /**
     * 作者姓名
     */
    private String authorName;

    /**
     * 标签列表 (JSON格式存储)
     */
    @com.baomidou.mybatisplus.annotation.TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> tags;
    
    /**
     * 关键词 (JSON格式存储)
     */
    @com.baomidou.mybatisplus.annotation.TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> keywords;
    
    /**
     * 相关文档ID列表 (JSON格式存储)
     */
    @com.baomidou.mybatisplus.annotation.TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> relatedDocuments;
    
    /**
     * 引用的外部资源 (JSON格式存储)
     */
    @com.baomidou.mybatisplus.annotation.TableField(value = "external_references", typeHandler = JacksonTypeHandler.class)
    private List<String> references;
    
    /**
     * 查看次数
     */
    private Integer viewCount;
    
    /**
     * 下载次数
     */
    private Integer downloadCount;
    
    /**
     * 发布时间
     */
    private LocalDateTime publishDate;
    
    /**
     * 过期时间
     */
    private LocalDateTime expiryDate;
    
    /**
     * 扩展字段 (JSON格式存储)
     */
    @com.baomidou.mybatisplus.annotation.TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> customFields;
}
