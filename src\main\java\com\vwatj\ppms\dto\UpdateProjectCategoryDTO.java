package com.vwatj.ppms.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 更新项目分类DTO
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Data
public class UpdateProjectCategoryDTO {

    /**
     * 分类ID
     */
    private Long id;

    /**
     * 分类代码
     */
    @NotBlank(message = "分类代码不能为空")
    private String code;

    /**
     * 分类名称
     */
    @NotBlank(message = "分类名称不能为空")
    private String name;

    /**
     * 分类描述
     */
    private String description;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 是否启用
     */
    @NotNull(message = "启用状态不能为空")
    private Boolean enabled;
}
