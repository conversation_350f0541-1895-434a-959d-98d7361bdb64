package com.vwatj.ppms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.DocumentQueryDTO;
import com.vwatj.ppms.dto.UpdateDocumentDTO;
import com.vwatj.ppms.dto.UploadDocumentDTO;
import com.vwatj.ppms.entity.Document;
import com.vwatj.ppms.mapper.DocumentMapper;
import com.vwatj.ppms.service.DocumentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 文档服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentServiceImpl extends ServiceImpl<DocumentMapper, Document> implements DocumentService {

    private final DocumentMapper documentMapper;

    @Value("${file.upload.cache-path:./uploads/cache}")
    private String tempPath;

    @Value("${file.upload.project-path:./uploads/projects}")
    private String projectBasePath;

    @Override
    public PageResult<Document> getDocumentPage(DocumentQueryDTO queryDTO) {
        Page<Document> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
        IPage<Document> result = documentMapper.selectDocumentPage(page,
                queryDTO.getKeyword(),
                queryDTO.getProjectId(),
                queryDTO.getCategory(),
                queryDTO.getStatus(),
                queryDTO.getFileType(),
                queryDTO.getAuthor(),
                queryDTO.getReviewer(),
                queryDTO.getApprover(),
                queryDTO.getTags(),
                queryDTO.getUploadDateStart(),
                queryDTO.getUploadDateEnd());
        return PageResult.from(result);
    }

    @Override
    public PageResult<Document> getProjectDocuments(Long projectId, DocumentQueryDTO queryDTO) {
        Page<Document> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
        IPage<Document> result = documentMapper.selectProjectDocuments(page, projectId);
        return PageResult.from(result);
    }

    @Override
    public Document getDocumentByTitle(String title, Long projectId) {
        return documentMapper.selectByTitle(title, projectId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Document uploadDocument(UploadDocumentDTO uploadDocumentDTO) {
        MultipartFile file = uploadDocumentDTO.getFile();

        // 检查文件是否为空
        if (file.isEmpty()) {
            throw new RuntimeException("文件不能为空");
        }

        // 获取文件信息
        String originalFilename = file.getOriginalFilename();
        String fileExtension = FileUtil.extName(originalFilename);
        String fileName = FileUtil.mainName(originalFilename);
        long fileSize = file.getSize();

        // 生成文件存储路径（这里简化处理，实际应该存储到文件服务器）
        String fileUrl = "/uploads/" + IdUtil.simpleUUID() + "." + fileExtension;

        // 根据文件扩展名确定文件类型
        String fileType = getFileTypeByExtension(fileExtension);

        Document document = new Document();
        document.setProjectId(uploadDocumentDTO.getProjectId());
        document.setTitle(fileName);
        document.setDescription(uploadDocumentDTO.getDescription());
        document.setFileType(fileType);
        document.setCategory(uploadDocumentDTO.getCategory());
        document.setStatus("draft");
        document.setFileName(originalFilename);
        document.setFileSize(fileSize);
        document.setFileExtension(fileExtension);
        document.setFileUrl(fileUrl);
        document.setVersion("1.0");
        document.setTags(uploadDocumentDTO.getTags());
        document.setViewCount(0);
        document.setDownloadCount(0);

        // TODO: 实际文件上传逻辑

        save(document);
        return document;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Document updateDocument(UpdateDocumentDTO updateDocumentDTO) {
        Document document = getById(updateDocumentDTO.getId());
        if (document == null) {
            throw new RuntimeException("文档不存在");
        }

        BeanUtil.copyProperties(updateDocumentDTO, document, "id");
        updateById(document);
        return document;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDocument(String id) {
        Document document = getById(id);
        if (document == null) {
            throw new RuntimeException("文档不存在");
        }

        // TODO: 删除实际文件

        removeById(id);
    }

    @Override
    public Map<String, String> downloadDocument(String id) {
        Document document = getById(id);
        if (document == null) {
            throw new RuntimeException("文档不存在");
        }

        // 增加下载次数
        incrementDownloadCount(id);

        Map<String, String> result = new HashMap<>();
        result.put("downloadUrl", document.getFileUrl());
        return result;
    }

    @Override
    public Map<String, String> previewDocument(String id) {
        Document document = getById(id);
        if (document == null) {
            throw new RuntimeException("文档不存在");
        }

        // 增加查看次数
        incrementViewCount(id);

        Map<String, String> result = new HashMap<>();
        result.put("previewUrl", document.getThumbnailUrl() != null ? document.getThumbnailUrl() : document.getFileUrl());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publishDocument(String id) {
        Document document = getById(id);
        if (document == null) {
            throw new RuntimeException("文档不存在");
        }

        document.setStatus("published");
        document.setPublishDate(LocalDateTime.now());
        updateById(document);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void archiveDocument(String id) {
        Document document = getById(id);
        if (document == null) {
            throw new RuntimeException("文档不存在");
        }

        document.setStatus("archived");
        updateById(document);
    }

    @Override
    public List<Map<String, Object>> getDocumentVersions(String id) {
        Document document = getById(id);
        if (document == null) {
            throw new RuntimeException("文档不存在");
        }

        return document.getVersionHistory();
    }

    @Override
    public Map<String, Object> getDocumentStats(Long projectId) {
        Map<String, Object> stats = new HashMap<>();

        // 总文档数
        stats.put("total", count());

        // 按状态统计
        List<Map<String, Object>> statusStats = documentMapper.countDocumentsByStatus(projectId);
        stats.put("byStatus", statusStats);

        // 按类别统计
        List<Map<String, Object>> categoryStats = documentMapper.countDocumentsByCategory(projectId);
        stats.put("byCategory", categoryStats);

        // 按文件类型统计
        List<Map<String, Object>> fileTypeStats = documentMapper.countDocumentsByFileType(projectId);
        stats.put("byFileType", fileTypeStats);

        // 总文件大小
        Long totalSize = documentMapper.sumDocumentFileSize(projectId);
        stats.put("totalSize", totalSize != null ? totalSize : 0L);

        // 总查看次数
        Long totalViews = documentMapper.sumDocumentViewCount(projectId);
        stats.put("totalViews", totalViews != null ? totalViews : 0L);

        // 总下载次数
        Long totalDownloads = documentMapper.sumDocumentDownloadCount(projectId);
        stats.put("totalDownloads", totalDownloads != null ? totalDownloads : 0L);

        return stats;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteDocuments(List<String> ids) {
        for (String id : ids) {
            deleteDocument(id);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Document duplicateDocument(String id, String title, Long projectId) {
        Document originalDocument = getById(id);
        if (originalDocument == null) {
            throw new RuntimeException("文档不存在");
        }

        Document newDocument = new Document();
        BeanUtil.copyProperties(originalDocument, newDocument, "id", "createdAt", "updatedAt");
        newDocument.setTitle(title);
        if (projectId != null) {
            newDocument.setProjectId(projectId);
        }
        newDocument.setStatus("draft");
        newDocument.setViewCount(0);
        newDocument.setDownloadCount(0);

        save(newDocument);
        return newDocument;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void moveDocument(String id, Long targetProjectId) {
        Document document = getById(id);
        if (document == null) {
            throw new RuntimeException("文档不存在");
        }

        document.setProjectId(targetProjectId);
        updateById(document);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void incrementViewCount(String id) {
        documentMapper.incrementViewCount(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void incrementDownloadCount(String id) {
        documentMapper.incrementDownloadCount(id);
    }

    /**
     * 根据文件扩展名获取文件类型
     */
    private String getFileTypeByExtension(String extension) {
        if (StrUtil.isBlank(extension)) {
            return "OTHER";
        }

        String ext = extension.toLowerCase();
        if ("pdf".equals(ext)) return "PDF";
        if ("ppt".equals(ext) || "pptx".equals(ext)) return "PPT";
        if ("doc".equals(ext) || "docx".equals(ext)) return "WORD";
        if ("xls".equals(ext) || "xlsx".equals(ext)) return "EXCEL";
        if ("txt".equals(ext)) return "TXT";
        if ("md".equals(ext) || "markdown".equals(ext)) return "MD";
        if ("jpg".equals(ext) || "jpeg".equals(ext) || "png".equals(ext) || "gif".equals(ext) || "bmp".equals(ext) || "svg".equals(ext) || "webp".equals(ext))
            return "IMAGE";
        if ("mp4".equals(ext) || "avi".equals(ext) || "mov".equals(ext) || "wmv".equals(ext) || "flv".equals(ext) || "mkv".equals(ext))
            return "VIDEO";
        if ("mp3".equals(ext) || "wav".equals(ext) || "flac".equals(ext) || "aac".equals(ext) || "ogg".equals(ext))
            return "AUDIO";
        if ("zip".equals(ext) || "rar".equals(ext) || "7z".equals(ext) || "tar".equals(ext) || "gz".equals(ext))
            return "ZIP";

        return "OTHER";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Map<String, Object>> processFormFiles(Long projectId, String stageKey, String formKey,
                                                      JSONObject formData, String operation) {
        log.info("处理表单文件: projectId={}, stageKey={}, formKey={}, operation={}",
                projectId, stageKey, formKey, operation);

        List<Map<String, Object>> processedFiles = new ArrayList<>();

        if (!containsFiles(formData)) {
            log.debug("表单数据中不包含文件");
            return processedFiles;
        }

        try {
            // 提取文件信息并处理文件，同时更新表单数据
            processAndUpdateFormData(formData, projectId, stageKey, formKey, operation, processedFiles);

            log.info("文件处理完成: projectId={}, 处理文件数量={}", projectId, processedFiles.size());

        } catch (Exception e) {
            log.error("处理表单文件失败: projectId={}, stageKey={}, formKey={}", projectId, stageKey, formKey, e);
            throw new RuntimeException("处理表单文件失败: " + e.getMessage(), e);
        }

        return processedFiles;
    }

    @Override
    public boolean containsFiles(JSONObject formData) {
        if (formData == null || formData.isEmpty()) {
            return false;
        }

        // 检查常见的文件字段
        String[] fileFields = {"file", "files", "attachment", "attachments", "document", "documents"};

        for (String field : fileFields) {
            if (formData.containsKey(field)) {
                Object value = formData.get(field);
                if (value != null) {
                    return true;
                }
            }
        }

        // 检查以file结尾的字段（如bbp_files）
        for (String key : formData.keySet()) {
            if (key.toLowerCase().contains("file") ||
                    key.toLowerCase().contains("attachment") ||
                    key.toLowerCase().endsWith("_files")) {
                Object value = formData.get(key);
                if (value != null) {
                    return true;
                }
            }
        }

        return false;
    }

    @Override
    public List<Map<String, Object>> extractFileInfo(JSONObject formData) {
        List<Map<String, Object>> fileInfoList = new ArrayList<>();

        if (formData == null || formData.isEmpty()) {
            return fileInfoList;
        }

        for (Map.Entry<String, Object> entry : formData.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value == null) {
                continue;
            }

            // 检查是否为文件相关字段
            if (isFileField(key)) {
                if (value instanceof Map) {
                    // 单个文件对象
                    @SuppressWarnings("unchecked")
                    Map<String, Object> fileInfo = (Map<String, Object>) value;
                    if (isValidFileInfo(fileInfo)) {
                        fileInfo.put("fieldName", key);
                        fileInfoList.add(fileInfo);
                    }
                } else if (value instanceof List) {
                    // 多个文件对象
                    @SuppressWarnings("unchecked")
                    List<Object> fileList = (List<Object>) value;
                    for (Object fileObj : fileList) {
                        if (fileObj instanceof Map) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> fileInfo = (Map<String, Object>) fileObj;
                            if (isValidFileInfo(fileInfo)) {
                                fileInfo.put("fieldName", key);
                                fileInfoList.add(fileInfo);
                            }
                        }
                    }
                }
            }
        }

        return fileInfoList;
    }

    @Override
    public String moveFileFromTempToProject(Long projectId, String tempFilePath, String fileName) {
        try {
            // 创建项目文档目录
            String projectDocPath = projectBasePath + "/" + projectId + "/document";
            Path projectDir = Paths.get(projectDocPath);
            if (!Files.exists(projectDir)) {
                Files.createDirectories(projectDir);
                log.info("创建项目文档目录: {}", projectDocPath);
            }

            // 构建目标文件路径，添加时间戳避免文件名冲突
            String timestamp = String.valueOf(System.currentTimeMillis());
            String fileExtension = getFileExtension(fileName);
            String baseFileName = fileName.substring(0, fileName.lastIndexOf('.') > 0 ? fileName.lastIndexOf('.') : fileName.length());
            String finalFileName = baseFileName + "_" + timestamp + (fileExtension.isEmpty() ? "" : "." + fileExtension);

            String targetFilePath = projectDocPath + "/" + finalFileName;
            Path sourcePath = Paths.get(tempFilePath);
            Path targetPath = Paths.get(targetFilePath);

            // 复制文件（保留临时文件，由系统定期清理）
            Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);

            log.info("文件复制成功: {} -> {}", tempFilePath, targetFilePath);
            return targetFilePath;

        } catch (IOException e) {
            log.error("文件复制失败: tempPath={}, fileName={}", tempFilePath, fileName, e);
            throw new RuntimeException("文件复制失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addFormDocumentRecord(Long projectId, String stageKey, String formKey,
                                      String filePath, String fileName, Long fileSize, String operation) {
        try {
            Document document = new Document();
            document.setProjectId(projectId);
            document.setTitle(fileName);
            document.setFileName(fileName);
            document.setFileUrl(filePath);
            document.setFileSize(fileSize);
            document.setFileExtension(getFileExtension(fileName));
            document.setCategory(stageKey); // 使用category字段存储stageKey
            document.setStatus("uploaded");

            // 使用customFields存储表单相关信息
            Map<String, Object> customFields = new HashMap<>();
            customFields.put("formKey", formKey);
            customFields.put("operation", operation);
            customFields.put("stage", stageKey);
            document.setCustomFields(customFields);

            save(document);

            log.info("文档记录添加成功: documentId={}, fileName={}, formKey={}", document.getId(), fileName, formKey);
            return document.getId();

        } catch (Exception e) {
            log.error("添加文档记录失败: fileName={}, formKey={}", fileName, formKey, e);
            throw new RuntimeException("添加文档记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理单个文件并更新文件信息
     * 直接修改传入的fileInfo对象，同时返回处理结果摘要
     */
    private Map<String, Object> processFileAndUpdate(Long projectId, String stageKey, String formKey,
                                                     Map<String, Object> fileInfo, String operation) {
        try {
            // 获取文件信息
            String fileName = (String) fileInfo.get("name");
            String originalName = (String) fileInfo.get("originalName");
            String cacheFileName = (String) fileInfo.get("cacheFileName");
            Long fileSize = getLongValue(fileInfo.get("size"));
            Boolean isTemporary = (Boolean) fileInfo.get("isTemporary");
            String url = (String) fileInfo.get("url");

            // 使用originalName作为最终文件名，如果没有则使用name
            String finalFileName = originalName != null ? originalName : fileName;

            if (finalFileName == null) {
                log.warn("文件名为空，跳过处理: {}", fileInfo);
                return null;
            }

            String finalFilePath;
            Long documentId;

            // 检查是否为临时文件
            if (Boolean.TRUE.equals(isTemporary) && cacheFileName != null) {
                // 从临时区转存到正式目录
                String cacheFilePath = tempPath + "/" + cacheFileName;

                // 检查临时文件是否存在
                if (!Files.exists(Paths.get(cacheFilePath))) {
                    log.warn("临时文件不存在，跳过处理: {}", cacheFilePath);
                    return null;
                }

                // 移动文件到项目目录
                finalFilePath = moveFileFromTempToProject(projectId, cacheFilePath, finalFileName);

                // 添加文档记录
                documentId = addFormDocumentRecord(projectId, stageKey, formKey, finalFilePath, finalFileName, fileSize, operation);

                // 直接更新原始文件信息对象
                fileInfo.put("isTemporary", false);
                fileInfo.put("url", generateFileUrl(finalFilePath));
                fileInfo.put("documentId", documentId);
                fileInfo.put("filePath", finalFilePath);
                fileInfo.put("processed", true);

                log.info("临时文件转存成功: {} -> {}", cacheFilePath, finalFilePath);
            } else {
                // 文件已经在正式目录，只需要添加文档记录
                if (url != null) {
                    // 从URL中提取文件路径
                    finalFilePath = extractFilePathFromUrl(url);
                } else {
                    log.warn("非临时文件缺少URL信息，跳过处理: {}", fileInfo);
                    return null;
                }

                // 添加文档记录
                documentId = addFormDocumentRecord(projectId, stageKey, formKey, finalFilePath, finalFileName, fileSize, operation);

                // 直接更新原始文件信息对象
                fileInfo.put("documentId", documentId);
                fileInfo.put("filePath", finalFilePath);
                fileInfo.put("processed", true);

                log.info("正式文件记录添加成功: {}", finalFilePath);
            }

            // 返回处理结果摘要（用于processedFiles数组）
            Map<String, Object> result = new HashMap<>();
            result.put("documentId", documentId);
            result.put("fileName", finalFileName);
            result.put("filePath", finalFilePath);
            result.put("fileSize", fileSize);
            result.put("fieldName", fileInfo.get("fieldName"));
            result.put("isTemporary", false); // 处理后都不是临时文件

            return result;

        } catch (Exception e) {
            log.error("处理文件失败: {}", fileInfo, e);
            return null;
        }
    }

    /**
     * 检查是否为文件字段
     */
    private boolean isFileField(String fieldName) {
        if (fieldName == null) {
            return false;
        }

        String lowerFieldName = fieldName.toLowerCase();
        return lowerFieldName.contains("file") ||
                lowerFieldName.contains("attachment") ||
                lowerFieldName.contains("document") ||
                lowerFieldName.contains("upload") ||
                lowerFieldName.endsWith("_files");
    }

    /**
     * 检查文件信息是否有效
     */
    private boolean isValidFileInfo(Map<String, Object> fileInfo) {
        if (fileInfo == null) {
            return false;
        }

        // 检查必要字段
        String name = (String) fileInfo.get("name");
        Boolean isTemporary = (Boolean) fileInfo.get("isTemporary");

        if (name == null) {
            return false;
        }

        // 如果是临时文件，需要有cacheFileName
        if (Boolean.TRUE.equals(isTemporary)) {
            return fileInfo.get("cacheFileName") != null;
        }

        // 如果不是临时文件，需要有url
        return fileInfo.get("url") != null;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
    }

    /**
     * 安全获取Long值
     */
    private Long getLongValue(Object value) {
        if (value == null) {
            return 0L;
        }
        if (value instanceof Long) {
            return (Long) value;
        }
        if (value instanceof Integer) {
            return ((Integer) value).longValue();
        }
        if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return 0L;
            }
        }
        return 0L;
    }

    /**
     * 从URL中提取文件路径
     */
    private String extractFilePathFromUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return null;
        }

        try {
            // 从URL中提取文件路径
            // 例如：http://localhost:8080/api/file-upload/cache/cache_20250722130433_318b0e57_a19n2aesglx0.pdf
            // 提取：cache_20250722130433_318b0e57_a19n2aesglx0.pdf

            if (url.contains("/cache/")) {
                // 临时文件URL
                String fileName = url.substring(url.lastIndexOf("/") + 1);
                return tempPath + "/" + fileName;
            } else if (url.contains("/projects/")) {
                // 正式文件URL
                int projectsIndex = url.indexOf("/projects/");
                return projectBasePath + url.substring(projectsIndex + "/projects".length());
            } else {
                // 其他情况，尝试提取文件名
                String fileName = url.substring(url.lastIndexOf("/") + 1);
                return fileName;
            }
        } catch (Exception e) {
            log.warn("从URL提取文件路径失败: {}", url, e);
            return null;
        }
    }

    /**
     * 处理文件并更新表单数据
     */
    private void processAndUpdateFormData(Map<String, Object> formData, Long projectId, String stageKey,
                                          String formKey, String operation, List<Map<String, Object>> processedFiles) {
        if (formData == null || formData.isEmpty()) {
            return;
        }

        for (Map.Entry<String, Object> entry : formData.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value == null || !isFileField(key)) {
                continue;
            }

            if (value instanceof List) {
                // 处理文件数组
                @SuppressWarnings("unchecked")
                List<Object> fileList = (List<Object>) value;
                List<Object> updatedFileList = new ArrayList<>();

                for (Object fileObj : fileList) {
                    if (fileObj instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> fileInfo = (Map<String, Object>) fileObj;

                        if (isValidFileInfo(fileInfo)) {
                            // 处理文件并直接更新文件信息
                            fileInfo.put("fieldName", key);
                            Map<String, Object> processResult = processFileAndUpdate(projectId, stageKey, formKey, fileInfo, operation);

                            if (processResult != null) {
                                processedFiles.add(processResult);
                            }

                            // 无论处理成功与否，都添加到列表中（失败时fileInfo保持原样）
                            updatedFileList.add(fileInfo);
                        } else {
                            // 无效文件信息，保留原始信息
                            updatedFileList.add(fileInfo);
                        }
                    } else {
                        // 非文件对象，保留原始信息
                        updatedFileList.add(fileObj);
                    }
                }

                // 更新表单数据中的文件列表
                formData.put(key, updatedFileList);

            } else if (value instanceof Map) {
                // 处理单个文件对象
                @SuppressWarnings("unchecked")
                Map<String, Object> fileInfo = (Map<String, Object>) value;

                if (isValidFileInfo(fileInfo)) {
                    // 处理文件并直接更新文件信息
                    fileInfo.put("fieldName", key);
                    Map<String, Object> processResult = processFileAndUpdate(projectId, stageKey, formKey, fileInfo, operation);

                    if (processResult != null) {
                        processedFiles.add(processResult);
                    }
                    // fileInfo已经被直接更新，无需额外操作
                }
            }
        }
    }


    /**
     * 生成文件访问URL
     */
    private String generateFileUrl(String filePath) {
        // 这里需要根据实际的文件访问URL格式来生成
        // 例如：/data/123/document/filename_123456789.pdf -> http://localhost:8080/api/file/projects/123/document/filename_123456789.pdf

        if (filePath.contains("/document/")) {
            String relativePath = filePath.substring(filePath.indexOf("/document/"));
            String projectId = extractProjectIdFromPath(filePath);
            return "http://localhost:8080/api/file/projects/" + projectId + relativePath;
        }

        return filePath; // 默认返回原路径
    }

    /**
     * 从文件路径中提取项目ID
     */
    private String extractProjectIdFromPath(String filePath) {
        try {
            // 从路径中提取项目ID，例如：/data/123/document/file.pdf -> 123
            String[] parts = filePath.split("/");
            for (int i = 0; i < parts.length - 1; i++) {
                if ("data".equals(parts[i]) && i + 1 < parts.length) {
                    return parts[i + 1];
                }
            }
        } catch (Exception e) {
            log.warn("从文件路径提取项目ID失败: {}", filePath, e);
        }
        return "unknown";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Map<String, Object>> processFormFilesWithChanges(Long projectId, String stageKey, String formKey,
                                                                 JSONObject currentFormData,
                                                                 JSONObject previousFormData,
                                                                 String operation) {
        log.info("处理表单文件变化: projectId={}, stageKey={}, formKey={}, operation={}",
                projectId, stageKey, formKey, operation);

        try {
            // 从提交前的表单数据中提取文档ID（原有状态）
            Set<Long> previousDocumentIds = previousFormData != null ?
                    extractDocumentIdsFromFormData(previousFormData) : new HashSet<>();

            // 处理当前提交的表单文件（会更新currentFormData中的文件对象，添加documentId）
            List<Map<String, Object>> processedFiles = processFormFiles(projectId, stageKey, formKey, currentFormData, operation);

            // 提取处理后表单中的文档ID（包括保留的旧文件和新增的文件）
            Set<Long> currentDocumentIds = extractDocumentIdsFromFormData(currentFormData);

            // 找出需要删除的文档ID（存在于提交前的表单中，但不在当前提交的表单中）
            Set<Long> documentsToDelete = new HashSet<>(previousDocumentIds);
            documentsToDelete.removeAll(currentDocumentIds);

            // 删除不再需要的文档和文件
            for (Long documentId : documentsToDelete) {
                try {
                    deleteDocumentAndFile(documentId);
                    log.info("删除文档成功: documentId={}", documentId);
                } catch (Exception e) {
                    log.error("删除文档失败: documentId={}", documentId, e);
                    throw  e;
                }
            }

            log.info("表单文件变化处理完成: projectId={}, 新增/更新文件数量={}, 删除文件数量={}",
                    projectId, processedFiles.size(), documentsToDelete.size());

            return processedFiles;

        } catch (Exception e) {
            log.error("处理表单文件变化失败: projectId={}, stageKey={}, formKey={}", projectId, stageKey, formKey, e);
            throw new RuntimeException("处理表单文件变化失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDocumentAndFile(Long documentId) {
        if (documentId == null) {
            log.warn("文档ID为空，跳过删除操作");
            return;
        }

        try {
            // 获取文档记录
            Document document = getById(documentId);
            if (document == null) {
                log.warn("文档记录不存在: documentId={}", documentId);
                return;
            }

            String filePath = document.getFileUrl();

            // 删除物理文件
            if (filePath != null && !filePath.trim().isEmpty()) {
                try {
                    Path fileToDelete = Paths.get(filePath);
                    if (Files.exists(fileToDelete)) {
                        Files.delete(fileToDelete);
                        log.info("物理文件删除成功: {}", filePath);
                    } else {
                        log.warn("物理文件不存在: {}", filePath);
                    }
                } catch (IOException e) {
                    log.error("删除物理文件失败: {}", filePath, e);
                    // 继续删除数据库记录，即使物理文件删除失败
                }
            }

            // 删除数据库记录
            removeById(documentId);
            log.info("文档记录删除成功: documentId={}", documentId);

        } catch (Exception e) {
            log.error("删除文档和文件失败: documentId={}", documentId, e);
            throw new RuntimeException("删除文档和文件失败: " + e.getMessage(), e);
        }
    }


    /**
     * 从表单数据中提取文档ID
     */
    private Set<Long> extractDocumentIdsFromFormData(Map<String, Object> formData) {
        Set<Long> documentIds = new HashSet<>();

        if (formData == null || formData.isEmpty()) {
            return documentIds;
        }

        for (Map.Entry<String, Object> entry : formData.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value == null || !isFileField(key)) {
                continue;
            }

            if (value instanceof JSONArray fileList) {
                // 处理文件数组
                for (Object fileObj : fileList) {
                    if (fileObj instanceof JSONObject fileInfo) {
                        Long documentId = fileInfo.getLongValue("documentId");
                        documentIds.add(documentId);
                    }
                }
            } else if (value instanceof JSONObject fileInfo) {
                // 处理单个文件对象
                Long documentId = fileInfo.getLongValue("documentId");
                documentIds.add(documentId);
            }
        }

        return documentIds;
    }
}
