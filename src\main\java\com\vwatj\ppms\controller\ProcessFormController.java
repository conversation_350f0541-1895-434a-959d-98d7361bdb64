package com.vwatj.ppms.controller;

import com.alibaba.fastjson2.JSONObject;
import com.vwatj.ppms.common.ApiResponse;
import com.vwatj.ppms.entity.ProcessForm;
import com.vwatj.ppms.service.ProcessFormService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 流程表单控制器
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Slf4j
@RestController
@RequestMapping("/process-form")
@RequiredArgsConstructor
public class ProcessFormController {

    private final ProcessFormService processFormService;

    /**
     * 根据表单Key获取表单信息
     */
    @GetMapping("/{formKey}")
    public ApiResponse<ProcessForm> getFormByKey(@PathVariable String formKey) {
        try {
            ProcessForm form = processFormService.getByFormKey(formKey);
            if (form == null) {
                return ApiResponse.notFound("表单不存在");
            }
            return ApiResponse.success(form);
        } catch (Exception e) {
            log.error("获取表单信息失败: formKey={}", formKey, e);
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 根据表单Key列表批量获取表单信息
     */
    @PostMapping("/batch")
    public ApiResponse<List<ProcessForm>> getFormsByKeys(@RequestBody List<String> formKeys) {
        try {
            List<ProcessForm> forms = processFormService.getByFormKeys(formKeys);
            return ApiResponse.success(forms);
        } catch (Exception e) {
            log.error("批量获取表单信息失败: formKeys={}", formKeys, e);
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 获取表单结构
     */
    @GetMapping("/{formKey}/struct")
    public ApiResponse<JSONObject> getFormStruct(@PathVariable String formKey) {
        try {
            JSONObject formStruct = processFormService.getFormStruct(formKey);
            if (formStruct == null) {
                return ApiResponse.notFound("表单结构不存在");
            }
            return ApiResponse.success(formStruct);
        } catch (Exception e) {
            log.error("获取表单结构失败: formKey={}", formKey, e);
            return ApiResponse.badRequest(e.getMessage());
        }
    }



    /**
     * 获取所有启用的表单
     */
    @GetMapping("/enabled")
    public ApiResponse<List<ProcessForm>> getAllEnabled() {
        try {
            List<ProcessForm> forms = processFormService.getAllEnabled();
            return ApiResponse.success(forms);
        } catch (Exception e) {
            log.error("获取启用表单列表失败", e);
            return ApiResponse.badRequest(e.getMessage());
        }
    }



    /**
     * 检查表单模板是否存在
     */
    @GetMapping("/{formKey}/exists")
    public ApiResponse<Boolean> checkFormExists(@PathVariable String formKey) {
        try {
            boolean exists = processFormService.existsByFormKey(formKey);
            return ApiResponse.success(exists);
        } catch (Exception e) {
            log.error("检查表单模板是否存在失败: formKey={}", formKey, e);
            return ApiResponse.badRequest(e.getMessage());
        }
    }
}
