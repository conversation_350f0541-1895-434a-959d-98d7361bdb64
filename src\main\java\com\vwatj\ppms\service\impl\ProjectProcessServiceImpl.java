package com.vwatj.ppms.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vwatj.ppms.entity.ProjectCategory;
import com.vwatj.ppms.entity.ProjectProcess;
import com.vwatj.ppms.enums.ProjectStageEnum;
import com.vwatj.ppms.exception.BusinessException;
import com.vwatj.ppms.workflow.form.FormEventHandler;
import com.vwatj.ppms.workflow.form.FormEventHandlerFactory;
import com.vwatj.ppms.mapper.ProjectProcessMapper;
import com.vwatj.ppms.service.ProjectCategoryService;
import com.vwatj.ppms.service.ProjectProcessService;
import com.vwatj.ppms.service.ProjectService;
import com.vwatj.ppms.service.DocumentService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.TaskService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 项目阶段服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Slf4j
@Service
public class ProjectProcessServiceImpl extends ServiceImpl<ProjectProcessMapper, ProjectProcess> implements ProjectProcessService {

    @Resource
    private ProjectCategoryService projectCategoryService;

    @Resource
    private TaskService taskService;

    @Resource
    @Lazy
    private FormEventHandlerFactory formEventHandlerFactory;

    @Resource
    private DocumentService documentService;

    @Resource
    @Lazy
    private ProjectService projectService;

    @Override
    public List<ProjectProcess> getStagesByProjectId(Long projectId) {
        if (projectId == null) {
            throw BusinessException.of("项目ID不能为空");
        }
        return baseMapper.selectByProjectId(projectId);
    }

    @Override
    public ProjectProcess getStageByProjectIdAndKey(Long projectId, String stageKey) {
        if (projectId == null) {
            throw BusinessException.of("项目ID不能为空");
        }
        if (stageKey == null || stageKey.trim().isEmpty()) {
            throw BusinessException.of("阶段Key不能为空");
        }
        return baseMapper.selectByProjectIdAndStageKey(projectId, stageKey);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initializeProjectStagesByCategory(Long projectId, String categoryName) {
        log.info("根据分类初始化项目阶段: projectId={}, categoryId={}", projectId, categoryName);

        if (projectId == null) {
            throw BusinessException.of("项目ID不能为空");
        }
        if (categoryName == null) {
            throw BusinessException.of("项目分类ID不能为空");
        }

        // 检查是否已经初始化
        List<ProjectProcess> existingStages = getStagesByProjectId(projectId);
        if (!existingStages.isEmpty()) {
            log.warn("项目阶段已存在，跳过初始化: projectId={}", projectId);
            return;
        }

        // 获取项目分类
        ProjectCategory category = projectCategoryService.getOne(new LambdaQueryWrapper<ProjectCategory>().eq(ProjectCategory::getName, categoryName));
        if (category == null) {
            throw BusinessException.of("项目分类不存在: " + categoryName);
        }

        JSONObject flowTemplate = category.getFlowTemplate();
        if (flowTemplate == null || flowTemplate.isEmpty()) {
            throw BusinessException.of("项目分类未配置流程模板: " + category.getName());
        }

        try {
            // 解析流程模板JSON
            JSONArray templateStages = flowTemplate.getJSONArray("stages");

            // 创建项目阶段
            List<ProjectProcess> stages = new ArrayList<>();
            for (Object ts : templateStages) {
                JSONObject templateStage = (JSONObject) ts;
                ProjectProcess stage = new ProjectProcess();
                stage.setProjectId(projectId);
                stage.setStageKey((String) templateStage.get("stageKey"));
                stage.setStageName((String) templateStage.get("stageName"));
                stage.setDescription((String) templateStage.get("description"));
                stage.setSortOrder((Integer) templateStage.get("sortOrder"));
                stage.setIsMainProcess((Boolean) templateStage.get("isMainProcess"));
                stage.setStatus("pending");
                stage.setProgress(0);
                stage.setStageProcess(new JSONObject());
                stage.getStageProcess().put("forms", templateStage.get("forms"));
                stages.add(stage);
            }

            // 批量插入
            if (!stages.isEmpty()) {
                baseMapper.batchInsertProjectStages(stages);
            }

            log.info("项目阶段初始化完成: projectId={}, stageCount={}", projectId, stages.size());

        } catch (Exception e) {
            log.error("解析流程模板失败: projectId={}, categoryName={}", projectId, categoryName, e);
            throw BusinessException.of("解析流程模板失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activateStage(Long projectId, String stageKey) {
        log.info("激活项目阶段: projectId={}, stageKey={}", projectId, stageKey);

        // 激活新阶段
        int updated = baseMapper.updateStageStatusAndProgress(
                projectId, stageKey, "active", null, LocalDateTime.now(), null);
        if (updated == 0) {
            throw BusinessException.of("激活阶段失败，阶段不存在: " + stageKey);
        }

        // 根据阶段key更新项目状态
        ProjectStageEnum projectStageEnum = ProjectStageEnum.fromCode(stageKey);
        if (projectStageEnum != null) {
            try {
                projectService.updateStatus(projectId, projectStageEnum.getCode());
                log.info("项目状态更新成功: projectId={}, status={}", projectId, projectStageEnum.getCode());
            } catch (Exception e) {
                log.error("更新项目状态失败: projectId={}, status={}", projectId, projectStageEnum.getCode(), e);
                // 不抛出异常，避免影响阶段激活
            }
        } else {
            log.debug("未找到阶段key对应的项目阶段枚举: {}", stageKey);
        }

        log.info("项目阶段激活成功: projectId={}, stageKey={}", projectId, stageKey);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeStage(Long projectId, String stageKey) {
        log.info("完成项目阶段: projectId={}, stageKey={}", projectId, stageKey);

        int updated = baseMapper.updateStageStatusAndProgress(
                projectId, stageKey, "completed", 100, null, LocalDateTime.now());
        if (updated == 0) {
            throw BusinessException.of("完成阶段失败，阶段不存在: " + stageKey);
        }

        log.info("项目阶段完成成功: projectId={}, stageKey={}", projectId, stageKey);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void skipStage(Long projectId, String stageKey) {
        log.info("跳过项目阶段: projectId={}, stageKey={}", projectId, stageKey);

        int updated = baseMapper.updateStageStatusAndProgress(
                projectId, stageKey, "skipped", 0, null, LocalDateTime.now());
        if (updated == 0) {
            throw BusinessException.of("跳过阶段失败，阶段不存在: " + stageKey);
        }

        log.info("项目阶段跳过成功: projectId={}, stageKey={}", projectId, stageKey);
    }


    @Override
    @Transactional
    public void submitStageForm(Long projectId, String stageKey, String formKey, JSONObject formData) {
        if (projectId == null) {
            throw BusinessException.of("项目ID不能为空");
        }
        if (stageKey == null || stageKey.trim().isEmpty()) {
            throw BusinessException.of("阶段Key不能为空");
        }
        if (formKey == null || formKey.trim().isEmpty()) {
            throw BusinessException.of("表单Key不能为空");
        }
        if (formData == null) {
            throw BusinessException.of("表单数据不能为空");
        }

        // 从表单数据中提取操作类型
        String operation = extractOperationFromFormData(formData);

        log.info("提交项目表单: projectId={}, stageKey={}, formKey={}, operation={}", projectId, stageKey, formKey, operation);

        // 获取项目阶段
        ProjectProcess stage = getStageByProjectIdAndKey(projectId, stageKey);
        if (stage == null) {
            throw BusinessException.of("项目阶段不存在");
        }

        try {
            // 获取表单事件处理器
            FormEventHandler handler = formEventHandlerFactory.getHandler(formKey);

            // 表单提交前验证
            handler.validateFormData(projectId, stageKey, formKey, formData, operation);

            // 处理表单中的文件（首次提交，没有之前的数据进行对比）
            List<Map<String, Object>> processedFiles = documentService.processFormFiles(
                    projectId, stageKey, formKey, formData, operation);

            // 将处理后的文件信息添加到表单数据中
            if (!processedFiles.isEmpty()) {
                formData.put("processedFiles", processedFiles);
                log.info("表单文件处理完成: projectId={}, 文件数量={}, 表单数据已更新", projectId, processedFiles.size());
            }

            // 更新阶段流程中的表单数据
            updateStageProcessFormData(stage, formKey, formData, "completed");

            // 处理表单提交事件
            handler.handleFormSubmit(projectId, stageKey, formKey, formData, operation);

            // 完成工作流中的UserTask，非主要阶段不走工作流
            if (stage.getIsMainProcess()) {
                completeWorkflowTask(projectId, formKey, formData, operation);
            }

            // 表单提交后处理
            handler.afterFormSubmit(projectId, stageKey, formKey, formData, operation);

            // 完成当前阶段
            completeStage(projectId, stageKey);

            log.info("项目表单提交成功: projectId={}, stageKey={}, formKey={}, operation={}", projectId, stageKey, formKey, operation);

        } catch (Exception e) {
            log.error("提交项目表单失败: projectId={}, stageKey={}, formKey={}", projectId, stageKey, formKey, e);
            throw BusinessException.of("提交项目表单失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void updateStageFormData(Long projectId, String stageKey, String formKey, JSONObject formData) {
        if (projectId == null) {
            throw BusinessException.of("项目ID不能为空");
        }
        if (stageKey == null || stageKey.trim().isEmpty()) {
            throw BusinessException.of("阶段Key不能为空");
        }
        if (formKey == null || formKey.trim().isEmpty()) {
            throw BusinessException.of("表单Key不能为空");
        }
        if (formData == null) {
            throw BusinessException.of("表单数据不能为空");
        }

        // 获取项目阶段
        ProjectProcess stage = getStageByProjectIdAndKey(projectId, stageKey);
        if (stage == null) {
            throw BusinessException.of("项目阶段不存在");
        }

        try {
            // 获取表单事件处理器
            FormEventHandler handler = formEventHandlerFactory.getHandler(formKey);

            // 获取编辑前的表单数据（用于对比文件变化）
            JSONObject previousFormData = getPreviousFormData(projectId, stageKey, formKey);

            // 处理表单中的文件变化（编辑时需要处理文件变化，包括删除，会直接修改formData中的文件信息）
            List<Map<String, Object>> processedFiles = documentService.processFormFilesWithChanges(
                    projectId, stageKey, formKey, formData, previousFormData, "edit");

            // 将处理后的文件信息添加到表单数据中
            if (!processedFiles.isEmpty()) {
                formData.put("processedFiles", processedFiles);
                log.info("表单编辑文件变化处理完成: projectId={}, 文件数量={}, 表单数据已更新", projectId, processedFiles.size());
            }

            // 处理表单更新事件（新增的更新事件处理）
            handler.handleFormUpdate(projectId, stageKey, formKey, formData, previousFormData);

            // 处理表单编辑事件（保持向后兼容）
            handler.handleFormEdit(projectId, stageKey, formKey, formData);

            // 更新阶段流程中的表单数据（不改变状态）
            updateStageProcessFormData(stage, formKey, formData, null);

            log.info("项目表单编辑成功: projectId={}, stageKey={}, formKey={}", projectId, stageKey, formKey);

        } catch (Exception e) {
            log.error("编辑项目表单失败: projectId={}, stageKey={}, formKey={}", projectId, stageKey, formKey, e);
            throw BusinessException.of("编辑项目表单失败: " + e.getMessage(), e);
        }
    }

    @Override
    public JSONObject getStageFormData(Long projectId, String stageKey, String formKey) {
        if (projectId == null) {
            throw BusinessException.of("项目ID不能为空");
        }
        if (stageKey == null || stageKey.trim().isEmpty()) {
            throw BusinessException.of("阶段Key不能为空");
        }
        if (formKey == null || formKey.trim().isEmpty()) {
            throw BusinessException.of("表单Key不能为空");
        }

        // 获取项目阶段
        ProjectProcess stage = getStageByProjectIdAndKey(projectId, stageKey);
        if (stage == null) {
            throw BusinessException.of("项目阶段不存在");
        }

        return getStageProcessFormData(stage, formKey);
    }

    /**
     * 更新阶段流程中的表单数据
     */
    private void updateStageProcessFormData(ProjectProcess stage, String formKey,
                                            JSONObject formData, String newStatus) {
        try {
            JSONObject stageProcessJson = stage.getStageProcess();
            if (stageProcessJson == null || stageProcessJson.isEmpty()) {
                throw BusinessException.of("阶段流程配置为空");
            }

            // 获取表单列表，支持多种格式
            JSONArray stageProcessList = stageProcessJson.getJSONArray("forms");


            if (stageProcessList == null) {
                throw BusinessException.of("阶段流程配置格式错误");
            }

            // 查找并更新对应的表单
            boolean formFound = false;
            for (int i = 0; i < stageProcessList.size(); i++) {
                JSONObject formConfig = stageProcessList.getJSONObject(i);
                if (formConfig != null && formKey.equals(formConfig.getString("formKey"))) {
                    formConfig.put("data", formData);
                    if (newStatus != null) {
                        formConfig.put("status", newStatus);
                    }
                    formFound = true;
                    break;
                }
            }

            if (!formFound) {
                throw BusinessException.of("表单配置不存在: " + formKey);
            }

            // 更新阶段流程JSON
            if (stageProcessJson.containsKey("forms")) {
                stageProcessJson.put("forms", stageProcessList);
                stage.setStageProcess(stageProcessJson);
            } else {
                // 如果原来不是forms格式，更新为forms格式
                JSONObject newStageProcess = new JSONObject();
                newStageProcess.put("forms", stageProcessList);
                stage.setStageProcess(newStageProcess);
            }
            updateById(stage);

        } catch (Exception e) {
            log.error("更新阶段流程表单数据失败: stageKey={}, formKey={}",
                    stage.getStageKey(), formKey, e);
            throw BusinessException.of("更新表单数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取阶段流程中的表单数据
     */
    private JSONObject getStageProcessFormData(ProjectProcess stage, String formKey) {
        try {
            JSONObject stageProcessJson = stage.getStageProcess();
            if (stageProcessJson == null || stageProcessJson.isEmpty()) {
                return new JSONObject();
            }

            // 获取表单列表，支持多种格式
            JSONArray stageProcessList;
            if (stageProcessJson.containsKey("forms")) {
                // 新格式：{forms: [...]}
                stageProcessList = stageProcessJson.getJSONArray("forms");
            } else {
                // 兼容旧格式：假设整个对象就是一个表单配置
                stageProcessList = new JSONArray();
                stageProcessList.add(stageProcessJson);
            }

            if (stageProcessList == null) {
                return new JSONObject();
            }

            // 查找对应的表单数据
            for (int i = 0; i < stageProcessList.size(); i++) {
                JSONObject formConfig = stageProcessList.getJSONObject(i);
                if (formConfig != null && formKey.equals(formConfig.getString("formKey"))) {
                    JSONObject data = formConfig.getJSONObject("data");
                    if (data != null) {
                        return data;
                    }
                    break;
                }
            }

            return new JSONObject();

        } catch (Exception e) {
            log.error("获取阶段流程表单数据失败: stageKey={}, formKey={}",
                    stage.getStageKey(), formKey, e);
            return new JSONObject();
        }
    }

    /**
     * 完成工作流任务
     *
     * @param projectId 项目ID
     * @param formKey   表单Key
     * @param formData  表单数据
     * @param operation 操作类型
     */
    private void completeWorkflowTask(Long projectId, String formKey, JSONObject formData, String operation) {
        try {
            String businessKey = "project_" + projectId;

            // 查找匹配的活跃任务
            List<org.camunda.bpm.engine.task.Task> tasks = taskService.createTaskQuery()
                    .processInstanceBusinessKey(businessKey)
                    .active()
                    .initializeFormKeys()
                    .list();

            log.info("查找工作流任务: businessKey={}, formKey={}, 找到任务数量={}", businessKey, formKey, tasks.size());

            for (org.camunda.bpm.engine.task.Task task : tasks) {
                if (formKey.equals(task.getFormKey())) {
                    // 准备任务变量
                    JSONObject taskVariables = new JSONObject();
                    taskVariables.put("formKey", formKey);
                    taskVariables.put("formData", formData);
                    taskVariables.put("operation", operation);
                    taskVariables.put("submitTime", LocalDateTime.now().toString());

                    // 完成任务
                    taskService.complete(task.getId(), taskVariables);

                    log.info("工作流任务完成成功: taskId={}, taskName={}, formKey={}, operation={}",
                            task.getId(), task.getName(), formKey, operation);
                    return;
                }
            }

            log.warn("未找到匹配的工作流任务: businessKey={}, formKey={}", businessKey, formKey);

        } catch (Exception e) {
            log.error("完成工作流任务失败: projectId={}, formKey={}", projectId, formKey, e);
            // 不抛出异常，避免影响表单数据保存
        }
    }

    /**
     * 从表单数据中提取操作类型
     *
     * @param formData 表单数据
     * @return 操作类型
     */
    private String extractOperationFromFormData(JSONObject formData) {
        if (formData == null) {
            return "submit"; // 默认操作
        }

        // 尝试从不同的字段名中提取operation
        Object operation = formData.get("operation");
        if (operation == null) {
            operation = formData.get("action");
        }
        if (operation == null) {
            operation = formData.get("op");
        }

        if (operation != null) {
            String operationStr = operation.toString().trim();
            if (!operationStr.isEmpty()) {
                log.debug("从表单数据中提取到操作类型: {}", operationStr);
                return operationStr;
            }
        }

        // 如果没有找到operation字段，返回默认值
        log.debug("表单数据中未找到操作类型，使用默认值: submit");
        return "submit";
    }

    /**
     * 获取编辑前的表单数据
     *
     * @param projectId 项目ID
     * @param stageKey  阶段Key
     * @param formKey   表单Key
     * @return 之前的表单数据
     */
    private JSONObject getPreviousFormData(Long projectId, String stageKey, String formKey) {
        try {
            // 获取项目阶段信息
            ProjectProcess stage = getStageByProjectIdAndKey(projectId, stageKey);
            if (stage == null) {
                log.warn("未找到项目阶段: projectId={}, stageKey={}", projectId, stageKey);
                return new JSONObject();
            }

            // 从阶段流程中获取之前保存的表单数据
            JSONObject previousFormData = getStageProcessFormData(stage, formKey);

            log.debug("获取到之前的表单数据: projectId={}, stageKey={}, formKey={}, 数据大小={}",
                    projectId, stageKey, formKey, previousFormData.size());

            return previousFormData;

        } catch (Exception e) {
            log.error("获取之前的表单数据失败: projectId={}, stageKey={}, formKey={}", projectId, stageKey, formKey, e);
            return new JSONObject();
        }
    }


}
