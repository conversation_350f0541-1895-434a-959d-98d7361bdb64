package com.vwatj.ppms.ai.function;

import com.vwatj.ppms.dto.CreateTaskDTO;
import com.vwatj.ppms.dto.TaskQueryDTO;
import com.vwatj.ppms.entity.ProjectTask;
import com.vwatj.ppms.enums.TaskStatusEnum;
import com.vwatj.ppms.service.ProjectTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 任务管理相关的AI功能函数
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskManagementFunctions {

    private final ProjectTaskService projectTaskService;

    /**
     * 查询任务列表
     */
    public Function<TaskQueryRequest, String> queryTasks() {
        return request -> {
            try {
                TaskQueryDTO queryDTO = new TaskQueryDTO();
                queryDTO.setProjectId(request.getProjectId());
                queryDTO.setTitle(request.getTitle());
                queryDTO.setStatus(request.getStatus());
                queryDTO.setAssignee(request.getAssignee());
                queryDTO.setPriority(request.getPriority());
                queryDTO.setPageNum(request.getPageNum() != null ? request.getPageNum() : 1);
                queryDTO.setPageSize(request.getPageSize() != null ? request.getPageSize() : 10);

                var result = projectTaskService.getTaskPage(queryDTO);
                
                StringBuilder response = new StringBuilder();
                response.append(String.format("找到 %d 个任务（共 %d 个）：\n", 
                    result.getRecords().size(), result.getTotal()));
                
                for (ProjectTask task : result.getRecords()) {
                    response.append(String.format("- %s (ID: %d, 状态: %s, 负责人: %s, 优先级: %s)\n",
                        task.getTitle(), task.getId(), task.getStatus(), 
                        task.getAssignee(), task.getPriority()));
                }
                
                return response.toString();
            } catch (Exception e) {
                log.error("查询任务失败", e);
                return "查询任务时发生错误: " + e.getMessage();
            }
        };
    }

    /**
     * 获取任务详情
     */
    public Function<Long, String> getTaskDetails() {
        return taskId -> {
            try {
                ProjectTask task = projectTaskService.getById(taskId);
                if (task == null) {
                    return "任务不存在 (ID: " + taskId + ")";
                }
                
                return String.format("""
                    任务详情：
                    - 标题: %s
                    - 描述: %s
                    - 状态: %s
                    - 负责人: %s
                    - 优先级: %s
                    - 类型: %s
                    - 项目ID: %d
                    - 开始时间: %s
                    - 结束时间: %s
                    - 预计工时: %s
                    - 实际工时: %s
                    - 进度: %d%%
                    """,
                    task.getTitle(),
                    task.getDescription(),
                    task.getStatus(),
                    task.getAssignee(),
                    task.getPriority(),
                    task.getType(),
                    task.getProjectId(),
                    task.getStartDate(),
                    task.getEndDate(),
                    task.getEstimatedHours(),
                    task.getActualHours(),
                    task.getProgress()
                );
            } catch (Exception e) {
                log.error("获取任务详情失败", e);
                return "获取任务详情时发生错误: " + e.getMessage();
            }
        };
    }

    /**
     * 创建任务
     */
    public Function<CreateTaskRequest, String> createTask() {
        return request -> {
            try {
                CreateTaskDTO createDTO = new CreateTaskDTO();
                createDTO.setProjectId(request.getProjectId());
                createDTO.setTitle(request.getTitle());
                createDTO.setDescription(request.getDescription());
                createDTO.setAssignee(request.getAssignee());
                createDTO.setPriority(request.getPriority());
                createDTO.setType(request.getType());
                createDTO.setStartDate(request.getStartDate());
                createDTO.setEndDate(request.getEndDate());
                createDTO.setEstimatedHours(request.getEstimatedHours());

                ProjectTask task = projectTaskService.createTask(createDTO);
                
                return String.format("任务创建成功！\n任务ID: %d\n任务标题: %s\n状态: %s\n负责人: %s",
                    task.getId(), task.getTitle(), task.getStatus(), task.getAssignee());
            } catch (Exception e) {
                log.error("创建任务失败", e);
                return "创建任务时发生错误: " + e.getMessage();
            }
        };
    }

    /**
     * 更新任务状态
     */
    public Function<UpdateTaskStatusRequest, String> updateTaskStatus() {
        return request -> {
            try {
                projectTaskService.updateTaskStatus(request.getTaskId(), 
                    request.getStatus(), request.getComment(), request.getOperation());
                return String.format("任务状态更新成功！任务ID: %d, 新状态: %s", 
                    request.getTaskId(), request.getStatus());
            } catch (Exception e) {
                log.error("更新任务状态失败", e);
                return "更新任务状态时发生错误: " + e.getMessage();
            }
        };
    }

    /**
     * 分配任务
     */
    public Function<AssignTaskRequest, String> assignTask() {
        return request -> {
            try {
                projectTaskService.assignTask(request.getTaskId(), request.getAssignee());
                return String.format("任务分配成功！任务ID: %d, 新负责人: %s", 
                    request.getTaskId(), request.getAssignee());
            } catch (Exception e) {
                log.error("分配任务失败", e);
                return "分配任务时发生错误: " + e.getMessage();
            }
        };
    }

    /**
     * 获取任务统计信息
     */
    public Function<Long, String> getTaskStats() {
        return projectId -> {
            try {
                Map<String, Object> stats = projectTaskService.getTaskStats(projectId);
                
                StringBuilder response = new StringBuilder();
                if (projectId != null) {
                    response.append(String.format("项目 %d 的任务统计：\n", projectId));
                } else {
                    response.append("全部任务统计：\n");
                }
                
                stats.forEach((key, value) -> 
                    response.append(String.format("- %s: %s\n", key, value)));
                
                return response.toString();
            } catch (Exception e) {
                log.error("获取任务统计失败", e);
                return "获取任务统计时发生错误: " + e.getMessage();
            }
        };
    }

    // 请求类定义
    public static class TaskQueryRequest {
        public Long projectId;
        public String title;
        public String status;
        public String assignee;
        public String priority;
        public Integer pageNum;
        public Integer pageSize;
        
        // getters
        public Long getProjectId() { return projectId; }
        public String getTitle() { return title; }
        public String getStatus() { return status; }
        public String getAssignee() { return assignee; }
        public String getPriority() { return priority; }
        public Integer getPageNum() { return pageNum; }
        public Integer getPageSize() { return pageSize; }
    }

    public static class CreateTaskRequest {
        public Long projectId;
        public String title;
        public String description;
        public String assignee;
        public String priority;
        public String type;
        public Long startDate;
        public Long endDate;
        public Integer estimatedHours;
        
        // getters
        public Long getProjectId() { return projectId; }
        public String getTitle() { return title; }
        public String getDescription() { return description; }
        public String getAssignee() { return assignee; }
        public String getPriority() { return priority; }
        public String getType() { return type; }
        public Long getStartDate() { return startDate; }
        public Long getEndDate() { return endDate; }
        public Integer getEstimatedHours() { return estimatedHours; }
    }

    public static class UpdateTaskStatusRequest {
        public Long taskId;
        public TaskStatusEnum status;
        public String comment;
        public String operation;
        
        // getters
        public Long getTaskId() { return taskId; }
        public TaskStatusEnum getStatus() { return status; }
        public String getComment() { return comment; }
        public String getOperation() { return operation; }
    }

    public static class AssignTaskRequest {
        public Long taskId;
        public String assignee;
        
        // getters
        public Long getTaskId() { return taskId; }
        public String getAssignee() { return assignee; }
    }
}
