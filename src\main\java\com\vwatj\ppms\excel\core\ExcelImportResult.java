package com.vwatj.ppms.excel.core;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * Excel导入结果
 */
@Getter
public class ExcelImportResult {
    // Getters and Setters
    private int total = 0;
    private int success = 0;
    private int failed = 0;
    private int skipped = 0;
    private List<ExcelImportError> errors = new ArrayList<>();

    public void setTotal(int total) {
        this.total = total;
    }

    public void incrementTotal() {
        this.total++;
    }

    public void setSuccess(int success) {
        this.success = success;
    }

    public void incrementSuccess() {
        this.success++;
    }

    public void setFailed(int failed) {
        this.failed = failed;
    }

    public void incrementFailed() {
        this.failed++;
    }

    public void setSkipped(int skipped) {
        this.skipped = skipped;
    }

    public void incrementSkipped() {
        this.skipped++;
    }

    public void setErrors(List<ExcelImportError> errors) {
        this.errors = errors;
    }
}
