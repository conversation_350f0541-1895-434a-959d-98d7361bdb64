package com.vwatj.ppms.exception;

/**
 * 业务异常类
 * 用于处理业务逻辑中的异常情况，会被全局异常处理器捕获并返回400错误
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
public class BusinessException extends RuntimeException {

    private String code;
    private String message;

    public BusinessException(String message) {
        super(message);
        this.message = message;
        this.code = "BUSINESS_ERROR";
    }

    public BusinessException(String code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.message = message;
        this.code = "BUSINESS_ERROR";
    }

    public BusinessException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * 创建业务异常的便捷方法
     */
    public static BusinessException of(String message) {
        return new BusinessException(message);
    }

    public static BusinessException of(String code, String message) {
        return new BusinessException(code, message);
    }

    public static BusinessException of(String message, Throwable cause) {
        return new BusinessException(message, cause);
    }

    public static BusinessException of(String code, String message, Throwable cause) {
        return new BusinessException(code, message, cause);
    }
}
