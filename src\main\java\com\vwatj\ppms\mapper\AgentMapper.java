package com.vwatj.ppms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.vwatj.ppms.entity.Agent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * Agent Mapper接口
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Mapper
public interface AgentMapper extends BaseMapper<Agent> {
    
    /**
     * 分页查询Agent
     */
    IPage<Agent> selectAgentPage(Page<Agent> page,
                                @Param("keyword") String keyword,
                                @Param("status") Integer status,
                                @Param("type") String type,
                                @Param("creatorId") Long creatorId);
    
    /**
     * 根据名称查询Agent
     */
    Agent selectByName(@Param("name") String name);
}
