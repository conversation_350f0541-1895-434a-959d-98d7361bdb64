<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1xj2zxl" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.37.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.23.0">
  <bpmn:process id="task_flow" name="task_flow" isExecutable="true" camunda:historyTimeToLive="0">
    <bpmn:startEvent id="StartEvent_1" name="start">
      <bpmn:outgoing>Flow_1ukwdjn</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_StartTask" sourceRef="OpenTask" targetRef="Gateway_0qvhhf1" />
    <bpmn:sequenceFlow id="Flow_FinishTask" sourceRef="InProgressTask" targetRef="Gateway_06mbwpp" />
    <bpmn:sequenceFlow id="Flow_CloseTask" sourceRef="ResolvedTask" targetRef="Gateway_0os9xvi" />
    <bpmn:sequenceFlow id="Flow_1ukwdjn" sourceRef="StartEvent_1" targetRef="Event_0cxbb84" />
    <bpmn:sequenceFlow id="Flow_0qc4fpj" name="Resolve" sourceRef="Gateway_0qvhhf1" targetRef="Event_1frmxx3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "resolve"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_08jabdx" name="Close" sourceRef="Gateway_0qvhhf1" targetRef="Event_06w8ow2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "close"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="OpenTask" name="Operation Task" camunda:formKey="task-operation">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0d06vsh</bpmn:incoming>
      <bpmn:outgoing>Flow_StartTask</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="InProgressTask" name="Operation Task" camunda:formKey="task-operation">
      <bpmn:incoming>Flow_1eglmkb</bpmn:incoming>
      <bpmn:outgoing>Flow_FinishTask</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:intermediateThrowEvent id="Event_0cxbb84" name="OPEN" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{taskBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>open</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1ukwdjn</bpmn:incoming>
      <bpmn:incoming>Flow_0fo7y2z</bpmn:incoming>
      <bpmn:incoming>Flow_14rqw6y</bpmn:incoming>
      <bpmn:incoming>Flow_10mg0ze</bpmn:incoming>
      <bpmn:outgoing>Flow_0d06vsh</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_0d06vsh" sourceRef="Event_0cxbb84" targetRef="OpenTask" />
    <bpmn:intermediateThrowEvent id="Event_0skvj1t" name="IN PROGRESS">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{taskBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>inprogress</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0bk5tkc</bpmn:incoming>
      <bpmn:incoming>Flow_1yk1vfn</bpmn:incoming>
      <bpmn:outgoing>Flow_1eglmkb</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateThrowEvent id="Event_1frmxx3" name="TO VERIFY">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{taskBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>toverify</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0qc4fpj</bpmn:incoming>
      <bpmn:incoming>Flow_1el3oai</bpmn:incoming>
      <bpmn:incoming>Flow_107gs1y</bpmn:incoming>
      <bpmn:outgoing>Flow_1jpliop</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1jpliop" sourceRef="Event_1frmxx3" targetRef="ResolvedTask" />
    <bpmn:userTask id="ResolvedTask" name="Operation Task" camunda:formKey="task-operation">
      <bpmn:incoming>Flow_1jpliop</bpmn:incoming>
      <bpmn:outgoing>Flow_CloseTask</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:intermediateThrowEvent id="Event_06w8ow2" name="CLOSED" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{taskBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>close</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_08jabdx</bpmn:incoming>
      <bpmn:incoming>Flow_0mds7o1</bpmn:incoming>
      <bpmn:incoming>Flow_14xfygl</bpmn:incoming>
      <bpmn:outgoing>Flow_0x55qa3</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:exclusiveGateway id="Gateway_0qvhhf1">
      <bpmn:incoming>Flow_StartTask</bpmn:incoming>
      <bpmn:outgoing>Flow_0qc4fpj</bpmn:outgoing>
      <bpmn:outgoing>Flow_08jabdx</bpmn:outgoing>
      <bpmn:outgoing>Flow_0bk5tkc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_06mbwpp">
      <bpmn:incoming>Flow_FinishTask</bpmn:incoming>
      <bpmn:outgoing>Flow_1el3oai</bpmn:outgoing>
      <bpmn:outgoing>Flow_0fo7y2z</bpmn:outgoing>
      <bpmn:outgoing>Flow_0mds7o1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1el3oai" name="Resolve" sourceRef="Gateway_06mbwpp" targetRef="Event_1frmxx3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "resolve"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0fo7y2z" name="Re-Start" sourceRef="Gateway_06mbwpp" targetRef="Event_0cxbb84">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "reopen"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0mds7o1" name="Close Task" sourceRef="Gateway_06mbwpp" targetRef="Event_06w8ow2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "close"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0os9xvi">
      <bpmn:incoming>Flow_CloseTask</bpmn:incoming>
      <bpmn:outgoing>Flow_14xfygl</bpmn:outgoing>
      <bpmn:outgoing>Flow_1yk1vfn</bpmn:outgoing>
      <bpmn:outgoing>Flow_14rqw6y</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_14xfygl" sourceRef="Gateway_0os9xvi" targetRef="Event_06w8ow2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "close"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1eglmkb" sourceRef="Event_0skvj1t" targetRef="InProgressTask" />
    <bpmn:sequenceFlow id="Flow_0bk5tkc" sourceRef="Gateway_0qvhhf1" targetRef="Event_0skvj1t">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "progress"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1yk1vfn" sourceRef="Gateway_0os9xvi" targetRef="Event_0skvj1t">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "progress"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_14rqw6y" name="Re-Open" sourceRef="Gateway_0os9xvi" targetRef="Event_0cxbb84">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "reopen"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_0xptmhg" name="Operation Task" camunda:formKey="task-operation">
      <bpmn:incoming>Flow_0x55qa3</bpmn:incoming>
      <bpmn:outgoing>Flow_0lcy9g8</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0x55qa3" sourceRef="Event_06w8ow2" targetRef="Activity_0xptmhg" />
    <bpmn:exclusiveGateway id="Gateway_1ke9ths">
      <bpmn:incoming>Flow_0lcy9g8</bpmn:incoming>
      <bpmn:outgoing>Flow_10mg0ze</bpmn:outgoing>
      <bpmn:outgoing>Flow_107gs1y</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0lcy9g8" sourceRef="Activity_0xptmhg" targetRef="Gateway_1ke9ths" />
    <bpmn:sequenceFlow id="Flow_10mg0ze" name="Re-Open" sourceRef="Gateway_1ke9ths" targetRef="Event_0cxbb84">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "reopen"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_107gs1y" sourceRef="Gateway_1ke9ths" targetRef="Event_1frmxx3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "resolve"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="task_flow">
      <bpmndi:BPMNShape id="StartEvent_1_di" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="369" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="159" y="412" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_01r7vpk_di" bpmnElement="OpenTask">
        <dc:Bounds x="390" y="347" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_13yh3zz_di" bpmnElement="InProgressTask">
        <dc:Bounds x="521" y="650" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0cxbb84_di" bpmnElement="Event_0cxbb84">
        <dc:Bounds x="292" y="369" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="294" y="345" width="32" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0skvj1t_di" bpmnElement="Event_0skvj1t">
        <dc:Bounds x="553" y="542" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="465" y="553" width="78" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1frmxx3_di" bpmnElement="Event_1frmxx3">
        <dc:Bounds x="872" y="369" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="861" y="345" width="58" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0os2uze_di" bpmnElement="ResolvedTask">
        <dc:Bounds x="1010" y="347" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_06w8ow2_di" bpmnElement="Event_06w8ow2">
        <dc:Bounds x="1322" y="369" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1317" y="412" width="46" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0qvhhf1_di" bpmnElement="Gateway_0qvhhf1" isMarkerVisible="true">
        <dc:Bounds x="546" y="362" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_06mbwpp_di" bpmnElement="Gateway_06mbwpp" isMarkerVisible="true">
        <dc:Bounds x="546" y="765" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0os9xvi_di" bpmnElement="Gateway_0os9xvi" isMarkerVisible="true">
        <dc:Bounds x="1215" y="362" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1vcpeh0" bpmnElement="Activity_0xptmhg">
        <dc:Bounds x="1450" y="347" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0coa62e" bpmnElement="Gateway_1ke9ths" isMarkerVisible="true">
        <dc:Bounds x="1475" y="145" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1ukwdjn_di" bpmnElement="Flow_1ukwdjn">
        <di:waypoint x="188" y="387" />
        <di:waypoint x="292" y="387" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0d06vsh_di" bpmnElement="Flow_0d06vsh">
        <di:waypoint x="328" y="387" />
        <di:waypoint x="390" y="387" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_StartTask_di" bpmnElement="Flow_StartTask">
        <di:waypoint x="490" y="387" />
        <di:waypoint x="546" y="387" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="337" y="222" width="46" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1eglmkb_di" bpmnElement="Flow_1eglmkb">
        <di:waypoint x="571" y="578" />
        <di:waypoint x="571" y="650" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_FinishTask_di" bpmnElement="Flow_FinishTask">
        <di:waypoint x="571" y="730" />
        <di:waypoint x="571" y="765" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="651" y="222" width="49" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fo7y2z_di" bpmnElement="Flow_0fo7y2z">
        <di:waypoint x="546" y="790" />
        <di:waypoint x="310" y="790" />
        <di:waypoint x="310" y="405" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="278" y="591" width="42" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14rqw6y_di" bpmnElement="Flow_14rqw6y">
        <di:waypoint x="1240" y="362" />
        <di:waypoint x="1240" y="270" />
        <di:waypoint x="310" y="270" />
        <di:waypoint x="310" y="369" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="752" y="252" width="45" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10mg0ze_di" bpmnElement="Flow_10mg0ze">
        <di:waypoint x="1500" y="145" />
        <di:waypoint x="1500" y="100" />
        <di:waypoint x="310" y="100" />
        <di:waypoint x="310" y="369" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="883" y="82" width="45" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bk5tkc_di" bpmnElement="Flow_0bk5tkc">
        <di:waypoint x="571" y="412" />
        <di:waypoint x="571" y="542" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yk1vfn_di" bpmnElement="Flow_1yk1vfn">
        <di:waypoint x="1240" y="412" />
        <di:waypoint x="1240" y="560" />
        <di:waypoint x="589" y="560" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qc4fpj_di" bpmnElement="Flow_0qc4fpj">
        <di:waypoint x="596" y="387" />
        <di:waypoint x="872" y="387" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="714" y="369" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1el3oai_di" bpmnElement="Flow_1el3oai">
        <di:waypoint x="596" y="790" />
        <di:waypoint x="890" y="790" />
        <di:waypoint x="890" y="405" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="905" y="587" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_107gs1y_di" bpmnElement="Flow_107gs1y">
        <di:waypoint x="1475" y="170" />
        <di:waypoint x="890" y="170" />
        <di:waypoint x="890" y="369" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jpliop_di" bpmnElement="Flow_1jpliop">
        <di:waypoint x="908" y="387" />
        <di:waypoint x="1010" y="387" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_CloseTask_di" bpmnElement="Flow_CloseTask">
        <di:waypoint x="1110" y="387" />
        <di:waypoint x="1215" y="387" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1234.9999999999995" y="222" width="50" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08jabdx_di" bpmnElement="Flow_08jabdx">
        <di:waypoint x="571" y="362" />
        <di:waypoint x="571" y="240" />
        <di:waypoint x="1340" y="240" />
        <di:waypoint x="1340" y="369" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="941" y="222" width="29" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mds7o1_di" bpmnElement="Flow_0mds7o1">
        <di:waypoint x="596" y="790" />
        <di:waypoint x="1340" y="790" />
        <di:waypoint x="1340" y="405" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1301" y="590" width="55" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14xfygl_di" bpmnElement="Flow_14xfygl">
        <di:waypoint x="1265" y="387" />
        <di:waypoint x="1322" y="387" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0x55qa3_di" bpmnElement="Flow_0x55qa3">
        <di:waypoint x="1358" y="387" />
        <di:waypoint x="1450" y="387" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lcy9g8_di" bpmnElement="Flow_0lcy9g8">
        <di:waypoint x="1500" y="347" />
        <di:waypoint x="1500" y="195" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
