<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vwatj.ppms.mapper.DocumentMapper">

    <!-- 分页查询文档 -->
    <select id="selectDocumentPage" resultType="com.vwatj.ppms.entity.Document">
        SELECT * FROM document
        <where>
            <if test="keyword != null and keyword != ''">
                AND (title LIKE CONCAT('%', #{keyword}, '%') 
                OR content LIKE CONCAT('%', #{keyword}, '%')
                OR summary LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="projectId != null">
                AND project_id = #{projectId}
            </if>
            <if test="category != null and category != ''">
                AND category = #{category}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="fileType != null and fileType != ''">
                AND file_type = #{fileType}
            </if>
            <if test="author != null and author != ''">
                AND author = #{author}
            </if>
            <if test="reviewer != null and reviewer != ''">
                AND reviewer = #{reviewer}
            </if>
            <if test="approver != null and approver != ''">
                AND approver = #{approver}
            </if>
            <if test="tags != null and tags.size() &gt; 0">
                AND JSON_OVERLAPS(tags, #{tags})
            </if>
            <if test="uploadDateStart != null">
                AND created_at &gt;= #{uploadDateStart}
            </if>
            <if test="uploadDateEnd != null">
                AND created_at &lt;= #{uploadDateEnd}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 查询项目文档 -->
    <select id="selectProjectDocuments" resultType="com.vwatj.ppms.entity.Document">
        SELECT * FROM document WHERE project_id = #{projectId} ORDER BY created_at DESC
    </select>

    <!-- 根据标题查询文档 -->
    <select id="selectByTitle" resultType="com.vwatj.ppms.entity.Document">
        SELECT * FROM document WHERE title = #{title} AND project_id = #{projectId}
    </select>

    <!-- 统计文档数量按状态 -->
    <select id="countDocumentsByStatus" resultType="map">
        SELECT status, COUNT(*) as count FROM document 
        <where>
            <if test="projectId != null">
                AND project_id = #{projectId}
            </if>
        </where>
        GROUP BY status
    </select>

    <!-- 统计文档数量按类别 -->
    <select id="countDocumentsByCategory" resultType="map">
        SELECT category, COUNT(*) as count FROM document 
        <where>
            <if test="projectId != null">
                AND project_id = #{projectId}
            </if>
        </where>
        GROUP BY category
    </select>

    <!-- 统计文档数量按文件类型 -->
    <select id="countDocumentsByFileType" resultType="map">
        SELECT file_type, COUNT(*) as count FROM document 
        <where>
            <if test="projectId != null">
                AND project_id = #{projectId}
            </if>
        </where>
        GROUP BY file_type
    </select>

    <!-- 统计文档总大小 -->
    <select id="sumDocumentFileSize" resultType="long">
        SELECT COALESCE(SUM(file_size), 0) FROM document 
        <where>
            <if test="projectId != null">
                AND project_id = #{projectId}
            </if>
        </where>
    </select>

    <!-- 统计文档总查看次数 -->
    <select id="sumDocumentViewCount" resultType="long">
        SELECT COALESCE(SUM(view_count), 0) FROM document 
        <where>
            <if test="projectId != null">
                AND project_id = #{projectId}
            </if>
        </where>
    </select>

    <!-- 统计文档总下载次数 -->
    <select id="sumDocumentDownloadCount" resultType="long">
        SELECT COALESCE(SUM(download_count), 0) FROM document 
        <where>
            <if test="projectId != null">
                AND project_id = #{projectId}
            </if>
        </where>
    </select>

    <!-- 增加文档查看次数 -->
    <update id="incrementViewCount">
        UPDATE document SET view_count = view_count + 1 WHERE id = #{id}
    </update>

    <!-- 增加文档下载次数 -->
    <update id="incrementDownloadCount">
        UPDATE document SET download_count = download_count + 1 WHERE id = #{id}
    </update>

</mapper>
