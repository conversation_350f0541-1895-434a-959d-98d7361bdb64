package com.vwatj.ppms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 流程版本实体
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("process_version")
public class ProcessVersion {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 流程定义ID
     */
    private Long processDefinitionId;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 版本描述
     */
    private String description;

    /**
     * BPMN文件名
     */
    private String fileName;

    /**
     * BPMN文件路径
     */
    private String filePath;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * Camunda部署ID（发布后生成）
     */
    private String deploymentId;

    /**
     * Camunda流程定义ID（发布后生成）
     */
    private String camundaProcessDefinitionId;

    /**
     * 版本状态：DRAFT(草稿), PUBLISHED(已发布), SUSPENDED(已挂起)
     */
    private String status;

    /**
     * 是否为当前发布版本
     */
    private Boolean isPublished;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
