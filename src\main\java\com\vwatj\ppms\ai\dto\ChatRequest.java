package com.vwatj.ppms.ai.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 聊天请求DTO
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public class ChatRequest {

    /**
     * 用户消息
     */
    private String message;

    /**
     * 会话ID（用于维持上下文）
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 项目ID（可选，用于项目相关的对话）
     */
    private Long projectId;

    /**
     * 任务ID（可选，用于任务相关的对话）
     */
    private Long taskId;

    /**
     * 聊天历史（可选）
     */
    private List<ChatMessage> history;

    /**
     * 额外的上下文信息
     */
    private Map<String, Object> context;

    /**
     * 是否需要调用业务功能
     */
    private Boolean enableFunctionCalling = true;

    /**
     * 聊天消息
     */
    @Data
    public static class ChatMessage {
        private String role; // user, assistant, system
        private String content;
        private Long timestamp;
    }
}
