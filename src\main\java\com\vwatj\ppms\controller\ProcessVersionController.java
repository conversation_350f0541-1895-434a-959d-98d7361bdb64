package com.vwatj.ppms.controller;

import com.vwatj.ppms.common.ApiResponse;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateProcessVersionDTO;
import com.vwatj.ppms.dto.ProcessVersionQueryDTO;
import com.vwatj.ppms.entity.ProcessVersion;
import com.vwatj.ppms.service.ProcessVersionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 流程版本控制器
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Slf4j
@RestController
@RequestMapping("/process-versions")
@RequiredArgsConstructor
public class ProcessVersionController {

    private final ProcessVersionService processVersionService;

    /**
     * 分页查询流程版本
     */
    @GetMapping
    public ApiResponse<PageResult<ProcessVersion>> getProcessVersions(ProcessVersionQueryDTO queryDTO) {
        PageResult<ProcessVersion> result = processVersionService.getProcessVersionPage(queryDTO);
        return ApiResponse.success(result);
    }

    /**
     * 根据ID查询流程版本
     */
    @GetMapping("/{id}")
    public ApiResponse<ProcessVersion> getProcessVersion(@PathVariable Long id) {
        ProcessVersion processVersion = processVersionService.getById(id);
        if (processVersion == null) {
            return ApiResponse.notFound("流程版本不存在");
        }
        return ApiResponse.success(processVersion);
    }



    /**
     * 删除流程版本
     */
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteProcessVersion(@PathVariable Long id) {
        try {
            processVersionService.deleteProcessVersion(id);
            return ApiResponse.success("流程版本删除成功");
        } catch (Exception e) {
            log.error("删除流程版本失败", e);
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 根据流程定义ID和版本号查询
     */
    @GetMapping("/by-definition/{processDefinitionId}/version/{version}")
    public ApiResponse<ProcessVersion> getByProcessDefinitionIdAndVersion(
            @PathVariable Long processDefinitionId,
            @PathVariable Integer version) {
        ProcessVersion processVersion = processVersionService.getByProcessDefinitionIdAndVersion(processDefinitionId, version);
        if (processVersion == null) {
            return ApiResponse.notFound("流程版本不存在");
        }
        return ApiResponse.success(processVersion);
    }

    /**
     * 根据流程定义ID查询当前发布版本
     */
    @GetMapping("/published/{processDefinitionId}")
    public ApiResponse<ProcessVersion> getPublishedVersion(@PathVariable Long processDefinitionId) {
        ProcessVersion processVersion = processVersionService.getPublishedVersionByProcessDefinitionId(processDefinitionId);
        if (processVersion == null) {
            return ApiResponse.notFound("未找到已发布的版本");
        }
        return ApiResponse.success(processVersion);
    }

    /**
     * 获取BPMN文件内容
     */
    @GetMapping("/{id}/bpmn-content")
    public ApiResponse<String> getBpmnContent(@PathVariable Long id) {
        try {
            String content = processVersionService.getBpmnContent(id);
            return ApiResponse.success(content);
        } catch (Exception e) {
            log.error("获取BPMN文件内容失败", e);
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 下载BPMN文件
     */
    @GetMapping("/{id}/download")
    public ResponseEntity<byte[]> downloadBpmnFile(@PathVariable Long id) {
        try {
            ProcessVersion processVersion = processVersionService.getById(id);
            if (processVersion == null) {
                return ResponseEntity.notFound().build();
            }

            byte[] fileContent = processVersionService.downloadBpmnFile(id);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION,
                            "attachment; filename=\"" + processVersion.getFileName() + "\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(fileContent);
        } catch (Exception e) {
            log.error("下载BPMN文件失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 激活版本
     */
    @PostMapping("/{id}/activate")
    public ApiResponse<String> activateVersion(@PathVariable Long id) {
        try {
            processVersionService.activateVersion(id);
            return ApiResponse.success("版本激活成功");
        } catch (Exception e) {
            log.error("激活版本失败", e);
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 挂起版本
     */
    @PostMapping("/{id}/suspend")
    public ApiResponse<String> suspendVersion(@PathVariable Long id) {
        try {
            processVersionService.suspendVersion(id);
            return ApiResponse.success("版本挂起成功");
        } catch (Exception e) {
            log.error("挂起版本失败", e);
            return ApiResponse.badRequest(e.getMessage());
        }
    }
}
