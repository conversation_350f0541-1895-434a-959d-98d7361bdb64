package com.vwatj.ppms.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.*;
import com.vwatj.ppms.entity.ProcessDefinition;
import com.vwatj.ppms.entity.ProcessVersion;
import com.vwatj.ppms.enums.ProcessStatus;
import com.vwatj.ppms.mapper.ProcessDefinitionMapper;
import com.vwatj.ppms.mapper.ProcessVersionMapper;
import com.vwatj.ppms.service.ProcessDefinitionService;
import com.vwatj.ppms.service.ProcessVersionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.repository.Deployment;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.time.LocalDateTime;

/**
 * 流程定义服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProcessDefinitionServiceImpl extends ServiceImpl<ProcessDefinitionMapper, ProcessDefinition>
        implements ProcessDefinitionService {

    private final ProcessDefinitionMapper processDefinitionMapper;
    private final ProcessVersionMapper processVersionMapper;
    private final ProcessVersionService processVersionService;
    private final RepositoryService repositoryService;

    @Value("${ppms.upload.path}")
    private String uploadPath;

    @Override
    public PageResult<ProcessDefinition> getProcessDefinitionPage(ProcessDefinitionQueryDTO queryDTO) {
        Page<ProcessDefinition> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
        IPage<ProcessDefinition> result = processDefinitionMapper.selectProcessDefinitionPage(
                page,
                queryDTO.getName(),
                queryDTO.getProcessKey(),
                queryDTO.getStatus(),
                queryDTO.getCreatedBy()
        );
        return PageResult.from(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessDefinition createProcessDefinition(CreateProcessDefinitionDTO createDTO) {
        // 检查流程Key是否已存在
        ProcessDefinition existing = processDefinitionMapper.selectByProcessKey(createDTO.getProcessKey());
        if (existing != null) {
            throw new RuntimeException("流程Key已存在: " + createDTO.getProcessKey());
        }

        // 创建流程定义主记录
        ProcessDefinition processDefinition = new ProcessDefinition();
        processDefinition.setProcessKey(createDTO.getProcessKey());
        processDefinition.setName(createDTO.getName());
        processDefinition.setDescription(createDTO.getDescription());
        processDefinition.setPublishedVersion(null); // 初始未发布
        processDefinition.setStatus(ProcessStatus.DRAFT.getCode());
        processDefinition.setCreatedBy("system"); // TODO: 从当前用户获取
        processDefinition.setCreatedTime(LocalDateTime.now());

        save(processDefinition);

        // 创建第一个版本并自动发布
        PublishProcessDTO publishDTO = new PublishProcessDTO();
        publishDTO.setProcessDefinitionId(processDefinition.getId());
        publishDTO.setDescription(createDTO.getVersionDescription());
        publishDTO.setBpmnFile(createDTO.getBpmnFile());

        // 自动发布第一个版本
        publishProcessWithNewVersion(publishDTO);

        // 重新获取更新后的流程定义
        return getById(processDefinition.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessDefinition updateProcessDefinition(Long id, String name, String description) {
        ProcessDefinition processDefinition = getById(id);
        if (processDefinition == null) {
            throw new RuntimeException("流程定义不存在");
        }

        processDefinition.setName(name);
        processDefinition.setDescription(description);
        processDefinition.setUpdatedBy("system"); // TODO: 从当前用户获取
        processDefinition.setUpdatedTime(LocalDateTime.now());
        updateById(processDefinition);

        return processDefinition;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publishProcessWithNewVersion(PublishProcessDTO publishDTO) {
        ProcessDefinition processDefinition = getById(publishDTO.getProcessDefinitionId());
        if (processDefinition == null) {
            throw new RuntimeException("流程定义不存在");
        }

        try {
            // 1. 先创建新版本
            CreateProcessVersionDTO versionDTO = new CreateProcessVersionDTO();
            versionDTO.setProcessDefinitionId(publishDTO.getProcessDefinitionId());
            versionDTO.setDescription(publishDTO.getDescription());
            versionDTO.setBpmnFile(publishDTO.getBpmnFile());

            ProcessVersion newVersion = processVersionService.createProcessVersion(versionDTO);
            log.info("创建新版本成功: processDefinitionId={}, version={}",
                    publishDTO.getProcessDefinitionId(), newVersion.getVersion());

            // 2. 读取BPMN文件内容
            String bpmnContent = processVersionService.getBpmnContent(newVersion.getId());

            // 3. 部署到Camunda
            Deployment deployment = repositoryService.createDeployment()
                    .name(processDefinition.getName() + " v" + newVersion.getVersion())
                    .addString(newVersion.getFileName(), bpmnContent)
                    .deploy();

            // 4. 获取部署的流程定义
            org.camunda.bpm.engine.repository.ProcessDefinition camundaProcessDef =
                    repositoryService.createProcessDefinitionQuery()
                            .deploymentId(deployment.getId())
                            .singleResult();

            // 5. 如果之前有发布版本，取消发布状态并挂起
            ProcessVersion currentPublished = processVersionService.getPublishedVersionByProcessDefinitionId(
                    publishDTO.getProcessDefinitionId());
            if (currentPublished != null) {
                // 挂起之前的Camunda流程定义
                if (StrUtil.isNotBlank(currentPublished.getCamundaProcessDefinitionId())) {
                    repositoryService.suspendProcessDefinitionById(currentPublished.getCamundaProcessDefinitionId());
                }

                // 取消发布状态
                processVersionMapper.updatePublishStatus(publishDTO.getProcessDefinitionId(), null, false);
                currentPublished.setIsPublished(false);
                processVersionService.updateById(currentPublished);
            }

            // 6. 更新新版本状态为已发布
            newVersion.setDeploymentId(deployment.getId());
            newVersion.setCamundaProcessDefinitionId(camundaProcessDef.getId());
            newVersion.setStatus(ProcessStatus.PUBLISHED.getCode());
            newVersion.setIsPublished(true);
            newVersion.setPublishTime(LocalDateTime.now());
            newVersion.setUpdatedBy("system"); // TODO: 从当前用户获取
            newVersion.setUpdatedTime(LocalDateTime.now());
            processVersionService.updateById(newVersion);

            // 7. 更新流程定义状态
            processDefinition.setPublishedVersion(newVersion.getVersion());
            processDefinition.setStatus(ProcessStatus.PUBLISHED.getCode());
            processDefinition.setUpdatedBy("system"); // TODO: 从当前用户获取
            processDefinition.setUpdatedTime(LocalDateTime.now());
            updateById(processDefinition);

            log.info("发布流程成功: processDefinitionId={}, version={}, deploymentId={}",
                    publishDTO.getProcessDefinitionId(), newVersion.getVersion(), deployment.getId());

        } catch (Exception e) {
            log.error("发布流程失败", e);
            throw new RuntimeException("发布流程失败: " + e.getMessage());
        }
    }

    @Override
    public ProcessDefinition getByProcessKey(String processKey) {
        return processDefinitionMapper.selectByProcessKey(processKey);
    }

}
