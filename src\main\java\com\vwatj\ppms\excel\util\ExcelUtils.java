package com.vwatj.ppms.excel.util;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * Excel工具类
 * 提供Excel操作的通用方法
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public class ExcelUtils {

    private static final String[] DATE_PATTERNS = {
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd HH:mm",
            "yyyy-MM-dd",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy/MM/dd HH:mm",
            "yyyy/MM/dd",
            "MM/dd/yyyy HH:mm:ss",
            "MM/dd/yyyy HH:mm",
            "MM/dd/yyyy",
            "dd/MM/yyyy HH:mm:ss",
            "dd/MM/yyyy HH:mm",
            "dd/MM/yyyy",
            "yyyy年MM月dd日 HH:mm:ss",
            "yyyy年MM月dd日 HH:mm",
            "yyyy年MM月dd日",
            "MM月dd日 HH:mm:ss",
            "MM月dd日 HH:mm",
            "MM月dd日",
            // Java Date.toString()格式
            "EEE MMM dd HH:mm:ss zzz yyyy",
            "EEE MMM dd HH:mm:ss yyyy"
    };

    private static final DateTimeFormatter[] DATE_FORMATTERS =
            java.util.Arrays.stream(DATE_PATTERNS)
                    .map(pattern -> {
                        try {
                            return DateTimeFormatter.ofPattern(pattern, Locale.ENGLISH);
                        } catch (Exception e) {
                            return DateTimeFormatter.ofPattern(pattern);
                        }
                    })
                    .toArray(DateTimeFormatter[]::new);

    /**
     * 设置单元格值
     */
    public static void setCellValue(Cell cell, Object value, CellStyle style) {
        if (value == null) {
            cell.setCellValue("");
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else if (value instanceof LocalDateTime) {
            cell.setCellValue(((LocalDateTime) value).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        } else if (value instanceof LocalDate) {
            cell.setCellValue(((LocalDate) value).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        } else if (value instanceof java.util.Date) {
            cell.setCellValue((java.util.Date) value);
        } else {
            cell.setCellValue(value.toString());
        }

        if (style != null) {
            cell.setCellStyle(style);
        }
    }

    /**
     * 获取单元格值
     */
    public static Object getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    // 如果是整数，返回Long，否则返回Double
                    if (numericValue == Math.floor(numericValue) && !Double.isInfinite(numericValue)) {
                        return (long) numericValue;
                    } else {
                        return numericValue;
                    }
                }
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
            default:
                return null;
        }
    }

    /**
     * 获取单元格字符串值
     */
    public static String getCellStringValue(Cell cell) {
        Object value = getCellValue(cell);
        return value != null ? value.toString().trim() : null;
    }

    /**
     * 解析表头
     */
    public static Map<String, Integer> parseHeaders(Row headerRow) {
        Map<String, Integer> headerMap = new HashMap<>();
        if (headerRow == null) {
            return headerMap;
        }

        for (Cell cell : headerRow) {
            String header = getCellStringValue(cell);
            if (header != null && !header.isEmpty()) {
                // 移除必填标识符*
                String cleanHeader = header.replace("*", "").trim();
                headerMap.put(cleanHeader, cell.getColumnIndex());
            }
        }
        return headerMap;
    }

    /**
     * 解析行数据
     */
    public static Map<String, Object> parseRowData(Row row, Map<String, Integer> headerMap) {
        Map<String, Object> rowData = new HashMap<>();
        if (row == null || headerMap == null) {
            return rowData;
        }

        for (Map.Entry<String, Integer> entry : headerMap.entrySet()) {
            String fieldName = entry.getKey();
            Integer columnIndex = entry.getValue();
            Cell cell = row.getCell(columnIndex);
            Object value = getCellValue(cell);
            rowData.put(fieldName, value);
        }
        return rowData;
    }

    /**
     * 判断行是否为空
     */
    public static boolean isEmptyRow(Row row) {
        if (row == null) {
            return true;
        }

        for (Cell cell : row) {
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                String value = getCellStringValue(cell);
                if (value != null && !value.isEmpty()) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 设置列宽
     */
    public static void setColumnWidths(Sheet sheet, int[] widths) {
        for (int i = 0; i < widths.length; i++) {
            // POI中列宽单位是1/256个字符宽度
            sheet.setColumnWidth(i, widths[i] * 256);
        }
    }

    /**
     * 冻结窗格
     */
    public static void freezePane(Sheet sheet, int colSplit, int rowSplit) {
        sheet.createFreezePane(colSplit, rowSplit);
    }

    /**
     * 合并单元格
     */
    public static void mergeCells(Sheet sheet, int firstRow, int lastRow, int firstCol, int lastCol) {
        CellRangeAddress cellRangeAddress = new CellRangeAddress(firstRow, lastRow, firstCol, lastCol);
        sheet.addMergedRegion(cellRangeAddress);
    }

    /**
     * 解析日期字符串
     */
    public static LocalDateTime parseDateTime(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }

        String trimmedDateStr = dateStr.trim();

        // 首先尝试解析数字时间戳
        try {
            long timestamp = Long.parseLong(trimmedDateStr);
            return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
        } catch (NumberFormatException ignored) {
            // 不是时间戳，继续其他格式
        }

        // 尝试使用DateTimeFormatter和SimpleDateFormat解析
        for (int i = 0; i < DATE_PATTERNS.length; i++) {
            String pattern = DATE_PATTERNS[i];

            // 对于包含时区或英文月份的格式，使用SimpleDateFormat
            if (pattern.contains("zzz") || pattern.contains("EEE")) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat(pattern, Locale.ENGLISH);
                    Date date = sdf.parse(trimmedDateStr);
                    return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                } catch (ParseException ignored) {
                    // 继续尝试下一个格式
                }
            } else {
                // 使用DateTimeFormatter
                try {
                    DateTimeFormatter formatter = DATE_FORMATTERS[i];
                    if (pattern.contains("HH:mm")) {
                        return LocalDateTime.parse(trimmedDateStr, formatter);
                    } else {
                        return LocalDate.parse(trimmedDateStr, formatter).atStartOfDay();
                    }
                } catch (DateTimeParseException ignored) {
                    // 继续尝试下一个格式
                }
            }
        }

        // 记录详细的错误信息以便调试
        System.err.println("无法解析日期格式: " + dateStr + " (类型: " + dateStr.getClass().getSimpleName() + ")");
        throw new RuntimeException("无法解析日期格式: " + dateStr);
    }

    /**
     * 解析科学计数
     */
    public static BigDecimal parseBigDecimal(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof BigDecimal) {
            return ((BigDecimal) value);
        }

        String str = value.toString().trim();
        if (str.isEmpty()) {
            return null;
        }

        try {
            return new BigDecimal(str);
        } catch (NumberFormatException e) {
            throw new RuntimeException("无法解析为整数: " + str);
        }
    }

    /**
     * 解析整数
     */
    public static Integer parseInteger(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof Number) {
            return ((Number) value).intValue();
        }

        String str = value.toString().trim();
        if (str.isEmpty()) {
            return null;
        }

        try {
            return Integer.parseInt(str);
        } catch (NumberFormatException e) {
            throw new RuntimeException("无法解析为整数: " + str);
        }
    }

    /**
     * 解析长整数
     */
    public static Long parseLong(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof Number) {
            return ((Number) value).longValue();
        }

        String str = value.toString().trim();
        if (str.isEmpty()) {
            return null;
        }

        try {
            return Long.parseLong(str);
        } catch (NumberFormatException e) {
            throw new RuntimeException("无法解析为长整数: " + str);
        }
    }

    /**
     * 解析双精度浮点数
     */
    public static Double parseDouble(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }

        String str = value.toString().trim();
        if (str.isEmpty()) {
            return null;
        }

        try {
            return Double.parseDouble(str);
        } catch (NumberFormatException e) {
            throw new RuntimeException("无法解析为浮点数: " + str);
        }
    }

    /**
     * 解析布尔值
     */
    public static Boolean parseBoolean(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof Boolean) {
            return (Boolean) value;
        }

        String str = value.toString().trim().toLowerCase();
        if (str.isEmpty()) {
            return null;
        }

        if ("true".equals(str) || "1".equals(str) || "是".equals(str) || "yes".equals(str)) {
            return true;
        } else if ("false".equals(str) || "0".equals(str) || "否".equals(str) || "no".equals(str)) {
            return false;
        } else {
            throw new RuntimeException("无法解析为布尔值: " + value);
        }
    }
}
