<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vwatj.ppms.mapper.SystemAssetMapper">

    <!-- 分页查询系统资产 -->
    <select id="selectSystemAssetPage" resultType="com.vwatj.ppms.entity.SystemAsset">
        SELECT * FROM system_asset
        <where>
            <if test="keyword != null and keyword != ''">
                AND (asset_no LIKE CONCAT('%', #{keyword}, '%') 
                OR project_name LIKE CONCAT('%', #{keyword}, '%')
                OR short_code LIKE CONCAT('%', #{keyword}, '%')
                OR ci_name LIKE CONCAT('%', #{keyword}, '%')
                OR description LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="region != null and region != ''">
                AND region = #{region}
            </if>
            <if test="businessDepartment != null and businessDepartment != ''">
                AND business_department = #{businessDepartment}
            </if>
            <if test="assetState != null and assetState != ''">
                AND asset_state = #{assetState}
            </if>
            <if test="pmOwner != null and pmOwner != ''">
                AND pm_owner = #{pmOwner}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 根据资产编号查询系统资产 -->
    <select id="selectByAssetNo" resultType="com.vwatj.ppms.entity.SystemAsset">
        SELECT * FROM system_asset WHERE asset_no = #{assetNo}
    </select>

    <!-- 根据项目名称查询系统资产 -->
    <select id="selectByProjectName" resultType="com.vwatj.ppms.entity.SystemAsset">
        SELECT * FROM system_asset WHERE project_name = #{projectName}
    </select>

    <!-- 根据短代码查询系统资产 -->
    <select id="selectByShortCode" resultType="com.vwatj.ppms.entity.SystemAsset">
        SELECT * FROM system_asset WHERE short_code = #{shortCode}
    </select>

</mapper>
