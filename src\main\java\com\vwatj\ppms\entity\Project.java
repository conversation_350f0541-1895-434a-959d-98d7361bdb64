package com.vwatj.ppms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.vwatj.ppms.common.BaseEntity;
import com.vwatj.ppms.config.TimestampToLocalDateTimeDeserializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 项目实体类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "project", autoResultMap = true)
public class Project extends BaseEntity {
    
    /**
     * 项目ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 项目名称
     */
    private String name;
    
    /**
     * 项目描述
     */
    private String description;
    
    /**
     * 项目封面
     */
    private String cover;
    
    /**
     * 项目版本
     */
    private String version;
    
    /**
     * 项目经理
     */
    private String manager;
    

    
    /**
     * 项目优先级 (low, medium, high, urgent)
     */
    private String priority;
    
    /**
     * 项目状态 (requirement_collect, bbp, tab, development, inner_test, uat, cab, release, planning, week_report, security_check)
     */
    private String status;
    
    /**
     * 关联系统资产ID
     */
    private Long relatedAssetId;

    /**
     * 资产编号
     */
    private String assetNo;

    /**
     * 资产名称
     */
    private String assetName;

    /**
     * 系统资产版本号
     */
    private String assetVersion;
    
    /**
     * 开始时间
     */
    @JsonDeserialize(using = TimestampToLocalDateTimeDeserializer.class)
    private LocalDateTime startDate;

    /**
     * 结束时间
     */
    @JsonDeserialize(using = TimestampToLocalDateTimeDeserializer.class)
    private LocalDateTime endDate;
    
    /**
     * 团队成员ID列表 (JSON格式存储)
     */
    @com.baomidou.mybatisplus.annotation.TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> teamMembers;
    
    /**
     * 标签列表 (JSON格式存储)
     */
    @com.baomidou.mybatisplus.annotation.TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> tags;
    
    /**
     * 项目分类
     */
    private String category;
    
    /**
     * 是否关注
     */
    private Boolean starred;
    
    /**
     * 是否归档
     */
    private Boolean archived;
    
    /**
     * 总任务数
     */
    private Integer totalTasks;
    
    /**
     * 已完成任务数
     */
    private Integer completedTasks;
    
    /**
     * 总文档数
     */
    private Integer totalDocuments;
    
    /**
     * 扩展字段 (JSON格式存储)
     */
    @com.baomidou.mybatisplus.annotation.TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> customFields;
}
