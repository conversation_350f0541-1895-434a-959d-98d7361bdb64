package com.vwatj.ppms.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateProcessVersionDTO;
import com.vwatj.ppms.dto.ProcessVersionQueryDTO;
import com.vwatj.ppms.entity.ProcessDefinition;
import com.vwatj.ppms.entity.ProcessVersion;
import com.vwatj.ppms.enums.ProcessStatus;
import com.vwatj.ppms.mapper.ProcessVersionMapper;
import com.vwatj.ppms.mapper.ProcessDefinitionMapper;
import com.vwatj.ppms.service.ProcessVersionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;

/**
 * 流程版本服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProcessVersionServiceImpl extends ServiceImpl<ProcessVersionMapper, ProcessVersion>
        implements ProcessVersionService {

    private final ProcessVersionMapper processVersionMapper;
    private final ProcessDefinitionMapper processDefinitionMapper;

    @Value("${ppms.upload.path}")
    private String uploadPath;

    @Override
    public PageResult<ProcessVersion> getProcessVersionPage(ProcessVersionQueryDTO queryDTO) {
        Page<ProcessVersion> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
        IPage<ProcessVersion> result = processVersionMapper.selectProcessVersionPage(
                page,
                queryDTO.getProcessDefinitionId(),
                queryDTO.getVersion(),
                queryDTO.getStatus(),
                queryDTO.getCreatedBy()
        );
        return PageResult.from(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessVersion createProcessVersion(CreateProcessVersionDTO createDTO) {
        // 验证流程定义是否存在 - 直接查询数据库避免循环依赖
        ProcessDefinition processDefinition = processDefinitionMapper.selectById(createDTO.getProcessDefinitionId());
        if (processDefinition == null) {
            throw new RuntimeException("流程定义不存在");
        }

        // 验证BPMN文件
        MultipartFile bpmnFile = createDTO.getBpmnFile();
        validateBpmnFile(bpmnFile);

        try {
            // 获取下一个版本号
            Integer maxVersion = processVersionMapper.selectMaxVersionByProcessDefinitionId(createDTO.getProcessDefinitionId());
            Integer nextVersion = (maxVersion == null) ? 1 : maxVersion + 1;

            // 保存BPMN文件
            String fileName = bpmnFile.getOriginalFilename();
            String filePath = saveBpmnFile(bpmnFile, processDefinition.getProcessKey(), nextVersion);

            // 创建版本记录
            ProcessVersion processVersion = new ProcessVersion();
            processVersion.setProcessDefinitionId(createDTO.getProcessDefinitionId());
            processVersion.setVersion(nextVersion);
            processVersion.setDescription(createDTO.getDescription());
            processVersion.setFileName(fileName);
            processVersion.setFilePath(filePath);
            processVersion.setFileSize(bpmnFile.getSize());
            processVersion.setStatus(ProcessStatus.DRAFT.getCode());
            processVersion.setIsPublished(false);
            processVersion.setCreatedBy("system"); // TODO: 从当前用户获取
            processVersion.setCreatedTime(LocalDateTime.now());

            save(processVersion);
            return processVersion;

        } catch (IOException e) {
            log.error("创建流程版本失败", e);
            throw new RuntimeException("创建流程版本失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProcessVersion(Long id) {
        ProcessVersion processVersion = getById(id);
        if (processVersion == null) {
            throw new RuntimeException("流程版本不存在");
        }

        // 检查是否为已发布版本
        if (Boolean.TRUE.equals(processVersion.getIsPublished())) {
            throw new RuntimeException("已发布的版本不能删除");
        }

        removeById(id);

        // 删除BPMN文件
        try {
            FileUtil.del(processVersion.getFilePath());
        } catch (Exception e) {
            log.warn("删除BPMN文件失败: {}", e.getMessage());
        }
    }

    @Override
    public ProcessVersion getByProcessDefinitionIdAndVersion(Long processDefinitionId, Integer version) {
        return processVersionMapper.selectByProcessDefinitionIdAndVersion(processDefinitionId, version);
    }

    @Override
    public ProcessVersion getPublishedVersionByProcessDefinitionId(Long processDefinitionId) {
        return processVersionMapper.selectPublishedVersionByProcessDefinitionId(processDefinitionId);
    }

    @Override
    public String getBpmnContent(Long id) {
        ProcessVersion processVersion = getById(id);
        if (processVersion == null) {
            throw new RuntimeException("流程版本不存在");
        }

        try {
            return FileUtil.readUtf8String(processVersion.getFilePath());
        } catch (Exception e) {
            log.error("读取BPMN文件内容失败", e);
            throw new RuntimeException("读取BPMN文件内容失败: " + e.getMessage());
        }
    }

    @Override
    public byte[] downloadBpmnFile(Long id) {
        ProcessVersion processVersion = getById(id);
        if (processVersion == null) {
            throw new RuntimeException("流程版本不存在");
        }

        try {
            return FileUtil.readBytes(processVersion.getFilePath());
        } catch (Exception e) {
            log.error("下载BPMN文件失败", e);
            throw new RuntimeException("下载BPMN文件失败: " + e.getMessage());
        }
    }

    /**
     * 验证BPMN文件
     */
    private void validateBpmnFile(MultipartFile file) {
        log.info("开始验证BPMN文件: {}", file != null ? file.getOriginalFilename() : "null");

        if (file == null) {
            log.error("BPMN文件对象为null");
            throw new RuntimeException("BPMN文件不能为空");
        }

        if (file.isEmpty()) {
            log.error("BPMN文件内容为空, 文件名: {}, 大小: {}", file.getOriginalFilename(), file.getSize());
            throw new RuntimeException("BPMN文件不能为空");
        }

        String fileName = file.getOriginalFilename();
        log.info("BPMN文件名: {}, 大小: {} bytes", fileName, file.getSize());

        if (StrUtil.isBlank(fileName) || !fileName.toLowerCase().endsWith(".bpmn")) {
            log.error("文件格式不正确: {}", fileName);
            throw new RuntimeException("文件必须是.bpmn格式");
        }

        // 检查文件大小（最大10MB）
        if (file.getSize() > 10 * 1024 * 1024) {
            log.error("文件大小超限: {} bytes", file.getSize());
            throw new RuntimeException("BPMN文件大小不能超过10MB");
        }

        log.info("BPMN文件验证通过");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activateVersion(Long id) {
        ProcessVersion processVersion = getById(id);
        if (processVersion == null) {
            throw new RuntimeException("流程版本不存在");
        }

        // 检查是否已发布
        if (!Boolean.TRUE.equals(processVersion.getIsPublished())) {
            throw new RuntimeException("只有已发布的版本才能激活");
        }

        // 检查是否已经是激活状态
        if (ProcessStatus.PUBLISHED.getCode().equals(processVersion.getStatus())) {
            throw new RuntimeException("版本已经是激活状态");
        }

        // 激活Camunda流程定义
        if (StrUtil.isNotBlank(processVersion.getCamundaProcessDefinitionId())) {
            try {
                // 这里需要注入RepositoryService，暂时先记录日志
                log.info("激活Camunda流程定义: {}", processVersion.getCamundaProcessDefinitionId());
                // repositoryService.activateProcessDefinitionById(processVersion.getCamundaProcessDefinitionId());
            } catch (Exception e) {
                log.error("激活Camunda流程定义失败", e);
                throw new RuntimeException("激活Camunda流程定义失败: " + e.getMessage());
            }
        }

        // 更新版本状态
        processVersion.setStatus(ProcessStatus.PUBLISHED.getCode());
        processVersion.setUpdatedBy("system"); // TODO: 从当前用户获取
        processVersion.setUpdatedTime(LocalDateTime.now());
        updateById(processVersion);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void suspendVersion(Long id) {
        ProcessVersion processVersion = getById(id);
        if (processVersion == null) {
            throw new RuntimeException("流程版本不存在");
        }

        // 检查是否已发布
        if (!Boolean.TRUE.equals(processVersion.getIsPublished())) {
            throw new RuntimeException("只有已发布的版本才能挂起");
        }

        // 检查是否已经是挂起状态
        if (ProcessStatus.SUSPENDED.getCode().equals(processVersion.getStatus())) {
            throw new RuntimeException("版本已经是挂起状态");
        }

        // TODO: 检查是否存在运行中的任务
        // 这里需要查询Camunda的运行时任务
        if (StrUtil.isNotBlank(processVersion.getCamundaProcessDefinitionId())) {
            // 检查是否有运行中的流程实例
            log.info("检查流程定义是否有运行中的实例: {}", processVersion.getCamundaProcessDefinitionId());
            // 如果有运行中的实例，抛出异常
            // long runningInstances = runtimeService.createProcessInstanceQuery()
            //     .processDefinitionId(processVersion.getCamundaProcessDefinitionId())
            //     .count();
            // if (runningInstances > 0) {
            //     throw new RuntimeException("存在运行中的流程实例，无法挂起版本");
            // }
        }

        // 挂起Camunda流程定义
        if (StrUtil.isNotBlank(processVersion.getCamundaProcessDefinitionId())) {
            try {
                log.info("挂起Camunda流程定义: {}", processVersion.getCamundaProcessDefinitionId());
                // repositoryService.suspendProcessDefinitionById(processVersion.getCamundaProcessDefinitionId());
            } catch (Exception e) {
                log.error("挂起Camunda流程定义失败", e);
                throw new RuntimeException("挂起Camunda流程定义失败: " + e.getMessage());
            }
        }

        // 更新版本状态
        processVersion.setStatus(ProcessStatus.SUSPENDED.getCode());
        processVersion.setUpdatedBy("system"); // TODO: 从当前用户获取
        processVersion.setUpdatedTime(LocalDateTime.now());
        updateById(processVersion);
    }

    /**
     * 保存BPMN文件
     */
    private String saveBpmnFile(MultipartFile file, String processKey, Integer version) throws IOException {
        // 创建目录
        String processDir = uploadPath + "/bpmn/" + processKey;
        FileUtil.mkdir(processDir);

        // 生成文件名
        String fileName = processKey + "_v" + version + ".bpmn";
        String filePath = processDir + "/" + fileName;

        // 保存文件
        Path path = Paths.get(filePath);
        Files.write(path, file.getBytes());

        return filePath;
    }
}
