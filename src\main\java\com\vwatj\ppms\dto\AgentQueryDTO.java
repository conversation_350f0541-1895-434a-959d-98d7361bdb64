package com.vwatj.ppms.dto;

import lombok.Data;

/**
 * Agent查询DTO
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
public class AgentQueryDTO {
    
    /**
     * 页码
     */
    private Long page = 1L;
    
    /**
     * 每页大小
     */
    private Long pageSize = 10L;
    
    /**
     * 排序字段
     */
    private String sortBy;
    
    /**
     * 排序方向
     */
    private String sortOrder;
    
    /**
     * 关键词
     */
    private String keyword;
    
    /**
     * Agent状态
     */
    private Integer status;
    
    /**
     * Agent类型
     */
    private String type;
    
    /**
     * 创建者ID
     */
    private Long creatorId;
}
