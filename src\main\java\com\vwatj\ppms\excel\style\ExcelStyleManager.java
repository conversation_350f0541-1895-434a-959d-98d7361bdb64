package com.vwatj.ppms.excel.style;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * Excel样式管理器
 * 提供统一的Excel样式配置
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public class ExcelStyleManager {

    private final Workbook workbook;
    private CellStyle headerStyle;
    private CellStyle requiredHeaderStyle;
    private CellStyle dataStyle;
    private CellStyle dateStyle;
    private CellStyle numberStyle;
    private CellStyle centerStyle;

    public ExcelStyleManager(Workbook workbook) {
        this.workbook = workbook;
        initStyles();
    }

    /**
     * 初始化所有样式
     */
    private void initStyles() {
        createHeaderStyle();
        createRequiredHeaderStyle();
        createDataStyle();
        createDateStyle();
        createNumberStyle();
        createCenterStyle();
    }

    /**
     * 创建表头样式
     */
    private void createHeaderStyle() {
        headerStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontName("新宋体");
        font.setFontHeightInPoints((short) 11);
        font.setColor(IndexedColors.BLACK.getIndex());
        
        headerStyle.setFont(font);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        setBorders(headerStyle);
        headerStyle.setWrapText(true);
    }

    /**
     * 创建必填表头样式（红色字体）
     */
    private void createRequiredHeaderStyle() {
        requiredHeaderStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontName("新宋体");
        font.setFontHeightInPoints((short) 11);
        font.setColor(IndexedColors.RED.getIndex());
        
        requiredHeaderStyle.setFont(font);
        requiredHeaderStyle.setAlignment(HorizontalAlignment.CENTER);
        requiredHeaderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        requiredHeaderStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        requiredHeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        setBorders(requiredHeaderStyle);
        requiredHeaderStyle.setWrapText(true);
    }

    /**
     * 创建数据行样式
     */
    private void createDataStyle() {
        dataStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName("新宋体");
        font.setFontHeightInPoints((short) 10);
        font.setColor(IndexedColors.BLACK.getIndex());
        
        dataStyle.setFont(font);
        dataStyle.setAlignment(HorizontalAlignment.LEFT);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        setBorders(dataStyle);
        dataStyle.setWrapText(true);
    }

    /**
     * 创建日期样式
     */
    private void createDateStyle() {
        dateStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName("新宋体");
        font.setFontHeightInPoints((short) 10);
        
        dateStyle.setFont(font);
        dateStyle.setAlignment(HorizontalAlignment.CENTER);
        dateStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dateStyle.setDataFormat(workbook.getCreationHelper().createDataFormat().getFormat("yyyy-mm-dd hh:mm:ss"));
        setBorders(dateStyle);
    }

    /**
     * 创建数字样式
     */
    private void createNumberStyle() {
        numberStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName("新宋体");
        font.setFontHeightInPoints((short) 10);
        
        numberStyle.setFont(font);
        numberStyle.setAlignment(HorizontalAlignment.RIGHT);
        numberStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        setBorders(numberStyle);
    }

    /**
     * 创建居中样式
     */
    private void createCenterStyle() {
        centerStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName("新宋体");
        font.setFontHeightInPoints((short) 10);
        
        centerStyle.setFont(font);
        centerStyle.setAlignment(HorizontalAlignment.CENTER);
        centerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        setBorders(centerStyle);
    }

    /**
     * 设置边框
     */
    private void setBorders(CellStyle style) {
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
    }

    // Getter方法
    public CellStyle getHeaderStyle() {
        return headerStyle;
    }

    public CellStyle getRequiredHeaderStyle() {
        return requiredHeaderStyle;
    }

    public CellStyle getDataStyle() {
        return dataStyle;
    }

    public CellStyle getDateStyle() {
        return dateStyle;
    }

    public CellStyle getNumberStyle() {
        return numberStyle;
    }

    public CellStyle getCenterStyle() {
        return centerStyle;
    }

    /**
     * 根据数据类型获取合适的样式
     */
    public CellStyle getStyleByDataType(Object value) {
        if (value == null) {
            return dataStyle;
        }
        
        if (value instanceof Number) {
            return numberStyle;
        } else if (value instanceof java.util.Date || 
                   value instanceof java.time.LocalDate || 
                   value instanceof java.time.LocalDateTime) {
            return dateStyle;
        } else {
            return dataStyle;
        }
    }

    /**
     * 获取表头样式（根据是否必填）
     */
    public CellStyle getHeaderStyle(boolean required) {
        return required ? requiredHeaderStyle : headerStyle;
    }
}
