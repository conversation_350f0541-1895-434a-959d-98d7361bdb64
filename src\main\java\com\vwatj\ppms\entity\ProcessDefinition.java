package com.vwatj.ppms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 流程定义实体（流程主表）
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("process_definition")
public class ProcessDefinition {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 流程定义Key（唯一标识）
     */
    private String processKey;

    /**
     * 流程名称
     */
    private String name;

    /**
     * 流程描述
     */
    private String description;

    /**
     * 当前发布的版本号
     */
    private Integer publishedVersion;

    /**
     * 流程状态：DRAFT(草稿), PUBLISHED(已发布), SUSPENDED(已挂起)
     */
    private String status;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
