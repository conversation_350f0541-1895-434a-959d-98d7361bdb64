# PPMS AI Agent 部署指南

## 快速开始

### 1. 获取 DeepSeek API Key

1. 访问 [DeepSeek 官网](https://platform.deepseek.com/)
2. 注册账号并登录
3. 在控制台中创建 API Key
4. 复制 API Key 备用

### 2. 配置环境变量

```bash
# Linux/Mac
export DEEPSEEK_API_KEY=your-deepseek-api-key-here

# Windows
set DEEPSEEK_API_KEY=your-deepseek-api-key-here
```

### 3. 启动应用

```bash
# 编译项目
mvn clean compile

# 运行应用
mvn spring-boot:run
```

### 4. 访问AI助手

- API接口: http://localhost:8080/ai/
- 演示页面: http://localhost:8080/ai-chat-demo.html
- 健康检查: http://localhost:8080/ai/health

## 配置说明

### application.yml 配置

```yaml
spring:
  ai:
    openai:
      api-key: ${DEEPSEEK_API_KEY}
      base-url: https://api.deepseek.com
      chat:
        options:
          model: deepseek-chat
          temperature: 0.7
          max-tokens: 4000
```

### 可配置参数

| 参数 | 说明 | 默认值 | 建议值 |
|------|------|--------|--------|
| model | 模型名称 | deepseek-chat | deepseek-chat |
| temperature | 创造性参数 | 0.7 | 0.3-0.9 |
| max-tokens | 最大输出长度 | 4000 | 2000-8000 |
| base-url | API基础URL | https://api.deepseek.com | 不建议修改 |

## 功能测试

### 1. 基础聊天测试

```bash
curl -X POST http://localhost:8080/ai/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "你好，请介绍一下你的功能",
    "sessionId": "test-session",
    "userId": "test-user"
  }'
```

### 2. 项目查询测试

```bash
curl -X POST http://localhost:8080/ai/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "查看所有项目信息",
    "sessionId": "test-session",
    "userId": "test-user",
    "enableFunctionCalling": true
  }'
```

### 3. 项目分析测试

```bash
curl -X POST http://localhost:8080/ai/analyze/project/1
```

### 4. 快速问答测试

```bash
curl -X POST http://localhost:8080/ai/quick-ask \
  -H "Content-Type: application/json" \
  -d '{
    "question": "项目1的进度如何？",
    "projectId": 1
  }'
```

## 生产环境部署

### 1. Docker 部署

创建 Dockerfile:

```dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app

COPY target/ppms-*.jar app.jar

EXPOSE 8080

ENV DEEPSEEK_API_KEY=""

CMD ["java", "-jar", "app.jar"]
```

构建和运行:

```bash
# 构建镜像
docker build -t ppms-ai .

# 运行容器
docker run -d \
  -p 8080:8080 \
  -e DEEPSEEK_API_KEY=your-api-key \
  --name ppms-ai \
  ppms-ai
```

### 2. Kubernetes 部署

创建 deployment.yaml:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ppms-ai
spec:
  replicas: 2
  selector:
    matchLabels:
      app: ppms-ai
  template:
    metadata:
      labels:
        app: ppms-ai
    spec:
      containers:
      - name: ppms-ai
        image: ppms-ai:latest
        ports:
        - containerPort: 8080
        env:
        - name: DEEPSEEK_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-secrets
              key: deepseek-api-key
---
apiVersion: v1
kind: Service
metadata:
  name: ppms-ai-service
spec:
  selector:
    app: ppms-ai
  ports:
  - port: 80
    targetPort: 8080
  type: LoadBalancer
```

### 3. 环境变量管理

生产环境建议使用密钥管理服务:

```bash
# 创建 Kubernetes Secret
kubectl create secret generic ai-secrets \
  --from-literal=deepseek-api-key=your-api-key

# 或使用配置文件
kubectl apply -f - <<EOF
apiVersion: v1
kind: Secret
metadata:
  name: ai-secrets
type: Opaque
data:
  deepseek-api-key: $(echo -n 'your-api-key' | base64)
EOF
```

## 监控和日志

### 1. 应用监控

添加监控配置:

```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
```

### 2. 日志配置

```yaml
logging:
  level:
    com.vwatj.ppms.ai: INFO
    org.springframework.ai: WARN
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/ppms-ai.log
```

### 3. 性能指标

监控以下关键指标:

- AI响应时间
- API调用成功率
- 并发用户数
- 内存和CPU使用率
- 错误率

## 故障排除

### 1. 常见问题

**问题**: AI服务无响应
**解决**: 检查API Key配置，验证网络连接

**问题**: 响应速度慢
**解决**: 调整max-tokens参数，检查网络延迟

**问题**: 功能调用失败
**解决**: 检查业务服务状态，验证数据库连接

### 2. 日志分析

```bash
# 查看AI相关日志
grep "AI" logs/ppms-ai.log

# 查看错误日志
grep "ERROR" logs/ppms-ai.log

# 实时监控日志
tail -f logs/ppms-ai.log
```

### 3. 健康检查

```bash
# 检查服务状态
curl http://localhost:8080/ai/health

# 检查详细状态
curl http://localhost:8080/actuator/health
```

## 安全配置

### 1. API访问控制

```java
@Configuration
@EnableWebSecurity
public class AISecurityConfig {
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.authorizeHttpRequests(authz -> authz
            .requestMatchers("/ai/**").authenticated()
            .anyRequest().permitAll()
        );
        return http.build();
    }
}
```

### 2. 速率限制

```java
@Component
public class RateLimitingFilter implements Filter {
    
    private final RateLimiter rateLimiter = RateLimiter.create(10.0); // 每秒10个请求
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) {
        if (rateLimiter.tryAcquire()) {
            chain.doFilter(request, response);
        } else {
            // 返回429状态码
        }
    }
}
```

### 3. 输入验证

```java
@Component
public class InputValidator {
    
    public void validateChatRequest(ChatRequest request) {
        if (request.getMessage().length() > 1000) {
            throw new IllegalArgumentException("消息长度不能超过1000字符");
        }
        // 其他验证逻辑
    }
}
```

## 扩展开发

### 1. 自定义功能函数

```java
@Component
public class CustomFunctions {
    
    public Function<CustomRequest, String> customAnalysis() {
        return request -> {
            // 实现自定义分析逻辑
            return "分析结果";
        };
    }
}
```

### 2. 集成外部服务

```java
@Service
public class ExternalServiceIntegration {
    
    @Autowired
    private RestTemplate restTemplate;
    
    public String callExternalAPI(String data) {
        // 调用外部API
        return restTemplate.postForObject("/external/api", data, String.class);
    }
}
```

### 3. 自定义提示词模板

```java
@Component
public class PromptTemplates {
    
    public String buildAnalysisPrompt(ProjectData data) {
        return String.format("""
            请分析以下项目数据：
            项目名称: %s
            进度: %d%%
            风险等级: %s
            
            请提供详细的分析报告。
            """, data.getName(), data.getProgress(), data.getRiskLevel());
    }
}
```

## 版本更新

### 1. 更新流程

1. 备份当前配置
2. 停止服务
3. 更新代码
4. 重新编译
5. 启动服务
6. 验证功能

### 2. 回滚策略

```bash
# 保存当前版本
docker tag ppms-ai:latest ppms-ai:backup

# 回滚到之前版本
docker run -d ppms-ai:backup
```

### 3. 数据迁移

如果涉及数据结构变更，需要执行相应的迁移脚本。
