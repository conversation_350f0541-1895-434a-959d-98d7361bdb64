<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1fehqw9" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.37.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.23.0">
  <bpmn:process id="project_new_flow" name="project_new_flow" isExecutable="true" camunda:historyTimeToLive="0">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_1rlmc1k</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:intermediateThrowEvent id="Event_0lkqwtz" name="Requirement Collect" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{projectBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>requirement_collect</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1rlmc1k</bpmn:incoming>
      <bpmn:outgoing>Flow_1ah14dz</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1rlmc1k" sourceRef="StartEvent_1" targetRef="Event_0lkqwtz" />
    <bpmn:intermediateThrowEvent id="Event_1pmtv44" name="BBP" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{projectBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>bbp</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0kxxfou</bpmn:incoming>
      <bpmn:incoming>Flow_0f2jp5p</bpmn:incoming>
      <bpmn:outgoing>Flow_0g6ie43</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateThrowEvent id="Event_17ywtoh" name="TAB" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{projectBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>tab</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1i3sc4w</bpmn:incoming>
      <bpmn:outgoing>Flow_0hpm3iu</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateThrowEvent id="Event_1yqwfcq" name="Development" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{projectBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>development</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1hgycsv</bpmn:incoming>
      <bpmn:incoming>Flow_0pdu8cf</bpmn:incoming>
      <bpmn:outgoing>Flow_1oev6mh</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateThrowEvent id="Event_1o5r78e" name="Inner Test" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{projectBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>inner_test</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0deuik0</bpmn:incoming>
      <bpmn:incoming>Flow_10uaqhq</bpmn:incoming>
      <bpmn:outgoing>Flow_0bql9dy</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateThrowEvent id="Event_0n2enpu" name="UAT" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{projectBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>uat</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0dxhsyh</bpmn:incoming>
      <bpmn:incoming>Flow_1rcw46x</bpmn:incoming>
      <bpmn:outgoing>Flow_1um3byl</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateThrowEvent id="Event_0kf6700" name="CAB" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{projectBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>cab</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0q0lr5p</bpmn:incoming>
      <bpmn:incoming>Flow_12kmoio</bpmn:incoming>
      <bpmn:outgoing>Flow_1yh1m2z</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateThrowEvent id="Event_12sxg21" name="Release" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{projectBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>release</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_147xbe2</bpmn:incoming>
      <bpmn:incoming>Flow_1cyj6er</bpmn:incoming>
      <bpmn:outgoing>Flow_0tkwwx7</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1ah14dz" sourceRef="Event_0lkqwtz" targetRef="Activity_1a06mfp" />
    <bpmn:sequenceFlow id="Flow_0g6ie43" sourceRef="Event_1pmtv44" targetRef="Activity_01of7rq" />
    <bpmn:sequenceFlow id="Flow_0hpm3iu" sourceRef="Event_17ywtoh" targetRef="Activity_19kid1z" />
    <bpmn:endEvent id="Event_0squ366">
      <bpmn:incoming>Flow_02uq3b4</bpmn:incoming>
      <bpmn:incoming>Flow_171v1bj</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0tkwwx7" sourceRef="Event_12sxg21" targetRef="Activity_0ghpwek" />
    <bpmn:userTask id="Activity_1a06mfp" name="Requirement Submit" camunda:formKey="requirement_submit_form">
      <bpmn:incoming>Flow_1ah14dz</bpmn:incoming>
      <bpmn:outgoing>Flow_0f2jp5p</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_01of7rq" name="BBP File Submit" camunda:formKey="bbp_submit_form">
      <bpmn:incoming>Flow_0g6ie43</bpmn:incoming>
      <bpmn:outgoing>Flow_01vcnay</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_089la7e">
      <bpmn:incoming>Flow_1wfx2ts</bpmn:incoming>
      <bpmn:outgoing>Flow_0kxxfou</bpmn:outgoing>
      <bpmn:outgoing>Flow_1i3sc4w</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0kxxfou" name="Reject" sourceRef="Gateway_089la7e" targetRef="Event_1pmtv44">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "reject"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1i3sc4w" name="Approve" sourceRef="Gateway_089la7e" targetRef="Event_17ywtoh">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "approve"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_19kid1z" name="TAB Form Submit" camunda:formKey="tab_submit_form">
      <bpmn:incoming>Flow_0hpm3iu</bpmn:incoming>
      <bpmn:outgoing>Flow_0vltohr</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1oev6mh" sourceRef="Event_1yqwfcq" targetRef="Activity_09oz8ns" />
    <bpmn:exclusiveGateway id="Gateway_0tfc0or">
      <bpmn:incoming>Flow_0n2mwbu</bpmn:incoming>
      <bpmn:outgoing>Flow_0deuik0</bpmn:outgoing>
      <bpmn:outgoing>Flow_1hgycsv</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0nnsz76" sourceRef="Activity_09oz8ns" targetRef="Activity_0phi5jq" />
    <bpmn:sequenceFlow id="Flow_0deuik0" name="Approve" sourceRef="Gateway_0tfc0or" targetRef="Event_1o5r78e">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "approve"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0bql9dy" sourceRef="Event_1o5r78e" targetRef="Activity_0w8vsni" />
    <bpmn:exclusiveGateway id="Gateway_0isscbt">
      <bpmn:incoming>Flow_1wpz0yy</bpmn:incoming>
      <bpmn:outgoing>Flow_1rcw46x</bpmn:outgoing>
      <bpmn:outgoing>Flow_10uaqhq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0rt4sqj" sourceRef="Activity_0w8vsni" targetRef="Activity_02ipeqq" />
    <bpmn:sequenceFlow id="Flow_1um3byl" sourceRef="Event_0n2enpu" targetRef="Activity_195qa37" />
    <bpmn:exclusiveGateway id="Gateway_1ezce66">
      <bpmn:incoming>Flow_05988sw</bpmn:incoming>
      <bpmn:outgoing>Flow_0q0lr5p</bpmn:outgoing>
      <bpmn:outgoing>Flow_0dxhsyh</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0q0lr5p" name="Approve" sourceRef="Gateway_1ezce66" targetRef="Event_0kf6700">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "approve"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0dxhsyh" name="Reject" sourceRef="Gateway_1ezce66" targetRef="Event_0n2enpu">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "reject"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1yh1m2z" sourceRef="Event_0kf6700" targetRef="Activity_0gwmn4p" />
    <bpmn:exclusiveGateway id="Gateway_0g8xup7">
      <bpmn:incoming>Flow_1qpm6rr</bpmn:incoming>
      <bpmn:outgoing>Flow_12kmoio</bpmn:outgoing>
      <bpmn:outgoing>Flow_1cyj6er</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1yaxx4u" sourceRef="Activity_0gwmn4p" targetRef="Activity_1a9pvom" />
    <bpmn:sequenceFlow id="Flow_12kmoio" name="Reject" sourceRef="Gateway_0g8xup7" targetRef="Event_0kf6700">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "reject"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_09oz8ns" name="Development Completed Submit" camunda:formKey="development_submit_form">
      <bpmn:incoming>Flow_1oev6mh</bpmn:incoming>
      <bpmn:outgoing>Flow_0nnsz76</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1hgycsv" name="Reject" sourceRef="Gateway_0tfc0or" targetRef="Event_1yqwfcq">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "reject"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_0w8vsni" name="Test Completed Submit" camunda:formKey="test_submit_form">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0bql9dy</bpmn:incoming>
      <bpmn:outgoing>Flow_0rt4sqj</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_1jgyblk">
      <bpmn:incoming>Flow_11rp3os</bpmn:incoming>
      <bpmn:outgoing>Flow_18pc5rr</bpmn:outgoing>
      <bpmn:outgoing>Flow_147xbe2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0bkkdmf" sourceRef="Activity_0ghpwek" targetRef="Activity_006ndy8" />
    <bpmn:sequenceFlow id="Flow_18pc5rr" name="Approve" sourceRef="Gateway_1jgyblk" targetRef="Activity_0c28pj1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "approve"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_147xbe2" name="Reject" sourceRef="Gateway_1jgyblk" targetRef="Event_12sxg21">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "reject"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_195qa37" name="UAT Start Submit" camunda:formKey="uat_submit_form">
      <bpmn:incoming>Flow_1um3byl</bpmn:incoming>
      <bpmn:outgoing>Flow_0c022hv</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_0gwmn4p" name="CAB Submit" camunda:formKey="cab_submit_form">
      <bpmn:incoming>Flow_1yh1m2z</bpmn:incoming>
      <bpmn:outgoing>Flow_1yaxx4u</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_0ghpwek" name="Go Live Compeleted Submit" camunda:formKey="golive_submit_form">
      <bpmn:incoming>Flow_0tkwwx7</bpmn:incoming>
      <bpmn:outgoing>Flow_0bkkdmf</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0f2jp5p" name="Submit" sourceRef="Activity_1a06mfp" targetRef="Event_1pmtv44" />
    <bpmn:sequenceFlow id="Flow_01vcnay" sourceRef="Activity_01of7rq" targetRef="Activity_0ubxas7" />
    <bpmn:sequenceFlow id="Flow_1wfx2ts" sourceRef="Activity_0ubxas7" targetRef="Gateway_089la7e" />
    <bpmn:userTask id="Activity_0ubxas7" name="Member Review Submit" camunda:formKey="bbp_review_submit_form">
      <bpmn:incoming>Flow_01vcnay</bpmn:incoming>
      <bpmn:outgoing>Flow_1wfx2ts</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_02l6iwt">
      <bpmn:incoming>Flow_1onda3u</bpmn:incoming>
      <bpmn:outgoing>Flow_0d6xxbu</bpmn:outgoing>
      <bpmn:outgoing>Flow_0pdu8cf</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0d6xxbu" name="Cancel" sourceRef="Gateway_02l6iwt" targetRef="Event_0moi766">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "cancel"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0pdu8cf" name="Approve" sourceRef="Gateway_02l6iwt" targetRef="Event_1yqwfcq">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "approve"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0vltohr" sourceRef="Activity_19kid1z" targetRef="Activity_0nccfn0" />
    <bpmn:intermediateThrowEvent id="Event_1785mmb" name="Go Live" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{projectBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>go_live</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0q41fps</bpmn:incoming>
      <bpmn:outgoing>Flow_02uq3b4</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_02uq3b4" sourceRef="Event_1785mmb" targetRef="Event_0squ366" />
    <bpmn:intermediateThrowEvent id="Event_0moi766" name="Cancel" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{projectBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>cancel</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0d6xxbu</bpmn:incoming>
      <bpmn:outgoing>Flow_171v1bj</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_171v1bj" sourceRef="Event_0moi766" targetRef="Event_0squ366" />
    <bpmn:userTask id="Activity_0nccfn0" name="TAB Result Submit" camunda:formKey="tab_result_submit_form">
      <bpmn:incoming>Flow_0vltohr</bpmn:incoming>
      <bpmn:outgoing>Flow_1onda3u</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1onda3u" sourceRef="Activity_0nccfn0" targetRef="Gateway_02l6iwt" />
    <bpmn:serviceTask id="Activity_02ipeqq" name="Check Test Ticket List" camunda:delegateExpression="projectCheckTestResultServiceTaskDelegate">
      <bpmn:incoming>Flow_0rt4sqj</bpmn:incoming>
      <bpmn:outgoing>Flow_1wpz0yy</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1wpz0yy" sourceRef="Activity_02ipeqq" targetRef="Gateway_0isscbt" />
    <bpmn:sequenceFlow id="Flow_05988sw" sourceRef="Activity_1kc1xjo" targetRef="Gateway_1ezce66" />
    <bpmn:userTask id="Activity_1kc1xjo" name="UAT Result Submit" camunda:formKey="uat_result_submit_form">
      <bpmn:incoming>Flow_0invim5</bpmn:incoming>
      <bpmn:outgoing>Flow_05988sw</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0c022hv" sourceRef="Activity_195qa37" targetRef="Activity_1olbs5u" />
    <bpmn:sequenceFlow id="Flow_0invim5" sourceRef="Activity_1olbs5u" targetRef="Activity_1kc1xjo" />
    <bpmn:sequenceFlow id="Flow_1rcw46x" name="Approve" sourceRef="Gateway_0isscbt" targetRef="Event_0n2enpu">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "approve"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1qpm6rr" sourceRef="Activity_1u0ulwy" targetRef="Gateway_0g8xup7" />
    <bpmn:sequenceFlow id="Flow_1cyj6er" name="Approve" sourceRef="Gateway_0g8xup7" targetRef="Event_12sxg21">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "approve"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1285wrk" sourceRef="Activity_1a9pvom" targetRef="Activity_1u0ulwy" />
    <bpmn:sequenceFlow id="Flow_10uaqhq" name="Reject" sourceRef="Gateway_0isscbt" targetRef="Event_1o5r78e">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "reject"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0q41fps" sourceRef="Activity_0c28pj1" targetRef="Event_1785mmb" />
    <bpmn:sendTask id="Activity_0c28pj1" name="Send Go Live Mail" camunda:delegateExpression="projectGoLiveSendTaskDelegate">
      <bpmn:incoming>Flow_18pc5rr</bpmn:incoming>
      <bpmn:outgoing>Flow_0q41fps</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:sendTask id="Activity_1a9pvom" name="Send CAB Mail" camunda:delegateExpression="projectCABSendTaskDelegate">
      <bpmn:incoming>Flow_1yaxx4u</bpmn:incoming>
      <bpmn:outgoing>Flow_1285wrk</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:sendTask id="Activity_1olbs5u" name="Send UAT Mail" camunda:delegateExpression="projectUATSendTaskDelegate">
      <bpmn:incoming>Flow_0c022hv</bpmn:incoming>
      <bpmn:outgoing>Flow_0invim5</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:userTask id="Activity_1u0ulwy" name="CAB Result Submit" camunda:formKey="cab_result_submit_form">
      <bpmn:incoming>Flow_1285wrk</bpmn:incoming>
      <bpmn:outgoing>Flow_1qpm6rr</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_11rp3os" sourceRef="Activity_006ndy8" targetRef="Gateway_1jgyblk" />
    <bpmn:serviceTask id="Activity_006ndy8" name="Check Service API" camunda:delegateExpression="projectCheckAPIServiceTaskDelegate">
      <bpmn:incoming>Flow_0bkkdmf</bpmn:incoming>
      <bpmn:outgoing>Flow_11rp3os</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0n2mwbu" sourceRef="Activity_0phi5jq" targetRef="Gateway_0tfc0or" />
    <bpmn:serviceTask id="Activity_0phi5jq" name="Check Development Ticket List" camunda:delegateExpression="projectCheckDevelopmentResultServiceTaskDelegate">
      <bpmn:incoming>Flow_0nnsz76</bpmn:incoming>
      <bpmn:outgoing>Flow_0n2mwbu</bpmn:outgoing>
    </bpmn:serviceTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="project_new_flow">
      <bpmndi:BPMNShape id="StartEvent_1_di" bpmnElement="StartEvent_1">
        <dc:Bounds x="582" y="202" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0es7yrm" bpmnElement="Event_0lkqwtz">
        <dc:Bounds x="742" y="202" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="728" y="246" width="64" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0kifoiu" bpmnElement="Event_1pmtv44">
        <dc:Bounds x="1042" y="202" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1049" y="246" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ohhv1q" bpmnElement="Event_17ywtoh">
        <dc:Bounds x="1502" y="202" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1510" y="246" width="21" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_04p79a3" bpmnElement="Event_1yqwfcq">
        <dc:Bounds x="2022" y="202" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2007" y="248" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_13fm60h" bpmnElement="Event_1o5r78e">
        <dc:Bounds x="2392" y="542" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2332" y="553" width="50" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1qxsvv2" bpmnElement="Event_0n2enpu">
        <dc:Bounds x="2102" y="852" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2109" y="828" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_08ulj9i" bpmnElement="Event_0kf6700">
        <dc:Bounds x="1482" y="852" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1488" y="828" width="24" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1rak3ap" bpmnElement="Event_12sxg21">
        <dc:Bounds x="912" y="852" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="910" y="828" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0squ366_di" bpmnElement="Event_0squ366">
        <dc:Bounds x="162" y="852" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_128kcsb_di" bpmnElement="Activity_1a06mfp">
        <dc:Bounds x="830" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0sa5faw_di" bpmnElement="Activity_01of7rq">
        <dc:Bounds x="1110" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_089la7e_di" bpmnElement="Gateway_089la7e" isMarkerVisible="true">
        <dc:Bounds x="1385" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1waq5qf_di" bpmnElement="Activity_19kid1z">
        <dc:Bounds x="1570" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0tfc0or_di" bpmnElement="Gateway_0tfc0or" isMarkerVisible="true">
        <dc:Bounds x="2385" y="425" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0isscbt_di" bpmnElement="Gateway_0isscbt" isMarkerVisible="true">
        <dc:Bounds x="2195" y="845" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ezce66_di" bpmnElement="Gateway_1ezce66" isMarkerVisible="true">
        <dc:Bounds x="1645" y="845" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0g8xup7_di" bpmnElement="Gateway_0g8xup7" isMarkerVisible="true">
        <dc:Bounds x="1035" y="845" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_07yo3b5_di" bpmnElement="Activity_09oz8ns">
        <dc:Bounds x="2160" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ogqaua_di" bpmnElement="Activity_0w8vsni">
        <dc:Bounds x="2360" y="720" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1jgyblk_di" bpmnElement="Gateway_1jgyblk" isMarkerVisible="true">
        <dc:Bounds x="535" y="845" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_10cyqhm_di" bpmnElement="Activity_195qa37">
        <dc:Bounds x="1980" y="830" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_08l7riz_di" bpmnElement="Activity_0gwmn4p">
        <dc:Bounds x="1360" y="830" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0qqg618_di" bpmnElement="Activity_0ghpwek">
        <dc:Bounds x="770" y="830" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0at4ace_di" bpmnElement="Activity_0ubxas7">
        <dc:Bounds x="1240" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_02l6iwt_di" bpmnElement="Gateway_02l6iwt" isMarkerVisible="true">
        <dc:Bounds x="1895" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_16ndc0d" bpmnElement="Event_1785mmb">
        <dc:Bounds x="292" y="852" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="291" y="898" width="38" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_04e01fc" bpmnElement="Event_0moi766">
        <dc:Bounds x="162" y="332" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="162" y="308" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1enous5_di" bpmnElement="Activity_0nccfn0">
        <dc:Bounds x="1730" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0dzr5do_di" bpmnElement="Activity_02ipeqq">
        <dc:Bounds x="2360" y="830" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0388gks_di" bpmnElement="Activity_1kc1xjo">
        <dc:Bounds x="1730" y="830" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1y5c3e6_di" bpmnElement="Activity_0c28pj1">
        <dc:Bounds x="390" y="830" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_04t9rsi_di" bpmnElement="Activity_1a9pvom">
        <dc:Bounds x="1230" y="830" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1plgycw_di" bpmnElement="Activity_1olbs5u">
        <dc:Bounds x="1860" y="830" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0mvj9nd_di" bpmnElement="Activity_1u0ulwy">
        <dc:Bounds x="1110" y="830" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0s1nvx1_di" bpmnElement="Activity_006ndy8">
        <dc:Bounds x="630" y="830" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1gto4i6_di" bpmnElement="Activity_0phi5jq">
        <dc:Bounds x="2350" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1rlmc1k_di" bpmnElement="Flow_1rlmc1k">
        <di:waypoint x="618" y="220" />
        <di:waypoint x="742" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ah14dz_di" bpmnElement="Flow_1ah14dz">
        <di:waypoint x="778" y="220" />
        <di:waypoint x="830" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0g6ie43_di" bpmnElement="Flow_0g6ie43">
        <di:waypoint x="1078" y="220" />
        <di:waypoint x="1110" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0hpm3iu_di" bpmnElement="Flow_0hpm3iu">
        <di:waypoint x="1538" y="220" />
        <di:waypoint x="1570" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0tkwwx7_di" bpmnElement="Flow_0tkwwx7">
        <di:waypoint x="912" y="870" />
        <di:waypoint x="870" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kxxfou_di" bpmnElement="Flow_0kxxfou">
        <di:waypoint x="1410" y="195" />
        <di:waypoint x="1410" y="100" />
        <di:waypoint x="1060" y="100" />
        <di:waypoint x="1060" y="202" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1219" y="82" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1i3sc4w_di" bpmnElement="Flow_1i3sc4w">
        <di:waypoint x="1435" y="220" />
        <di:waypoint x="1502" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1449" y="202" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oev6mh_di" bpmnElement="Flow_1oev6mh">
        <di:waypoint x="2058" y="220" />
        <di:waypoint x="2160" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nnsz76_di" bpmnElement="Flow_0nnsz76">
        <di:waypoint x="2260" y="220" />
        <di:waypoint x="2350" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0deuik0_di" bpmnElement="Flow_0deuik0">
        <di:waypoint x="2410" y="475" />
        <di:waypoint x="2410" y="542" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2420" y="506" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bql9dy_di" bpmnElement="Flow_0bql9dy">
        <di:waypoint x="2410" y="578" />
        <di:waypoint x="2410" y="720" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rt4sqj_di" bpmnElement="Flow_0rt4sqj">
        <di:waypoint x="2410" y="800" />
        <di:waypoint x="2410" y="830" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1um3byl_di" bpmnElement="Flow_1um3byl">
        <di:waypoint x="2102" y="870" />
        <di:waypoint x="2080" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0q0lr5p_di" bpmnElement="Flow_0q0lr5p">
        <di:waypoint x="1645" y="870" />
        <di:waypoint x="1518" y="870" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1563" y="852" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dxhsyh_di" bpmnElement="Flow_0dxhsyh">
        <di:waypoint x="1670" y="895" />
        <di:waypoint x="1670" y="1020" />
        <di:waypoint x="2120" y="1020" />
        <di:waypoint x="2120" y="888" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1879" y="1002" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yh1m2z_di" bpmnElement="Flow_1yh1m2z">
        <di:waypoint x="1482" y="870" />
        <di:waypoint x="1460" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yaxx4u_di" bpmnElement="Flow_1yaxx4u">
        <di:waypoint x="1360" y="870" />
        <di:waypoint x="1330" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12kmoio_di" bpmnElement="Flow_12kmoio">
        <di:waypoint x="1060" y="895" />
        <di:waypoint x="1060" y="1020" />
        <di:waypoint x="1500" y="1020" />
        <di:waypoint x="1500" y="888" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1264" y="1002" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hgycsv_di" bpmnElement="Flow_1hgycsv">
        <di:waypoint x="2435" y="450" />
        <di:waypoint x="2510" y="450" />
        <di:waypoint x="2510" y="80" />
        <di:waypoint x="2040" y="80" />
        <di:waypoint x="2040" y="202" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2513" y="262" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bkkdmf_di" bpmnElement="Flow_0bkkdmf">
        <di:waypoint x="770" y="870" />
        <di:waypoint x="730" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18pc5rr_di" bpmnElement="Flow_18pc5rr">
        <di:waypoint x="535" y="870" />
        <di:waypoint x="490" y="870" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="493" y="852" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_147xbe2_di" bpmnElement="Flow_147xbe2">
        <di:waypoint x="560" y="895" />
        <di:waypoint x="560" y="1020" />
        <di:waypoint x="930" y="1020" />
        <di:waypoint x="930" y="888" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="729" y="1002" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f2jp5p_di" bpmnElement="Flow_0f2jp5p">
        <di:waypoint x="930" y="220" />
        <di:waypoint x="1042" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="968" y="202" width="36" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01vcnay_di" bpmnElement="Flow_01vcnay">
        <di:waypoint x="1160" y="180" />
        <di:waypoint x="1160" y="150" />
        <di:waypoint x="1290" y="150" />
        <di:waypoint x="1290" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wfx2ts_di" bpmnElement="Flow_1wfx2ts">
        <di:waypoint x="1340" y="220" />
        <di:waypoint x="1385" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0d6xxbu_di" bpmnElement="Flow_0d6xxbu">
        <di:waypoint x="1920" y="245" />
        <di:waypoint x="1920" y="350" />
        <di:waypoint x="198" y="350" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1415" y="363" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pdu8cf_di" bpmnElement="Flow_0pdu8cf">
        <di:waypoint x="1945" y="220" />
        <di:waypoint x="2022" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1964" y="202" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vltohr_di" bpmnElement="Flow_0vltohr">
        <di:waypoint x="1670" y="220" />
        <di:waypoint x="1730" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02uq3b4_di" bpmnElement="Flow_02uq3b4">
        <di:waypoint x="292" y="870" />
        <di:waypoint x="198" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_171v1bj_di" bpmnElement="Flow_171v1bj">
        <di:waypoint x="180" y="368" />
        <di:waypoint x="180" y="852" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1onda3u_di" bpmnElement="Flow_1onda3u">
        <di:waypoint x="1830" y="220" />
        <di:waypoint x="1895" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wpz0yy_di" bpmnElement="Flow_1wpz0yy">
        <di:waypoint x="2360" y="870" />
        <di:waypoint x="2245" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05988sw_di" bpmnElement="Flow_05988sw">
        <di:waypoint x="1730" y="870" />
        <di:waypoint x="1695" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0c022hv_di" bpmnElement="Flow_0c022hv">
        <di:waypoint x="1980" y="870" />
        <di:waypoint x="1960" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0invim5_di" bpmnElement="Flow_0invim5">
        <di:waypoint x="1860" y="870" />
        <di:waypoint x="1830" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rcw46x_di" bpmnElement="Flow_1rcw46x">
        <di:waypoint x="2195" y="870" />
        <di:waypoint x="2138" y="870" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2150" y="843" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qpm6rr_di" bpmnElement="Flow_1qpm6rr">
        <di:waypoint x="1110" y="870" />
        <di:waypoint x="1085" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1cyj6er_di" bpmnElement="Flow_1cyj6er">
        <di:waypoint x="1035" y="870" />
        <di:waypoint x="948" y="870" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="972" y="852" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1285wrk_di" bpmnElement="Flow_1285wrk">
        <di:waypoint x="1230" y="870" />
        <di:waypoint x="1210" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10uaqhq_di" bpmnElement="Flow_10uaqhq">
        <di:waypoint x="2220" y="895" />
        <di:waypoint x="2220" y="1020" />
        <di:waypoint x="2520" y="1020" />
        <di:waypoint x="2520" y="560" />
        <di:waypoint x="2428" y="560" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2354" y="1002" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0q41fps_di" bpmnElement="Flow_0q41fps">
        <di:waypoint x="390" y="870" />
        <di:waypoint x="328" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11rp3os_di" bpmnElement="Flow_11rp3os">
        <di:waypoint x="630" y="870" />
        <di:waypoint x="585" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n2mwbu_di" bpmnElement="Flow_0n2mwbu">
        <di:waypoint x="2410" y="260" />
        <di:waypoint x="2410" y="425" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
