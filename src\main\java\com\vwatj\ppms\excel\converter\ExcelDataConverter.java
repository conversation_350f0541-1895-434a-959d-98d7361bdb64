package com.vwatj.ppms.excel.converter;

import java.util.Map;

/**
 * Excel数据转换接口
 * 定义实体与Excel行数据之间的转换规则
 *
 * @param <T> 实体类型
 * <AUTHOR>
 * @since 2025-07-09
 */
public interface ExcelDataConverter<T> {

    /**
     * 将实体转换为Excel行数据
     *
     * @param entity 实体对象
     * @return Excel行数据数组
     */
    Object[] entityToRowData(T entity);

    /**
     * 将Excel行数据转换为实体
     *
     * @param rowData 行数据Map，key为列名，value为单元格值
     * @param rowIndex 行索引（用于错误提示）
     * @return 实体对象
     * @throws ExcelDataConvertException 转换异常
     */
    T rowDataToEntity(Map<String, Object> rowData, int rowIndex) throws ExcelDataConvertException;

    /**
     * 获取导出表头
     *
     * @return 表头数组
     */
    String[] getExportHeaders();

    /**
     * 获取模板表头（带必填标识）
     *
     * @return 模板表头数组
     */
    String[] getTemplateHeaders();

    /**
     * 获取唯一标识符字段名
     *
     * @return 字段名
     */
    String getUniqueField();

    /**
     * 从实体中获取唯一标识符值
     *
     * @param entity 实体对象
     * @return 唯一标识符值
     */
    Object getUniqueFieldValue(T entity);

    /**
     * 获取列宽配置
     *
     * @return 列宽数组，单位为字符数
     */
    default int[] getColumnWidths() {
        String[] headers = getExportHeaders();
        int[] widths = new int[headers.length];
        
        for (int i = 0; i < headers.length; i++) {
            String header = headers[i];
            if (header.contains("编号") || header.contains("ID")) {
                widths[i] = 15;
            } else if (header.contains("名称") || header.contains("标题")) {
                widths[i] = 25;
            } else if (header.contains("描述") || header.contains("备注")) {
                widths[i] = 35;
            } else if (header.contains("时间") || header.contains("日期")) {
                widths[i] = 20;
            } else {
                widths[i] = 15;
            }
        }
        
        return widths;
    }

    /**
     * 验证必填字段
     *
     * @param rowData 行数据
     * @param rowIndex 行索引
     * @throws ExcelDataConvertException 验证失败异常
     */
    default void validateRequiredFields(Map<String, Object> rowData, int rowIndex) throws ExcelDataConvertException {
        String[] templateHeaders = getTemplateHeaders();
        
        for (String header : templateHeaders) {
            if (header.contains("*")) {
                String fieldName = header.replace("*", "").trim();
                Object value = rowData.get(fieldName);
                
                if (value == null || (value instanceof String && ((String) value).trim().isEmpty())) {
                    throw new ExcelDataConvertException(rowIndex, fieldName, "必填字段不能为空");
                }
            }
        }
    }
}
