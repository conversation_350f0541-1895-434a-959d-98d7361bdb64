package com.vwatj.ppms.config.typehandler;

import com.alibaba.fastjson2.JSONObject;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * JSONObject类型处理器
 * 用于处理数据库中JSON字段与JSONObject对象之间的转换
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@MappedTypes(JSONObject.class)
@MappedJdbcTypes({JdbcType.VARCHAR, JdbcType.LONGVARCHAR, JdbcType.CLOB})
public class JSONObjectTypeHandler extends BaseTypeHandler<JSONObject> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, JSONObject parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.toJSONString());
    }

    @Override
    public JSONObject getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String jsonString = rs.getString(columnName);
        return parseJSONObject(jsonString);
    }

    @Override
    public JSONObject getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String jsonString = rs.getString(columnIndex);
        return parseJSONObject(jsonString);
    }

    @Override
    public JSONObject getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String jsonString = cs.getString(columnIndex);
        return parseJSONObject(jsonString);
    }

    /**
     * 解析JSON字符串为JSONObject
     *
     * @param jsonString JSON字符串
     * @return JSONObject对象，如果字符串为空或null则返回null
     */
    private JSONObject parseJSONObject(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }
        
        try {
            return JSONObject.parseObject(jsonString);
        } catch (Exception e) {
            // 如果解析失败，记录日志并返回null
            System.err.println("Failed to parse JSON string: " + jsonString + ", error: " + e.getMessage());
            return null;
        }
    }
}
