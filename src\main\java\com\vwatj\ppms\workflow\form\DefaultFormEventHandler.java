package com.vwatj.ppms.workflow.form;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 默认表单事件处理器
 * 当没有找到对应formKey的处理器时使用
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Slf4j
public class DefaultFormEventHandler implements FormEventHandler {

    @Override
    public String getSupportedFormKey() {
        return "default";
    }

    @Override
    public void handleFormSubmit(Long projectId, String stageKey, String formKey, Map<String, Object> formData, String operation) {
        log.info("默认表单提交处理: projectId={}, stageKey={}, formKey={}, operation={}", 
                projectId, stageKey, formKey, operation);
        
        // 默认处理逻辑：只记录日志，不做特殊处理
        log.debug("表单数据: {}", formData);
    }

    @Override
    public void handleFormEdit(Long projectId, String stageKey, String formKey, Map<String, Object> formData) {
        log.info("默认表单编辑处理: projectId={}, stageKey={}, formKey={}",
                projectId, stageKey, formKey);

        // 默认处理逻辑：只记录日志，不做特殊处理
        log.debug("表单数据: {}", formData);
    }

    @Override
    public void handleFormUpdate(Long projectId, String stageKey, String formKey,
                                 Map<String, Object> formData, Map<String, Object> previousFormData) {
        log.info("默认表单更新处理: projectId={}, stageKey={}, formKey={}",
                projectId, stageKey, formKey);

        // 默认处理逻辑：记录数据变化
        log.debug("新表单数据: {}", formData);
        log.debug("之前表单数据: {}", previousFormData);

        // 可以在这里添加通用的数据变化检测逻辑
        if (previousFormData != null && !previousFormData.equals(formData)) {
            log.info("检测到表单数据变化: projectId={}, stageKey={}, formKey={}",
                    projectId, stageKey, formKey);
        }
    }

    @Override
    public void validateFormData(Long projectId, String stageKey, String formKey, Map<String, Object> formData, String operation) {
        log.debug("默认表单验证: projectId={}, stageKey={}, formKey={}, operation={}", 
                projectId, stageKey, formKey, operation);
        
        // 默认验证：检查基本字段
        if (formData == null || formData.isEmpty()) {
            throw new RuntimeException("表单数据不能为空");
        }
    }

    @Override
    public void afterFormSubmit(Long projectId, String stageKey, String formKey, Map<String, Object> formData, String operation) {
        log.debug("默认表单提交后处理: projectId={}, stageKey={}, formKey={}, operation={}", 
                projectId, stageKey, formKey, operation);
        
        // 默认后置处理：不做特殊处理
    }
}
