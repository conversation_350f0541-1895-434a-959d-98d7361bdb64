package com.vwatj.ppms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateSystemDictDTO;
import com.vwatj.ppms.dto.SystemDictQueryDTO;
import com.vwatj.ppms.dto.UpdateSystemDictDTO;
import com.vwatj.ppms.entity.SystemDict;

import java.util.List;
import java.util.Map;

/**
 * 系统字典服务接口
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public interface SystemDictService extends IService<SystemDict> {

    /**
     * 根据字典类型获取字典选项列表
     * @param dictType 字典类型
     * @return 选项列表
     */
    List<Map<String, String>> getDictOptionsByType(String dictType);

    /**
     * 获取所有字典类型
     * @return 字典类型列表
     */
    List<String> getAllDictTypes();

    /**
     * 根据字典类型和代码获取字典项
     * @param dictType 字典类型
     * @param dictCode 字典代码
     * @return 字典项
     */
    SystemDict getDictByTypeAndCode(String dictType, String dictCode);

    /**
     * 获取区域选项
     * @return 区域选项列表
     */
    List<Map<String, String>> getRegionOptions();

    /**
     * 获取资产状态选项
     * @return 资产状态选项列表
     */
    List<Map<String, String>> getAssetStateOptions();

    /**
     * 获取业务部门选项
     * @return 业务部门选项列表
     */
    List<Map<String, String>> getBusinessDepartmentOptions();

    /**
     * 获取使用状态选项
     * @return 使用状态选项列表
     */
    List<Map<String, String>> getUsageStatusOptions();

    /**
     * 获取优先级选项
     * @return 优先级选项列表
     */
    List<Map<String, String>> getPriorityOptions();

    /**
     * 获取站点选项
     * @return 站点选项列表
     */
    List<Map<String, String>> getSiteOptions();

    /**
     * 分页查询系统字典
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<SystemDict> getSystemDictPage(SystemDictQueryDTO queryDTO);

    /**
     * 创建系统字典
     * @param createSystemDictDTO 创建参数
     * @return 创建的系统字典
     */
    SystemDict createSystemDict(CreateSystemDictDTO createSystemDictDTO);

    /**
     * 更新系统字典
     * @param updateSystemDictDTO 更新参数
     * @return 更新的系统字典
     */
    SystemDict updateSystemDict(UpdateSystemDictDTO updateSystemDictDTO);

    /**
     * 删除系统字典
     * @param id 字典ID
     */
    void deleteSystemDict(Long id);
}
