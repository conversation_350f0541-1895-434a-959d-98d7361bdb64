package com.vwatj.ppms.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotNull;

/**
 * 发布流程DTO（创建新版本并发布）
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
public class PublishProcessDTO {

    /**
     * 流程定义ID（从路径参数设置）
     */
    private Long processDefinitionId;

    /**
     * 版本描述
     */
    private String description;

    /**
     * BPMN文件
     */
    @NotNull(message = "BPMN文件不能为空")
    private MultipartFile bpmnFile;
}
