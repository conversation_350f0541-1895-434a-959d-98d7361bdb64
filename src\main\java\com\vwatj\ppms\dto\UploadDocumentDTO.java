package com.vwatj.ppms.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 上传文档DTO
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
public class UploadDocumentDTO {
    
    /**
     * 文件
     */
    @NotNull(message = "文件不能为空")
    private MultipartFile file;
    
    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Long projectId;
    
    /**
     * 文档类别
     */
    @NotNull(message = "文档类别不能为空")
    private String category;
    
    /**
     * 文档描述
     */
    private String description;
    
    /**
     * 标签列表
     */
    private List<String> tags;
    
    /**
     * 是否公开
     */
    private Boolean isPublic = false;
    
    /**
     * 查看者ID列表
     */
    private List<String> viewers;
    
    /**
     * 编辑者ID列表
     */
    private List<String> editors;
}
