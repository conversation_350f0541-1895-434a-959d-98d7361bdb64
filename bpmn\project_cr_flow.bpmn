<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1fehqw9" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.37.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.23.0">
  <bpmn:process id="project_cr_flow" name="project_cr_flow" isExecutable="true" camunda:historyTimeToLive="0">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_1rlmc1k</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:intermediateThrowEvent id="Event_0lkqwtz" name="Requirement Collect" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{projectBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>requirement_collect</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1rlmc1k</bpmn:incoming>
      <bpmn:outgoing>Flow_1ah14dz</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1rlmc1k" sourceRef="StartEvent_1" targetRef="Event_0lkqwtz" />
    <bpmn:intermediateThrowEvent id="Event_1pmtv44" name="BBP" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{projectBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>bbp</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0kxxfou</bpmn:incoming>
      <bpmn:incoming>Flow_0f2jp5p</bpmn:incoming>
      <bpmn:outgoing>Flow_0g6ie43</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateThrowEvent id="Event_1yqwfcq" name="Development" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{projectBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>development</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1hgycsv</bpmn:incoming>
      <bpmn:incoming>Flow_1i3sc4w</bpmn:incoming>
      <bpmn:outgoing>Flow_1oev6mh</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateThrowEvent id="Event_1o5r78e" name="Inner Test" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{projectBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>inner_test</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0deuik0</bpmn:incoming>
      <bpmn:incoming>Flow_10uaqhq</bpmn:incoming>
      <bpmn:outgoing>Flow_0bql9dy</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateThrowEvent id="Event_0n2enpu" name="UAT" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{projectBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>uat</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0dxhsyh</bpmn:incoming>
      <bpmn:incoming>Flow_1rcw46x</bpmn:incoming>
      <bpmn:outgoing>Flow_1um3byl</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateThrowEvent id="Event_12sxg21" name="Release" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{projectBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>release</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_147xbe2</bpmn:incoming>
      <bpmn:incoming>Flow_0q0lr5p</bpmn:incoming>
      <bpmn:outgoing>Flow_0tkwwx7</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1ah14dz" sourceRef="Event_0lkqwtz" targetRef="Activity_1a06mfp" />
    <bpmn:sequenceFlow id="Flow_0g6ie43" sourceRef="Event_1pmtv44" targetRef="Activity_01of7rq" />
    <bpmn:endEvent id="Event_0squ366">
      <bpmn:incoming>Flow_02uq3b4</bpmn:incoming>
      <bpmn:incoming>Flow_171v1bj</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0tkwwx7" sourceRef="Event_12sxg21" targetRef="Activity_0ghpwek" />
    <bpmn:userTask id="Activity_1a06mfp" name="Requirement Submit" camunda:formKey="requirement_submit_form">
      <bpmn:incoming>Flow_1ah14dz</bpmn:incoming>
      <bpmn:outgoing>Flow_0f2jp5p</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_01of7rq" name="BBP File Submit" camunda:formKey="bbp_submit_form">
      <bpmn:incoming>Flow_0g6ie43</bpmn:incoming>
      <bpmn:outgoing>Flow_01vcnay</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_089la7e">
      <bpmn:incoming>Flow_1wfx2ts</bpmn:incoming>
      <bpmn:outgoing>Flow_0kxxfou</bpmn:outgoing>
      <bpmn:outgoing>Flow_1i3sc4w</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0kxxfou" name="Reject" sourceRef="Gateway_089la7e" targetRef="Event_1pmtv44">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "reject"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1oev6mh" sourceRef="Event_1yqwfcq" targetRef="Activity_09oz8ns" />
    <bpmn:exclusiveGateway id="Gateway_0tfc0or">
      <bpmn:incoming>Flow_0n2mwbu</bpmn:incoming>
      <bpmn:outgoing>Flow_0deuik0</bpmn:outgoing>
      <bpmn:outgoing>Flow_1hgycsv</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0nnsz76" sourceRef="Activity_09oz8ns" targetRef="Activity_0phi5jq" />
    <bpmn:sequenceFlow id="Flow_0deuik0" name="Approve" sourceRef="Gateway_0tfc0or" targetRef="Event_1o5r78e">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "approve"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0bql9dy" sourceRef="Event_1o5r78e" targetRef="Activity_0w8vsni" />
    <bpmn:exclusiveGateway id="Gateway_0isscbt">
      <bpmn:incoming>Flow_1wpz0yy</bpmn:incoming>
      <bpmn:outgoing>Flow_1rcw46x</bpmn:outgoing>
      <bpmn:outgoing>Flow_10uaqhq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0rt4sqj" sourceRef="Activity_0w8vsni" targetRef="Activity_02ipeqq" />
    <bpmn:sequenceFlow id="Flow_1um3byl" sourceRef="Event_0n2enpu" targetRef="Activity_195qa37" />
    <bpmn:exclusiveGateway id="Gateway_1ezce66">
      <bpmn:incoming>Flow_05988sw</bpmn:incoming>
      <bpmn:outgoing>Flow_0q0lr5p</bpmn:outgoing>
      <bpmn:outgoing>Flow_0dxhsyh</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0q0lr5p" sourceRef="Gateway_1ezce66" targetRef="Event_12sxg21">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "approve"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0dxhsyh" name="Reject" sourceRef="Gateway_1ezce66" targetRef="Event_0n2enpu">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "reject"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_09oz8ns" name="Development Completed Submit" camunda:formKey="development_submit_form">
      <bpmn:incoming>Flow_1oev6mh</bpmn:incoming>
      <bpmn:outgoing>Flow_0nnsz76</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1hgycsv" name="Reject" sourceRef="Gateway_0tfc0or" targetRef="Event_1yqwfcq">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "reject"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_0w8vsni" name="Test Completed Submit" camunda:formKey="test_submit_form">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0bql9dy</bpmn:incoming>
      <bpmn:outgoing>Flow_0rt4sqj</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_1jgyblk">
      <bpmn:incoming>Flow_11rp3os</bpmn:incoming>
      <bpmn:outgoing>Flow_18pc5rr</bpmn:outgoing>
      <bpmn:outgoing>Flow_147xbe2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0bkkdmf" sourceRef="Activity_0ghpwek" targetRef="Activity_006ndy8" />
    <bpmn:sequenceFlow id="Flow_18pc5rr" name="Approve" sourceRef="Gateway_1jgyblk" targetRef="Activity_0c28pj1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "approve"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_147xbe2" name="Reject" sourceRef="Gateway_1jgyblk" targetRef="Event_12sxg21">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "reject"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_195qa37" name="UAT Start Submit" camunda:formKey="uat_submit_form">
      <bpmn:incoming>Flow_1um3byl</bpmn:incoming>
      <bpmn:outgoing>Flow_0c022hv</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_0ghpwek" name="Go Live Compeleted Submit" camunda:formKey="golive_submit_form">
      <bpmn:incoming>Flow_0tkwwx7</bpmn:incoming>
      <bpmn:outgoing>Flow_0bkkdmf</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0f2jp5p" name="Submit" sourceRef="Activity_1a06mfp" targetRef="Event_1pmtv44" />
    <bpmn:sequenceFlow id="Flow_01vcnay" sourceRef="Activity_01of7rq" targetRef="Activity_0ubxas7" />
    <bpmn:sequenceFlow id="Flow_1wfx2ts" sourceRef="Activity_0ubxas7" targetRef="Gateway_089la7e" />
    <bpmn:userTask id="Activity_0ubxas7" name="Member Review Submit" camunda:formKey="bbp_review_submit_form">
      <bpmn:incoming>Flow_01vcnay</bpmn:incoming>
      <bpmn:outgoing>Flow_1wfx2ts</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:intermediateThrowEvent id="Event_1785mmb" name="Go Live" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{projectBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>go_live</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0q41fps</bpmn:incoming>
      <bpmn:outgoing>Flow_02uq3b4</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_02uq3b4" sourceRef="Event_1785mmb" targetRef="Event_0squ366" />
    <bpmn:intermediateThrowEvent id="Event_0moi766" name="Cancel" camunda:jobPriority="50">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="#{projectBoundaryEventDelegate}" event="start">
          <camunda:field name="targetStatus">
            <camunda:string>cancel</camunda:string>
          </camunda:field>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_171v1bj</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_171v1bj" sourceRef="Event_0moi766" targetRef="Event_0squ366" />
    <bpmn:serviceTask id="Activity_02ipeqq" name="Check Test Ticket List" camunda:delegateExpression="projectCheckTestResultServiceTaskDelegate">
      <bpmn:incoming>Flow_0rt4sqj</bpmn:incoming>
      <bpmn:outgoing>Flow_1wpz0yy</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1wpz0yy" sourceRef="Activity_02ipeqq" targetRef="Gateway_0isscbt" />
    <bpmn:sequenceFlow id="Flow_05988sw" sourceRef="Activity_1kc1xjo" targetRef="Gateway_1ezce66" />
    <bpmn:userTask id="Activity_1kc1xjo" name="UAT Result Submit" camunda:formKey="uat_result_submit_form">
      <bpmn:incoming>Flow_0invim5</bpmn:incoming>
      <bpmn:outgoing>Flow_05988sw</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0c022hv" sourceRef="Activity_195qa37" targetRef="Activity_1olbs5u" />
    <bpmn:sequenceFlow id="Flow_0invim5" sourceRef="Activity_1olbs5u" targetRef="Activity_1kc1xjo" />
    <bpmn:sequenceFlow id="Flow_1rcw46x" name="Approve" sourceRef="Gateway_0isscbt" targetRef="Event_0n2enpu">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "approve"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_10uaqhq" name="Reject" sourceRef="Gateway_0isscbt" targetRef="Event_1o5r78e">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "reject"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0q41fps" sourceRef="Activity_0c28pj1" targetRef="Event_1785mmb" />
    <bpmn:sendTask id="Activity_0c28pj1" name="Send Go Live Mail" camunda:delegateExpression="projectGoLiveSendTaskDelegate">
      <bpmn:incoming>Flow_18pc5rr</bpmn:incoming>
      <bpmn:outgoing>Flow_0q41fps</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:sendTask id="Activity_1olbs5u" name="Send UAT Mail" camunda:delegateExpression="projectUATSendTaskDelegate">
      <bpmn:incoming>Flow_0c022hv</bpmn:incoming>
      <bpmn:outgoing>Flow_0invim5</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:sequenceFlow id="Flow_11rp3os" sourceRef="Activity_006ndy8" targetRef="Gateway_1jgyblk" />
    <bpmn:serviceTask id="Activity_006ndy8" name="Check Service API" camunda:delegateExpression="projectCheckAPIServiceTaskDelegate">
      <bpmn:incoming>Flow_0bkkdmf</bpmn:incoming>
      <bpmn:outgoing>Flow_11rp3os</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0n2mwbu" sourceRef="Activity_0phi5jq" targetRef="Gateway_0tfc0or" />
    <bpmn:serviceTask id="Activity_0phi5jq" name="Check Development Ticket List" camunda:delegateExpression="projectCheckDevelopmentResultServiceTaskDelegate">
      <bpmn:incoming>Flow_0nnsz76</bpmn:incoming>
      <bpmn:outgoing>Flow_0n2mwbu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1i3sc4w" sourceRef="Gateway_089la7e" targetRef="Event_1yqwfcq">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${operation == "approve"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="project_cr_flow">
      <bpmndi:BPMNShape id="StartEvent_1_di" bpmnElement="StartEvent_1">
        <dc:Bounds x="582" y="202" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0es7yrm" bpmnElement="Event_0lkqwtz">
        <dc:Bounds x="742" y="202" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="728" y="246" width="64" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0kifoiu" bpmnElement="Event_1pmtv44">
        <dc:Bounds x="1042" y="202" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1049" y="246" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_04p79a3" bpmnElement="Event_1yqwfcq">
        <dc:Bounds x="2022" y="202" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2007" y="248" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_13fm60h" bpmnElement="Event_1o5r78e">
        <dc:Bounds x="2392" y="542" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2332" y="553" width="50" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1qxsvv2" bpmnElement="Event_0n2enpu">
        <dc:Bounds x="2102" y="852" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2109" y="828" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1rak3ap" bpmnElement="Event_12sxg21">
        <dc:Bounds x="912" y="852" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="910" y="828" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0squ366_di" bpmnElement="Event_0squ366">
        <dc:Bounds x="162" y="852" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_128kcsb_di" bpmnElement="Activity_1a06mfp">
        <dc:Bounds x="830" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0sa5faw_di" bpmnElement="Activity_01of7rq">
        <dc:Bounds x="1110" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_089la7e_di" bpmnElement="Gateway_089la7e" isMarkerVisible="true">
        <dc:Bounds x="1385" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0tfc0or_di" bpmnElement="Gateway_0tfc0or" isMarkerVisible="true">
        <dc:Bounds x="2385" y="425" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0isscbt_di" bpmnElement="Gateway_0isscbt" isMarkerVisible="true">
        <dc:Bounds x="2195" y="845" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ezce66_di" bpmnElement="Gateway_1ezce66" isMarkerVisible="true">
        <dc:Bounds x="1645" y="845" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_07yo3b5_di" bpmnElement="Activity_09oz8ns">
        <dc:Bounds x="2160" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ogqaua_di" bpmnElement="Activity_0w8vsni">
        <dc:Bounds x="2360" y="720" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1jgyblk_di" bpmnElement="Gateway_1jgyblk" isMarkerVisible="true">
        <dc:Bounds x="535" y="845" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_10cyqhm_di" bpmnElement="Activity_195qa37">
        <dc:Bounds x="1980" y="830" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0qqg618_di" bpmnElement="Activity_0ghpwek">
        <dc:Bounds x="770" y="830" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0at4ace_di" bpmnElement="Activity_0ubxas7">
        <dc:Bounds x="1240" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_16ndc0d" bpmnElement="Event_1785mmb">
        <dc:Bounds x="292" y="852" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="291" y="898" width="38" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_04e01fc" bpmnElement="Event_0moi766">
        <dc:Bounds x="162" y="332" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="162" y="308" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0dzr5do_di" bpmnElement="Activity_02ipeqq">
        <dc:Bounds x="2360" y="830" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0388gks_di" bpmnElement="Activity_1kc1xjo">
        <dc:Bounds x="1730" y="830" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1y5c3e6_di" bpmnElement="Activity_0c28pj1">
        <dc:Bounds x="390" y="830" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1plgycw_di" bpmnElement="Activity_1olbs5u">
        <dc:Bounds x="1860" y="830" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0s1nvx1_di" bpmnElement="Activity_006ndy8">
        <dc:Bounds x="630" y="830" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1gto4i6_di" bpmnElement="Activity_0phi5jq">
        <dc:Bounds x="2350" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1rlmc1k_di" bpmnElement="Flow_1rlmc1k">
        <di:waypoint x="618" y="220" />
        <di:waypoint x="742" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ah14dz_di" bpmnElement="Flow_1ah14dz">
        <di:waypoint x="778" y="220" />
        <di:waypoint x="830" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0g6ie43_di" bpmnElement="Flow_0g6ie43">
        <di:waypoint x="1078" y="220" />
        <di:waypoint x="1110" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0tkwwx7_di" bpmnElement="Flow_0tkwwx7">
        <di:waypoint x="912" y="870" />
        <di:waypoint x="870" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kxxfou_di" bpmnElement="Flow_0kxxfou">
        <di:waypoint x="1410" y="195" />
        <di:waypoint x="1410" y="100" />
        <di:waypoint x="1060" y="100" />
        <di:waypoint x="1060" y="202" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1219" y="82" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1i3sc4w_di" bpmnElement="Flow_1i3sc4w">
        <di:waypoint x="1435" y="220" />
        <di:waypoint x="2022" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1713" y="202" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oev6mh_di" bpmnElement="Flow_1oev6mh">
        <di:waypoint x="2058" y="220" />
        <di:waypoint x="2160" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nnsz76_di" bpmnElement="Flow_0nnsz76">
        <di:waypoint x="2260" y="220" />
        <di:waypoint x="2350" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0deuik0_di" bpmnElement="Flow_0deuik0">
        <di:waypoint x="2410" y="475" />
        <di:waypoint x="2410" y="542" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2420" y="506" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bql9dy_di" bpmnElement="Flow_0bql9dy">
        <di:waypoint x="2410" y="578" />
        <di:waypoint x="2410" y="720" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rt4sqj_di" bpmnElement="Flow_0rt4sqj">
        <di:waypoint x="2410" y="800" />
        <di:waypoint x="2410" y="830" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1um3byl_di" bpmnElement="Flow_1um3byl">
        <di:waypoint x="2102" y="870" />
        <di:waypoint x="2080" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0q0lr5p_di" bpmnElement="Flow_0q0lr5p">
        <di:waypoint x="1645" y="870" />
        <di:waypoint x="948" y="870" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1285" y="852" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dxhsyh_di" bpmnElement="Flow_0dxhsyh">
        <di:waypoint x="1670" y="895" />
        <di:waypoint x="1670" y="1020" />
        <di:waypoint x="2120" y="1020" />
        <di:waypoint x="2120" y="888" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1879" y="1002" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hgycsv_di" bpmnElement="Flow_1hgycsv">
        <di:waypoint x="2435" y="450" />
        <di:waypoint x="2510" y="450" />
        <di:waypoint x="2510" y="80" />
        <di:waypoint x="2040" y="80" />
        <di:waypoint x="2040" y="202" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2513" y="262" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bkkdmf_di" bpmnElement="Flow_0bkkdmf">
        <di:waypoint x="770" y="870" />
        <di:waypoint x="730" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18pc5rr_di" bpmnElement="Flow_18pc5rr">
        <di:waypoint x="535" y="870" />
        <di:waypoint x="490" y="870" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="493" y="852" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_147xbe2_di" bpmnElement="Flow_147xbe2">
        <di:waypoint x="560" y="895" />
        <di:waypoint x="560" y="1020" />
        <di:waypoint x="930" y="1020" />
        <di:waypoint x="930" y="888" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="729" y="1002" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f2jp5p_di" bpmnElement="Flow_0f2jp5p">
        <di:waypoint x="930" y="220" />
        <di:waypoint x="1042" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="968" y="202" width="36" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01vcnay_di" bpmnElement="Flow_01vcnay">
        <di:waypoint x="1160" y="180" />
        <di:waypoint x="1160" y="150" />
        <di:waypoint x="1290" y="150" />
        <di:waypoint x="1290" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wfx2ts_di" bpmnElement="Flow_1wfx2ts">
        <di:waypoint x="1340" y="220" />
        <di:waypoint x="1385" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02uq3b4_di" bpmnElement="Flow_02uq3b4">
        <di:waypoint x="292" y="870" />
        <di:waypoint x="198" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_171v1bj_di" bpmnElement="Flow_171v1bj">
        <di:waypoint x="180" y="368" />
        <di:waypoint x="180" y="852" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wpz0yy_di" bpmnElement="Flow_1wpz0yy">
        <di:waypoint x="2360" y="870" />
        <di:waypoint x="2245" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05988sw_di" bpmnElement="Flow_05988sw">
        <di:waypoint x="1730" y="870" />
        <di:waypoint x="1695" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0c022hv_di" bpmnElement="Flow_0c022hv">
        <di:waypoint x="1980" y="870" />
        <di:waypoint x="1960" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0invim5_di" bpmnElement="Flow_0invim5">
        <di:waypoint x="1860" y="870" />
        <di:waypoint x="1830" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rcw46x_di" bpmnElement="Flow_1rcw46x">
        <di:waypoint x="2195" y="870" />
        <di:waypoint x="2138" y="870" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2150" y="843" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10uaqhq_di" bpmnElement="Flow_10uaqhq">
        <di:waypoint x="2220" y="895" />
        <di:waypoint x="2220" y="1020" />
        <di:waypoint x="2520" y="1020" />
        <di:waypoint x="2520" y="560" />
        <di:waypoint x="2428" y="560" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2354" y="1002" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0q41fps_di" bpmnElement="Flow_0q41fps">
        <di:waypoint x="390" y="870" />
        <di:waypoint x="328" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11rp3os_di" bpmnElement="Flow_11rp3os">
        <di:waypoint x="630" y="870" />
        <di:waypoint x="585" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n2mwbu_di" bpmnElement="Flow_0n2mwbu">
        <di:waypoint x="2410" y="260" />
        <di:waypoint x="2410" y="425" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
