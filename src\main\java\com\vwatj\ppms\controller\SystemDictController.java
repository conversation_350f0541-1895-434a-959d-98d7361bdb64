package com.vwatj.ppms.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.vwatj.ppms.common.ApiResponse;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateSystemDictDTO;
import com.vwatj.ppms.dto.SystemDictQueryDTO;
import com.vwatj.ppms.dto.UpdateSystemDictDTO;
import com.vwatj.ppms.entity.SystemDict;
import com.vwatj.ppms.service.SystemDictService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 系统字典控制器
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@RestController
@RequestMapping("/system/dict")
@RequiredArgsConstructor
public class SystemDictController {

    private final SystemDictService systemDictService;

    /**
     * 根据字典类型获取字典选项列表
     */
    @GetMapping("/options/{dictType}")
    public ApiResponse<List<Map<String, String>>> getDictOptionsByType(@PathVariable String dictType) {
        return ApiResponse.success(systemDictService.getDictOptionsByType(dictType));
    }

    /**
     * 获取所有字典类型
     */
    @GetMapping("/types")
    public ApiResponse<List<String>> getAllDictTypes() {
        return ApiResponse.success(systemDictService.getAllDictTypes());
    }

    /**
     * 获取区域选项
     */
    @GetMapping("/regions")
    public ApiResponse<List<Map<String, String>>> getRegionOptions() {
        return ApiResponse.success(systemDictService.getRegionOptions());
    }

    /**
     * 获取资产状态选项
     */
    @GetMapping("/asset-states")
    public ApiResponse<List<Map<String, String>>> getAssetStateOptions() {
        return ApiResponse.success(systemDictService.getAssetStateOptions());
    }

    /**
     * 获取业务部门选项
     */
    @GetMapping("/business-departments")
    public ApiResponse<List<Map<String, String>>> getBusinessDepartmentOptions() {
        return ApiResponse.success(systemDictService.getBusinessDepartmentOptions());
    }

    /**
     * 获取使用状态选项
     */
    @GetMapping("/usage-statuses")
    public ApiResponse<List<Map<String, String>>> getUsageStatusOptions() {
        return ApiResponse.success(systemDictService.getUsageStatusOptions());
    }

    /**
     * 获取优先级选项
     */
    @GetMapping("/priorities")
    public ApiResponse<List<Map<String, String>>> getPriorityOptions() {
        return ApiResponse.success(systemDictService.getPriorityOptions());
    }

    /**
     * 获取站点选项
     */
    @GetMapping("/sites")
    public ApiResponse<List<Map<String, String>>> getSiteOptions() {
        return ApiResponse.success(systemDictService.getSiteOptions());
    }

    /**
     * 分页查询系统字典
     */
    @GetMapping("/page")
    public ApiResponse<PageResult<SystemDict>> getSystemDictPage(SystemDictQueryDTO queryDTO) {
        PageResult<SystemDict> result = systemDictService.getSystemDictPage(queryDTO);
        return ApiResponse.success(result);
    }

    /**
     * 根据ID查询系统字典
     */
    @GetMapping("/{id}")
    public ApiResponse<SystemDict> getSystemDict(@PathVariable Long id) {
        SystemDict systemDict = systemDictService.getById(id);
        if (systemDict == null) {
            return ApiResponse.notFound("系统字典不存在");
        }
        return ApiResponse.success(systemDict);
    }

    /**
     * 创建系统字典
     */
    @PostMapping
    public ApiResponse<SystemDict> createSystemDict(@Validated @RequestBody CreateSystemDictDTO createSystemDictDTO) {
        try {
            SystemDict systemDict = systemDictService.createSystemDict(createSystemDictDTO);
            return ApiResponse.success("系统字典创建成功", systemDict);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 更新系统字典
     */
    @PutMapping("/{id}")
    public ApiResponse<SystemDict> updateSystemDict(@PathVariable Long id, @Validated @RequestBody UpdateSystemDictDTO updateSystemDictDTO) {
        try {
            updateSystemDictDTO.setId(id);
            SystemDict systemDict = systemDictService.updateSystemDict(updateSystemDictDTO);
            return ApiResponse.success("系统字典更新成功", systemDict);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 删除系统字典
     */
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteSystemDict(@PathVariable Long id) {
        try {
            systemDictService.deleteSystemDict(id);
            return ApiResponse.success("系统字典删除成功");
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
}
