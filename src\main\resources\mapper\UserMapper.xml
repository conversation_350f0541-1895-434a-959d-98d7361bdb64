<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vwatj.ppms.mapper.UserMapper">

    <!-- 根据用户名查询用户 -->
    <select id="selectByUsername" resultType="com.vwatj.ppms.entity.User">
        SELECT * FROM sys_user WHERE username = #{username}
    </select>

    <!-- 根据邮箱查询用户 -->
    <select id="selectByEmail" resultType="com.vwatj.ppms.entity.User">
        SELECT * FROM sys_user WHERE email = #{email}
    </select>

    <!-- 分页查询用户 -->
    <select id="selectUserPage" resultType="com.vwatj.ppms.entity.User">
        SELECT * FROM sys_user
        <where>
            <if test="keyword != null and keyword != ''">
                AND (username LIKE CONCAT('%', #{keyword}, '%') 
                OR email LIKE CONCAT('%', #{keyword}, '%') 
                OR full_name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="role != null and role != ''">
                AND role = #{role}
            </if>
            <if test="department != null and department != ''">
                AND department = #{department}
            </if>
            <if test="isActive != null">
                AND is_active = #{isActive}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

</mapper>
