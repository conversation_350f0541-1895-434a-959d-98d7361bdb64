package com.vwatj.ppms.controller;

import com.vwatj.ppms.common.ApiResponse;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateSystemAssetDTO;
import com.vwatj.ppms.dto.SystemAssetQueryDTO;
import com.vwatj.ppms.dto.UpdateSystemAssetDTO;
import com.vwatj.ppms.entity.SystemAsset;
import com.vwatj.ppms.service.SystemAssetService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.Map;

/**
 * 系统资产控制器
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@RestController
@RequestMapping("/system/assets")
@RequiredArgsConstructor
public class SystemAssetController {
    
    private final SystemAssetService systemAssetService;
    
    /**
     * 分页查询系统资产
     */
    @GetMapping
    public ApiResponse<PageResult<SystemAsset>> getSystemAssets(SystemAssetQueryDTO queryDTO) {
        PageResult<SystemAsset> result = systemAssetService.getSystemAssetPage(queryDTO);
        return ApiResponse.success(result);
    }
    
    /**
     * 根据ID查询系统资产
     */
    @GetMapping("/{id}")
    public ApiResponse<SystemAsset> getSystemAsset(@PathVariable Long id) {
        SystemAsset systemAsset = systemAssetService.getById(id);
        if (systemAsset == null) {
            return ApiResponse.notFound("系统资产不存在");
        }
        return ApiResponse.success(systemAsset);
    }
    
    /**
     * 创建系统资产
     */
    @PostMapping
    public ApiResponse<SystemAsset> createSystemAsset(@Validated @RequestBody CreateSystemAssetDTO createSystemAssetDTO) {
        try {
            SystemAsset systemAsset = systemAssetService.createSystemAsset(createSystemAssetDTO);
            return ApiResponse.success("系统资产创建成功", systemAsset);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 更新系统资产
     */
    @PutMapping("/{id}")
    public ApiResponse<SystemAsset> updateSystemAsset(@PathVariable Long id, @Validated @RequestBody UpdateSystemAssetDTO updateSystemAssetDTO) {
        try {
            updateSystemAssetDTO.setId(id);
            SystemAsset systemAsset = systemAssetService.updateSystemAsset(updateSystemAssetDTO);
            return ApiResponse.success("系统资产更新成功", systemAsset);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 删除系统资产
     */
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteSystemAsset(@PathVariable Long id) {
        try {
            systemAssetService.deleteSystemAsset(id);
            return ApiResponse.success("系统资产删除成功");
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 导出系统资产数据或下载模板
     * @param template 是否为模板下载，true=下载模板，false=导出数据
     * @param keyword 关键词
     * @param region 区域
     * @param businessDepartment 业务部门
     * @param assetState 资产状态
     * @param pmOwner PM负责人
     * @param page 页码
     * @param pageSize 每页大小
     */
    @GetMapping("/export")
    public ResponseEntity<Resource> exportAssets(
            @RequestParam(value = "template", defaultValue = "false") boolean template,
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "region", required = false) String region,
            @RequestParam(value = "businessDepartment", required = false) String businessDepartment,
            @RequestParam(value = "assetState", required = false) String assetState,
            @RequestParam(value = "pmOwner", required = false) String pmOwner,
            @RequestParam(value = "page", required = false) Long page,
            @RequestParam(value = "pageSize", required = false) Long pageSize) {
        try {
            Resource resource;
            String filename;

            if (template) {
                // 下载模板
                resource = systemAssetService.generateTemplate();
                filename = "系统资产导入模板.xlsx";
            } else {
                // 构建查询条件
                SystemAssetQueryDTO queryDTO = new SystemAssetQueryDTO();
                queryDTO.setKeyword(keyword);
                queryDTO.setRegion(region);
                queryDTO.setBusinessDepartment(businessDepartment);
                queryDTO.setAssetState(assetState);
                queryDTO.setPmOwner(pmOwner);
                queryDTO.setPage(page != null ? page : 1L);
                queryDTO.setPageSize(pageSize != null ? pageSize : 10000L); // 导出时使用大的页面大小

                // 导出数据
                resource = systemAssetService.exportAssets(queryDTO);
                filename = "系统资产数据_" +
                    java.time.LocalDateTime.now().format(
                        java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")
                    ) + ".xlsx";
            }

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .header(HttpHeaders.CONTENT_DISPOSITION,
                           "attachment; filename=\"" + filename + "\"")
                    .body(resource);
        } catch (Exception e) {
            log.error("导出失败", e);
            throw new RuntimeException("导出失败: " + e.getMessage());
        }
    }

    /**
     * 导入系统资产
     */
    @PostMapping("/import")
    public SseEmitter importAssets(
            @RequestParam("file") MultipartFile file,
            @RequestParam("mode") String mode) {
        try {
            if (file.isEmpty()) {
                SseEmitter emitter = new SseEmitter();
                try {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data(Map.of("message", "文件不能为空")));
                    emitter.complete();
                } catch (Exception e) {
                    emitter.completeWithError(e);
                }
                return emitter;
            }

            // 验证文件类型
            String filename = file.getOriginalFilename();
            if (filename == null || (!filename.endsWith(".xlsx") && !filename.endsWith(".xls"))) {
                SseEmitter emitter = new SseEmitter();
                try {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data(Map.of("message", "只支持 .xlsx 和 .xls 格式的Excel文件")));
                    emitter.complete();
                } catch (Exception e) {
                    emitter.completeWithError(e);
                }
                return emitter;
            }

            // 验证导入模式
            if (!"incremental".equals(mode) && !"overwrite".equals(mode)) {
                SseEmitter emitter = new SseEmitter();
                try {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data(Map.of("message", "导入模式只能是 incremental 或 overwrite")));
                    emitter.complete();
                } catch (Exception e) {
                    emitter.completeWithError(e);
                }
                return emitter;
            }

            return systemAssetService.importAssets(file, mode);
        } catch (Exception e) {
            log.error("导入系统资产失败", e);
            SseEmitter emitter = new SseEmitter();
            try {
                emitter.send(SseEmitter.event()
                    .name("error")
                    .data(Map.of("message", "导入失败: " + e.getMessage())));
                emitter.complete();
            } catch (Exception ex) {
                emitter.completeWithError(ex);
            }
            return emitter;
        }
    }


}
