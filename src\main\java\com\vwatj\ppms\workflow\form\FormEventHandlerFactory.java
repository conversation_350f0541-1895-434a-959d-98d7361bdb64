package com.vwatj.ppms.workflow.form;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 表单事件处理器工厂类
 * 用于根据formKey获取对应的处理器实例
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Slf4j
@Component
public class FormEventHandlerFactory {

    @Autowired
    private List<FormEventHandler> formEventHandlers;

    private final Map<String, FormEventHandler> handlerMap = new HashMap<>();

    /**
     * 初始化处理器映射
     */
    @PostConstruct
    public void initHandlerMap() {
        if (formEventHandlers != null) {
            for (FormEventHandler handler : formEventHandlers) {
                String formKey = handler.getSupportedFormKey();
                if (formKey != null && !formKey.trim().isEmpty()) {
                    handlerMap.put(formKey, handler);
                    log.info("注册表单事件处理器: formKey={}, handler={}", formKey, handler.getClass().getSimpleName());
                }
            }
        }
        log.info("表单事件处理器初始化完成，共注册{}个处理器", handlerMap.size());
    }

    /**
     * 根据formKey获取对应的处理器
     *
     * @param formKey 表单Key
     * @return 表单事件处理器，如果没有找到则返回默认处理器
     */
    public FormEventHandler getHandler(String formKey) {
        if (formKey == null || formKey.trim().isEmpty()) {
            log.warn("formKey为空，返回默认处理器");
            return getDefaultHandler();
        }

        FormEventHandler handler = handlerMap.get(formKey);
        if (handler == null) {
            log.warn("未找到formKey对应的处理器: {}, 返回默认处理器", formKey);
            return getDefaultHandler();
        }

        log.debug("找到formKey对应的处理器: formKey={}, handler={}", formKey, handler.getClass().getSimpleName());
        return handler;
    }

    /**
     * 获取默认处理器
     *
     * @return 默认表单事件处理器
     */
    private FormEventHandler getDefaultHandler() {
        return new DefaultFormEventHandler();
    }

    /**
     * 获取所有已注册的formKey
     *
     * @return formKey集合
     */
    public java.util.Set<String> getAllSupportedFormKeys() {
        return handlerMap.keySet();
    }

    /**
     * 检查是否支持指定的formKey
     *
     * @param formKey 表单Key
     * @return 是否支持
     */
    public boolean isSupported(String formKey) {
        return handlerMap.containsKey(formKey);
    }
}
