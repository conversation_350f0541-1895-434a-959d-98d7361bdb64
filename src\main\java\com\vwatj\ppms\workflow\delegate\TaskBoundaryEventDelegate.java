package com.vwatj.ppms.workflow.delegate;

import com.vwatj.ppms.entity.ProjectTask;
import com.vwatj.ppms.enums.TaskStatusEnum;
import com.vwatj.ppms.service.ProjectTaskService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.Expression;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 任务边界事件监听器
 * 用于监听工作流边界事件并自动更新任务状态
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Slf4j
@Component("taskBoundaryEventDelegate")
public class TaskBoundaryEventDelegate implements JavaDelegate {

    @Resource
    private ProjectTaskService projectTaskService;

    private Expression targetStatus;


    @Override
    public void execute(DelegateExecution execution) throws Exception {

        try {
            log.info("边界事件触发: activityId={}, eventName={}, processInstanceId={}",
                    execution.getCurrentActivityId(),
                    execution.getEventName(),
                    execution.getProcessInstanceId());

            // 获取流程变量中的任务ID
            Object taskIdObj = execution.getVariable("taskId");
            if (taskIdObj == null) {
                log.warn("流程变量中未找到taskId: processInstanceId={}", execution.getProcessInstanceId());
                return;
            }

            Long taskId = Long.valueOf(taskIdObj.toString());

            // 获取当前任务
            ProjectTask projectTask = projectTaskService.getById(taskId);
            if (projectTask == null) {
                log.warn("未找到任务: taskId={}", taskId);
                return;
            }

            // 根据边界事件类型更新任务状态
            TaskStatusEnum newStatus = determineStatusByBoundaryEvent(execution);

            if (newStatus != null && !newStatus.equals(projectTask.getStatus())) {
                log.info("边界事件更新任务状态: taskId={}, oldStatus={}, newStatus={}",
                        taskId, projectTask.getStatus(), newStatus);

                updateTaskStatusByBoundaryEvent(projectTask, newStatus);
            }

        } catch (Exception e) {
            log.error("边界事件监听器处理失败: processInstanceId={}",
                    execution.getProcessInstanceId(), e);
            throw e;
        }
    }

    /**
     * 根据边界事件确定目标状态
     */
    private TaskStatusEnum determineStatusByBoundaryEvent(DelegateExecution execution) {
        // 可以根据活动ID或其他流程变量来确定目标状态

        // 检查流程变量中是否有目标状态
        String status = (String) targetStatus.getValue(execution);
        if (status != null) {
            if (TaskStatusEnum.isValidCode(status)) {
                return TaskStatusEnum.fromCode(status);
            }
        }
        return null;
    }

    /**
     * 通过边界事件更新任务状态
     */
    private void updateTaskStatusByBoundaryEvent(ProjectTask projectTask, TaskStatusEnum newStatus) {
        TaskStatusEnum oldStatus = projectTask.getStatus();
        projectTask.setStatus(newStatus);

        // 根据状态更新时间
        LocalDateTime now = LocalDateTime.now();
        switch (newStatus) {
            case IN_PROGRESS:
                if (projectTask.getActualStartTime() == null) {
                    projectTask.setActualStartTime(now);
                }
                break;
            case CLOSE:
                if (projectTask.getActualEndTime() == null) {
                    projectTask.setActualEndTime(now);
                }
                break;
            case OPEN:
                // 重新打开时清除结束时间
                projectTask.setActualEndTime(null);
                break;
        }

        // 更新任务
        projectTaskService.updateById(projectTask);

        log.info("边界事件成功更新任务状态: taskId={}, oldStatus={}, newStatus={}",
                projectTask.getId(), oldStatus, newStatus);
    }
}
