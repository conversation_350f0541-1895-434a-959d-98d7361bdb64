package com.vwatj.ppms.workflow.form.handler;

import com.vwatj.ppms.workflow.form.FormEventHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 需求提交表单处理器
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Slf4j
@Component
public class RequirementSubmitFormHandler implements FormEventHandler {

    @Override
    public String getSupportedFormKey() {
        return "requirement_submit_form";
    }

    @Override
    public void handleFormSubmit(Long projectId, String stageKey, String formKey, Map<String, Object> formData, String operation) {
        log.info("处理需求提交表单: projectId={}, stageKey={}, operation={}", projectId, stageKey, operation);

        // 需求提交特定的业务逻辑
        switch (operation) {
            case "submit":
                handleRequirementSubmit(projectId, formData);
                break;
            case "approve":
                handleRequirementApprove(projectId, formData);
                break;
            case "reject":
                handleRequirementReject(projectId, formData);
                break;
            default:
                log.warn("未知的操作类型: {}", operation);
        }
    }

    @Override
    public void handleFormEdit(Long projectId, String stageKey, String formKey, Map<String, Object> formData) {
        log.info("处理需求表单编辑: projectId={}, stageKey={}", projectId, stageKey);

        // 需求编辑特定的业务逻辑
        validateRequirementData(formData);

        // 可以在这里添加需求变更通知等逻辑
        notifyRequirementChange(projectId, formData);
    }

    @Override
    public void handleFormUpdate(Long projectId, String stageKey, String formKey,
                                 Map<String, Object> formData, Map<String, Object> previousFormData) {
        log.info("处理需求表单更新: projectId={}, stageKey={}", projectId, stageKey);

        // 验证需求数据
        validateRequirementData(formData);

        // 检查需求是否发生重大变化
        boolean hasSignificantChanges = checkSignificantRequirementChanges(formData, previousFormData);

        if (hasSignificantChanges) {
            // 发送需求变更通知
            notifyRequirementChange(projectId, formData);

            // 记录需求变更历史
            recordRequirementChangeHistory(projectId, formData, previousFormData);

            log.info("检测到需求重大变更，已发送通知并记录历史: projectId={}", projectId);
        }

        log.info("需求表单更新处理完成: projectId={}, hasSignificantChanges={}",
                projectId, hasSignificantChanges);
    }

    @Override
    public void validateFormData(Long projectId, String stageKey, String formKey, Map<String, Object> formData, String operation) {
        log.debug("验证需求提交表单数据");
    }

    @Override
    public void afterFormSubmit(Long projectId, String stageKey, String formKey, Map<String, Object> formData, String operation) {
        log.info("需求提交表单后置处理: projectId={}, operation={}", projectId, operation);

        // 需求提交后的后置处理
        if ("submit".equals(operation)) {
            // 发送需求提交通知
            sendRequirementSubmitNotification(projectId, formData);
        } else if ("approve".equals(operation)) {
            // 发送需求批准通知
            sendRequirementApproveNotification(projectId, formData);
        }
    }

    /**
     * 处理需求提交
     */
    private void handleRequirementSubmit(Long projectId, Map<String, Object> formData) {
        log.info("处理需求提交: projectId={}", projectId);
        // TODO: 实现需求提交的具体逻辑
        // 例如：创建需求记录、分配需求编号等
    }

    /**
     * 处理需求批准
     */
    private void handleRequirementApprove(Long projectId, Map<String, Object> formData) {
        log.info("处理需求批准: projectId={}", projectId);
        // TODO: 实现需求批准的具体逻辑
        // 例如：更新需求状态、触发下一阶段等
    }

    /**
     * 处理需求拒绝
     */
    private void handleRequirementReject(Long projectId, Map<String, Object> formData) {
        log.info("处理需求拒绝: projectId={}", projectId);
        // TODO: 实现需求拒绝的具体逻辑
        // 例如：记录拒绝原因、通知相关人员等
    }

    /**
     * 验证需求数据
     */
    private void validateRequirementData(Map<String, Object> formData) {
        // TODO: 实现需求数据验证逻辑
        log.debug("验证需求数据: {}", formData);
    }

    /**
     * 通知需求变更
     */
    private void notifyRequirementChange(Long projectId, Map<String, Object> formData) {
        // TODO: 实现需求变更通知逻辑
        log.debug("通知需求变更: projectId={}", projectId);
    }

    /**
     * 发送需求提交通知
     */
    private void sendRequirementSubmitNotification(Long projectId, Map<String, Object> formData) {
        // TODO: 实现需求提交通知逻辑
        log.debug("发送需求提交通知: projectId={}", projectId);
    }

    /**
     * 发送需求批准通知
     */
    private void sendRequirementApproveNotification(Long projectId, Map<String, Object> formData) {
        // TODO: 实现需求批准通知逻辑
        log.debug("发送需求批准通知: projectId={}", projectId);
    }

    /**
     * 检查需求是否发生重大变化
     */
    private boolean checkSignificantRequirementChanges(Map<String, Object> formData, Map<String, Object> previousFormData) {
        if (previousFormData == null) {
            return true; // 如果没有之前的数据，认为是重大变化
        }

        // 检查关键字段的变化
        String[] significantFields = {
            "requirementTitle", "requirementDescription", "priority",
            "expectedDeliveryDate", "businessValue", "acceptanceCriteria"
        };

        for (String field : significantFields) {
            Object newValue = formData.get(field);
            Object oldValue = previousFormData.get(field);

            if (!Objects.equals(newValue, oldValue)) {
                log.debug("检测到需求重大变化字段: {}, 新值: {}, 旧值: {}", field, newValue, oldValue);
                return true;
            }
        }

        return false;
    }

    /**
     * 记录需求变更历史
     */
    private void recordRequirementChangeHistory(Long projectId, Map<String, Object> formData, Map<String, Object> previousFormData) {
        log.info("记录需求变更历史: projectId={}", projectId);

        // 记录变更详情
        log.debug("需求变更详情: projectId={}, 新数据={}, 旧数据={}",
                projectId, formData, previousFormData);

        // TODO: 实现需求变更历史记录逻辑，如保存到数据库、生成变更报告等
    }
}
