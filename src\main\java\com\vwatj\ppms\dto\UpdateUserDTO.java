package com.vwatj.ppms.dto;

import lombok.Data;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;

/**
 * 更新用户DTO
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
public class UpdateUserDTO {
    
    /**
     * 用户ID
     */
    private String id;
    
    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    private String email;
    
    /**
     * 全名
     */
    private String fullName;
    
    /**
     * 角色
     */
    private String role;
    
    /**
     * 部门
     */
    private String department;
    
    /**
     * 职位
     */
    private String position;
    
    /**
     * 电话
     */
    private String phone;
    
    /**
     * 头像
     */
    private String avatar;
}
