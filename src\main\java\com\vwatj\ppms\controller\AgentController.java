package com.vwatj.ppms.controller;

import com.vwatj.ppms.common.ApiResponse;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.AgentQueryDTO;
import com.vwatj.ppms.dto.CreateAgentDTO;
import com.vwatj.ppms.dto.UpdateAgentDTO;
import com.vwatj.ppms.entity.Agent;
import com.vwatj.ppms.service.AgentService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * Agent控制器
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@RestController
@RequestMapping("/agents")
@RequiredArgsConstructor
public class AgentController {
    
    private final AgentService agentService;
    
    /**
     * 分页查询Agent
     */
    @GetMapping
    public ApiResponse<PageResult<Agent>> getAgents(AgentQueryDTO queryDTO) {
        PageResult<Agent> result = agentService.getAgentPage(queryDTO);
        return ApiResponse.success(result);
    }
    
    /**
     * 根据ID查询Agent
     */
    @GetMapping("/{id}")
    public ApiResponse<Agent> getAgent(@PathVariable Long id) {
        Agent agent = agentService.getById(id);
        if (agent == null) {
            return ApiResponse.notFound("Agent不存在");
        }
        return ApiResponse.success(agent);
    }
    
    /**
     * 创建Agent
     */
    @PostMapping
    public ApiResponse<Agent> createAgent(@Validated @RequestBody CreateAgentDTO createAgentDTO) {
        try {
            Agent agent = agentService.createAgent(createAgentDTO);
            return ApiResponse.success("Agent创建成功", agent);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 更新Agent
     */
    @PutMapping("/{id}")
    public ApiResponse<Agent> updateAgent(@PathVariable Long id, @Validated @RequestBody UpdateAgentDTO updateAgentDTO) {
        try {
            updateAgentDTO.setId(id);
            Agent agent = agentService.updateAgent(updateAgentDTO);
            return ApiResponse.success("Agent更新成功", agent);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 删除Agent
     */
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteAgent(@PathVariable Long id) {
        try {
            agentService.deleteAgent(id);
            return ApiResponse.success("Agent删除成功");
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 切换Agent状态
     */
    @PatchMapping("/{id}/toggle-status")
    public ApiResponse<String> toggleAgentStatus(@PathVariable Long id) {
        try {
            agentService.toggleAgentStatus(id);
            return ApiResponse.success("Agent状态切换成功");
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
}
