package com.vwatj.ppms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateTaskDTO;
import com.vwatj.ppms.dto.TaskQueryDTO;
import com.vwatj.ppms.dto.UpdateTaskDTO;
import com.vwatj.ppms.entity.ProjectTask;
import com.vwatj.ppms.entity.ProjectProcess;
import com.vwatj.ppms.enums.TaskStatusEnum;
import com.vwatj.ppms.exception.BusinessException;
import com.vwatj.ppms.excel.generate.TaskExcelGenerate;
import com.vwatj.ppms.mapper.ProjectTaskMapper;
import com.vwatj.ppms.service.ProjectTaskService;
import com.vwatj.ppms.service.ProjectProcessService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.HistoryService;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectTaskServiceImpl extends ServiceImpl<ProjectTaskMapper, ProjectTask> implements ProjectTaskService {

    private final ProjectTaskMapper projectTaskMapper;
    private final RuntimeService runtimeService;
    private final TaskService camundaProjectTaskService;
    private final HistoryService historyService;
    private final ProjectProcessService projectProcessService;

    @Override
    public PageResult<ProjectTask> getTaskPage(TaskQueryDTO queryDTO) {
        Page<ProjectTask> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
        IPage<ProjectTask> result = projectTaskMapper.selectTaskPage(page,
                queryDTO.getKeyword(),
                queryDTO.getProjectId(),
                queryDTO.getStatus(),
                queryDTO.getPriority(),
                queryDTO.getStageName(),
                queryDTO.getIssueType(),
                queryDTO.getAssignee(),
                queryDTO.getReporter(),
                queryDTO.getPlannedStartTime(),
                queryDTO.getPlannedEndTime(),
                queryDTO.getActualStartTime(),
                queryDTO.getActualEndTime());
        return PageResult.from(result);
    }

    @Override
    public PageResult<ProjectTask> getProjectTasks(Long projectId, TaskQueryDTO queryDTO) {
        Page<ProjectTask> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
        IPage<ProjectTask> result = projectTaskMapper.selectProjectTasks(page, projectId);
        return PageResult.from(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectTask createTask(CreateTaskDTO createTaskDTO) {
        // 参数验证
        if (createTaskDTO.getTitle() == null || createTaskDTO.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("任务标题不能为空");
        }

        ProjectTask projectTask = new ProjectTask();
        BeanUtil.copyProperties(createTaskDTO, projectTask);

        // 自动生成任务编号（如果为空）
        if (createTaskDTO.getTaskNo() == null || createTaskDTO.getTaskNo().trim().isEmpty()) {
            String generatedTaskNo = generateTaskNo(createTaskDTO.getProjectId(), createTaskDTO.getStageName());
            projectTask.setTaskNo(generatedTaskNo);
        } else {
            // 如果提供了任务编号，检查是否已存在
            QueryWrapper<ProjectTask> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("task_no", createTaskDTO.getTaskNo()).eq("project_id", createTaskDTO.getProjectId());
            if (count(queryWrapper) > 0) {
                throw BusinessException.of("任务编号已存在");
            }
            projectTask.setTaskNo(createTaskDTO.getTaskNo());
        }

        // 设置默认状态
        if (projectTask.getStatus() == null) {
            projectTask.setStatus(TaskStatusEnum.OPEN);
        }

        save(projectTask);

        // 启动任务工作流
        startTaskFlow(projectTask);

        return projectTask;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectTask updateTask(UpdateTaskDTO updateTaskDTO) {
        if (updateTaskDTO.getId() == null) {
            throw BusinessException.of("任务ID不能为空");
        }

        ProjectTask projectTask = getById(updateTaskDTO.getId());
        if (projectTask == null) {
            throw BusinessException.of("任务不存在");
        }

        // 如果更新了任务编号，检查是否与其他任务重复
        if (updateTaskDTO.getTaskNo() != null && !updateTaskDTO.getTaskNo().equals(projectTask.getTaskNo())) {
            QueryWrapper<ProjectTask> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("task_no", updateTaskDTO.getTaskNo())
                    .eq("project_id", updateTaskDTO.getProjectId())
                    .ne("id", updateTaskDTO.getId());
            if (count(queryWrapper) > 0) {
                throw BusinessException.of("任务编号已存在");
            }
        }

        try {
            // 检查是否需要更新状态
            TaskStatusEnum newStatus = updateTaskDTO.getStatus();
            TaskStatusEnum oldStatus = projectTask.getStatus();
            boolean statusChanged = newStatus != null && !newStatus.equals(oldStatus);

            // 更新非状态字段
            BeanUtil.copyProperties(updateTaskDTO, projectTask, "id", "status");
            boolean updateResult = updateById(projectTask);
            if (!updateResult) {
                throw BusinessException.of("任务更新失败");
            }

            // 如果状态发生变更，使用updateTaskStatus处理状态流转
            if (statusChanged) {
                // 推导操作名
                String operation = deduceOperation(oldStatus, newStatus);
                if (operation != null) {
                    // 复用updateTaskStatus的逻辑进行状态流转
                    updateTaskStatus(updateTaskDTO.getId(), newStatus, "状态更新", operation);
                } else {
                    throw BusinessException.of("无法推导操作名进行状态流转: " + oldStatus + " -> " + newStatus);
                }
            }

            // 返回最新的任务状态
            ProjectTask updatedTask = getById(updateTaskDTO.getId());
            if (updatedTask == null) {
                throw BusinessException.of("获取更新后的任务失败");
            }
            return updatedTask;

        } catch (BusinessException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("更新任务失败: taskId={}", updateTaskDTO.getId(), e);
            throw BusinessException.of("更新任务失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTask(Long id) {
        if (id == null) {
            throw BusinessException.of("任务ID不能为空");
        }

        ProjectTask projectTask = getById(id);
        if (projectTask == null) {
            throw BusinessException.of("任务不存在");
        }

        // 删除相关的任务流程
        deleteTaskFlow(projectTask);

        // 删除任务
        removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTaskStatus(Long id, TaskStatusEnum status, String comment) {
        updateTaskStatus(id, status, comment, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTaskStatus(Long id, TaskStatusEnum status, String comment, String operation) {
        ProjectTask projectTask = getById(id);
        if (projectTask == null) {
            throw BusinessException.of("任务不存在");
        }

        // 如果没有提供operation，尝试推导
        if (operation == null) {
            TaskStatusEnum currentStatus = projectTask.getStatus();
            operation = deduceOperation(currentStatus, status);
            if (operation == null) {
                throw BusinessException.of("无法推导操作名进行状态流转: " + currentStatus + " -> " + status);
            }
        }

        // 状态由工作流listener自动更新，这里不设置状态
        // 触发工作流流转
        triggerTaskFlowWithOperation(projectTask, operation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignTask(Long id, String assignee) {
        ProjectTask projectTask = getById(id);
        if (projectTask == null) {
            throw BusinessException.of("任务不存在");
        }
        projectTask.setAssignee(assignee);
        updateById(projectTask);
    }

    @Override
    public Map<String, Object> getTaskStats(Long projectId) {
        Map<String, Object> stats = new HashMap<>();

        // 总任务数
        stats.put("total", count());

        // 按状态统计
        List<Map<String, Object>> statusStats = projectTaskMapper.countTasksByStatus(projectId);
        stats.put("byStatus", statusStats);

        // 按优先级统计
        List<Map<String, Object>> priorityStats = projectTaskMapper.countTasksByPriority(projectId);
        stats.put("byPriority", priorityStats);

        // 按问题类型统计
        List<Map<String, Object>> issueTypeStats = projectTaskMapper.countTasksByIssueType(projectId);
        stats.put("byIssueType", issueTypeStats);

        // 逾期任务数
        Long overdueCount = projectTaskMapper.countOverdueTasks(projectId);
        stats.put("overdue", overdueCount);

        // 已完成任务数
        Long completedCount = projectTaskMapper.countCompletedTasks(projectId);
        stats.put("completed", completedCount);

        return stats;
    }

    @Override
    public PageResult<ProjectTask> getMyTasks(String assignee, TaskQueryDTO queryDTO) {
        Page<ProjectTask> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
        IPage<ProjectTask> result = projectTaskMapper.selectUserTasks(page, assignee);
        return PageResult.from(result);
    }


    @Override
    public Resource generateTemplate() {

        TaskExcelGenerate taskExcelGenerate = new TaskExcelGenerate(this);
        return taskExcelGenerate.generateTemplate();
    }

    @Override
    public Resource exportTasks(TaskQueryDTO queryDTO) {
        TaskExcelGenerate taskExcelGenerate = new TaskExcelGenerate(this);
        return taskExcelGenerate.exportData(queryDTO);
    }

    /**
     * 导入任务（SSE方式，带项目名称）
     */
    public SseEmitter importTasks(MultipartFile file, String mode, Long projectId, String projectName) {
        TaskExcelGenerate taskExcelGenerate = new TaskExcelGenerate(this);
        return taskExcelGenerate.importDataWithSSE(file, mode, projectId, projectName);
    }


    /**
     * 删除任务流程
     */
    private void deleteTaskFlow(ProjectTask projectTask) {
        try {
            log.info("删除任务流程: taskId={}", projectTask.getId());

            // 查找该任务的所有流程实例（包括活跃和已完成的）
            String businessKey = "task_" + projectTask.getId();
            List<org.camunda.bpm.engine.runtime.ProcessInstance> activeProcessInstances =
                    runtimeService.createProcessInstanceQuery()
                            .processDefinitionKey("task_flow")
                            .processInstanceBusinessKey(businessKey)
                            .active()
                            .list();

            // 删除活跃的流程实例
            for (org.camunda.bpm.engine.runtime.ProcessInstance processInstance : activeProcessInstances) {
                log.info("删除活跃流程实例: taskId={}, processInstanceId={}",
                        projectTask.getId(), processInstance.getId());
                runtimeService.deleteProcessInstance(processInstance.getId(),
                        "任务被删除", false, false);
            }

            // 查找历史流程实例
            List<org.camunda.bpm.engine.history.HistoricProcessInstance> historicProcessInstances =
                    historyService.createHistoricProcessInstanceQuery()
                            .processDefinitionKey("task_flow")
                            .processInstanceBusinessKey(businessKey)
                            .list();

            // 删除历史流程实例
            for (org.camunda.bpm.engine.history.HistoricProcessInstance historicInstance : historicProcessInstances) {
                log.info("删除历史流程实例: taskId={}, processInstanceId={}",
                        projectTask.getId(), historicInstance.getId());
                historyService.deleteHistoricProcessInstance(historicInstance.getId());
            }

            log.info("任务流程删除成功: taskId={}", projectTask.getId());

        } catch (Exception e) {
            log.error("删除任务流程失败: taskId={}", projectTask.getId(), e);
            // 抛出异常，确保事务回滚
            throw new RuntimeException("删除任务流程失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, TaskStatusEnum> getTaskStatusTransitions(Long taskId) {
        ProjectTask projectTask = getById(taskId);
        if (projectTask == null) {
            throw BusinessException.of("任务不存在");
        }

        TaskStatusEnum currentStatus = projectTask.getStatus();
        return getStatusTransitions(currentStatus);
    }

    @Override
    public List<Map<String, Object>> getAvailableOperations(String status) {
        List<Map<String, Object>> operations = new ArrayList<>();

        if (!TaskStatusEnum.isValidCode(status)) {
            return operations;
        }

        TaskStatusEnum currentStatus = TaskStatusEnum.fromCode(status);

        // 根据当前状态返回可用操作
        switch (currentStatus) {
            case OPEN:
                operations.add(createOperationInfo("progress", "开始处理", TaskStatusEnum.IN_PROGRESS.getCode()));
                break;
            case IN_PROGRESS:
                operations.add(createOperationInfo("resolve", "解决", TaskStatusEnum.TO_VERIFY.getCode()));
                operations.add(createOperationInfo("close", "关闭", TaskStatusEnum.CLOSE.getCode()));
                break;
            case TO_VERIFY:
                operations.add(createOperationInfo("close", "关闭", TaskStatusEnum.CLOSE.getCode()));
                operations.add(createOperationInfo("reopen", "重新打开", TaskStatusEnum.OPEN.getCode()));
                break;
            case CLOSE:
                operations.add(createOperationInfo("reopen", "重新打开", TaskStatusEnum.OPEN.getCode()));
                break;
        }

        return operations;
    }

    @Override
    public String deduceOperation(TaskStatusEnum oldTaskStatusEnum, TaskStatusEnum newTaskStatusEnum) {
        if (oldTaskStatusEnum == null || newTaskStatusEnum == null || oldTaskStatusEnum.equals(newTaskStatusEnum)) {
            return null;
        }
        // 根据状态变化推导操作
        if (oldTaskStatusEnum == TaskStatusEnum.OPEN && newTaskStatusEnum == TaskStatusEnum.IN_PROGRESS) {
            return "progress";
        } else if ((oldTaskStatusEnum == TaskStatusEnum.OPEN || oldTaskStatusEnum == TaskStatusEnum.IN_PROGRESS) && newTaskStatusEnum == TaskStatusEnum.TO_VERIFY) {
            return "resolve";
        } else if ((oldTaskStatusEnum == TaskStatusEnum.IN_PROGRESS || oldTaskStatusEnum == TaskStatusEnum.TO_VERIFY)
                && newTaskStatusEnum == TaskStatusEnum.CLOSE) {
            return "close";
        } else if ((oldTaskStatusEnum == TaskStatusEnum.TO_VERIFY || oldTaskStatusEnum == TaskStatusEnum.CLOSE)
                && newTaskStatusEnum == TaskStatusEnum.OPEN) {
            return "reopen";
        }

        return null;
    }

    /**
     * 获取状态转换映射
     */
    private Map<String, TaskStatusEnum> getStatusTransitions(TaskStatusEnum status) {
        Map<String, TaskStatusEnum> transitions = new HashMap<>();

        switch (status) {
            case OPEN:
                transitions.put("progress", TaskStatusEnum.IN_PROGRESS);
                transitions.put("resolve", TaskStatusEnum.TO_VERIFY);
                transitions.put("close", TaskStatusEnum.CLOSE);
                break;
            case IN_PROGRESS:
                transitions.put("resolve", TaskStatusEnum.TO_VERIFY);
                transitions.put("close", TaskStatusEnum.CLOSE);
                transitions.put("reopen", TaskStatusEnum.OPEN);
                break;
            case TO_VERIFY:
                transitions.put("resolve", TaskStatusEnum.TO_VERIFY);
                transitions.put("close", TaskStatusEnum.CLOSE);
                transitions.put("reopen", TaskStatusEnum.OPEN);
                break;
            case CLOSE:
                transitions.put("reopen", TaskStatusEnum.OPEN);
                break;
        }

        return transitions;
    }

    /**
     * 创建操作信息
     */
    private Map<String, Object> createOperationInfo(String operation, String description, String targetStatus) {
        Map<String, Object> info = new HashMap<>();
        info.put("operation", operation);
        info.put("description", description);
        info.put("targetStatus", targetStatus);
        return info;
    }

    /**
     * 触发带操作名的任务流程流转
     */
    private void triggerTaskFlowWithOperation(ProjectTask projectTask, String operation) {
        try {
            // 如果没有提供operation，尝试推导
            if (operation == null) {
                throw BusinessException.of("操作名不能为空，无法推导工作流操作");
            }

            log.info("触发带操作名的任务流程流转: taskId={}, operation={}",
                    projectTask.getId(), operation);

            // 查找该任务的活跃流程实例
            String businessKey = "task_" + projectTask.getId();
            List<org.camunda.bpm.engine.runtime.ProcessInstance> processInstances =
                    runtimeService.createProcessInstanceQuery()
                            .processDefinitionKey("task_flow")
                            .processInstanceBusinessKey(businessKey)
                            .active()
                            .list();

            if (processInstances.isEmpty()) {
                log.warn("未找到任务的活跃流程实例: taskId={}", projectTask.getId());
                // 如果没有活跃流程实例，启动新的流程
                startTaskFlowWithOperation(projectTask, operation);
                return;
            }

            // 更新流程变量
            String processInstanceId = processInstances.get(0).getId();
            Map<String, Object> variables = new HashMap<>();
            variables.put("operation", operation);

            log.info("设置工作流变量: processInstanceId={}, operation={}", processInstanceId, operation);
            runtimeService.setVariables(processInstanceId, variables);

            // 完成当前的Camunda任务
            List<org.camunda.bpm.engine.task.Task> tasks =
                    camundaProjectTaskService.createTaskQuery()
                            .processInstanceId(processInstanceId)
                            .active()
                            .initializeFormKeys()
                            .list();

            for (org.camunda.bpm.engine.task.Task camundaTask : tasks) {
                // 调试日志：输出task的详细信息
                log.info("检查Camunda任务: taskId={}, camundaTaskId={}, taskName={}, formKey={}, taskDefinitionKey={}",
                        projectTask.getId(), camundaTask.getId(), camundaTask.getName(),
                        camundaTask.getFormKey(), camundaTask.getTaskDefinitionKey());

                // 检查是否是task-operation表单
                if ("task-operation".equals(camundaTask.getFormKey())) {
                    log.info("完成task-operation任务: taskId={}, camundaTaskId={}, operation={}",
                            projectTask.getId(), camundaTask.getId(), operation);

                    Map<String, Object> taskVariables = new HashMap<>(variables);
                    taskVariables.put("operation", operation);

                    camundaProjectTaskService.complete(camundaTask.getId(), taskVariables);
                } else {
                    log.warn("任务FormKey不匹配: 期望='task-operation', 实际='{}'", camundaTask.getFormKey());
                }
            }

            log.info("带操作名的任务流程流转成功: taskId={}, processInstanceId={}, operation={}",
                    projectTask.getId(), processInstanceId, operation);

        } catch (Exception e) {
            log.error("触发带操作名的任务流程流转失败: taskId={}, operation={}",
                    projectTask.getId(), operation, e);
            // 抛出异常，保证事务完整性
            throw BusinessException.of("工作流流转失败: " + e.getMessage(), e);
        }
    }

    /**
     * 启动带操作名的任务流程
     */
    private void startTaskFlowWithOperation(ProjectTask projectTask, String operation) {
        try {
            log.info("启动带操作名的任务流程: taskId={}, operation={}",
                    projectTask.getId(), operation);

            TaskStatusEnum currentStatus = projectTask.getStatus();

            // 根据当前状态和操作判断是否需要特殊处理
            if (!TaskStatusEnum.OPEN.equals(currentStatus)) {
                log.info("任务初始状态不是open: taskId={}, currentStatus={}, 需要执行状态流转",
                        projectTask.getId(), currentStatus);

                // 启动流程并执行状态流转
                startTaskFlowAndExecuteTransition(projectTask, operation);
            } else {
                // 当前状态是open，直接启动流程
                startTaskFlowDirectly(projectTask, operation);
            }

        } catch (Exception e) {
            log.error("启动带操作名的任务流程失败: taskId={}, operation={}",
                    projectTask.getId(), operation, e);
            throw e;
        }
    }

    /**
     * 直接启动任务流程
     */
    private void startTaskFlowDirectly(ProjectTask projectTask, String operation) {
        // 准备流程变量
        Map<String, Object> variables = new HashMap<>();
        variables.put("taskId", projectTask.getId());
        variables.put("taskNo", projectTask.getTaskNo());
        variables.put("title", projectTask.getTitle());
        variables.put("operation", operation);

        // 启动task_flow流程
        String processInstanceId = runtimeService.startProcessInstanceByKey("task_flow",
                "task_" + projectTask.getId(), variables).getId();

        log.info("任务流程启动成功: taskId={}, processInstanceId={}, operation={}",
                projectTask.getId(), processInstanceId, operation);
    }

    /**
     * 启动流程并执行状态流转
     */
    private void startTaskFlowAndExecuteTransition(ProjectTask projectTask, String operation) {
        // 先启动流程
        Map<String, Object> variables = new HashMap<>();
        variables.put("taskId", projectTask.getId());
        variables.put("taskNo", projectTask.getTaskNo());
        variables.put("title", projectTask.getTitle());
        variables.put("operation", operation);

        String processInstanceId = runtimeService.startProcessInstanceByKey("task_flow",
                "task_" + projectTask.getId(), variables).getId();

        log.info("任务流程启动成功: taskId={}, processInstanceId={}",
                projectTask.getId(), processInstanceId);

        // 然后执行状态流转
        variables.put("operation", operation);
        runtimeService.setVariables(processInstanceId, variables);

        // 完成当前的Camunda任务以触发流转
        List<org.camunda.bpm.engine.task.Task> tasks =
                camundaProjectTaskService.createTaskQuery()
                        .processInstanceId(processInstanceId)
                        .active()
                        .initializeFormKeys()
                        .list();

        for (org.camunda.bpm.engine.task.Task camundaTask : tasks) {
            // 调试日志：输出task的详细信息
            log.info("检查初始流转任务: taskId={}, camundaTaskId={}, taskName={}, formKey={}, taskDefinitionKey={}",
                    projectTask.getId(), camundaTask.getId(), camundaTask.getName(),
                    camundaTask.getFormKey(), camundaTask.getTaskDefinitionKey());

            if ("task-operation".equals(camundaTask.getFormKey())) {
                log.info("执行初始状态流转: taskId={}, camundaTaskId={}, operation={}",
                        projectTask.getId(), camundaTask.getId(), operation);

                Map<String, Object> taskVariables = new HashMap<>(variables);
                taskVariables.put("operation", operation);

                camundaProjectTaskService.complete(camundaTask.getId(), taskVariables);
            } else {
                log.warn("初始流转任务FormKey不匹配: 期望='task-operation', 实际='{}'", camundaTask.getFormKey());
            }
        }

        log.info("初始状态流转完成: taskId={}, operation={}",
                projectTask.getId(), operation);
    }

    /**
     * 启动新建任务的工作流
     */
    private void startTaskFlow(ProjectTask projectTask) {
        try {
            log.info("启动新建任务的工作流: taskId={}", projectTask.getId());

            // 准备流程变量（不包含operation，因为是外置表单）
            Map<String, Object> variables = new HashMap<>();
            variables.put("taskId", projectTask.getId());
            variables.put("taskNo", projectTask.getTaskNo());
            variables.put("title", projectTask.getTitle());

            // 启动task_flow流程
            String processInstanceId = runtimeService.startProcessInstanceByKey("task_flow",
                    "task_" + projectTask.getId(), variables).getId();

            log.info("新建任务工作流启动成功: taskId={}, processInstanceId={}, 等待外置表单操作",
                    projectTask.getId(), processInstanceId);

        } catch (Exception e) {
            log.error("启动新建任务工作流失败: taskId={}", projectTask.getId(), e);
            throw BusinessException.of("启动任务工作流失败: " + e.getMessage(), e);
        }
    }

    @Override
    public PageResult<ProjectTask> searchTasks(TaskQueryDTO queryDTO) {
        try {
            log.info("执行高级任务搜索: {}", queryDTO);

            // 创建分页对象
            Page<ProjectTask> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());

            // 执行搜索
            IPage<ProjectTask> result = projectTaskMapper.searchTasks(
                    page,
                    queryDTO.getKeyword(),
                    queryDTO.getProjectId(),
                    queryDTO.getStatus(),
                    queryDTO.getPriority(),
                    queryDTO.getStageName(),
                    queryDTO.getIssueType(),
                    queryDTO.getAssignee(),
                    queryDTO.getReporter(),
                    queryDTO.getTaskScope(),
                    queryDTO.getCurrentUser(),
                    queryDTO.getStatusList(),
                    queryDTO.getDateRangeStart(),
                    queryDTO.getDateRangeEnd(),
                    queryDTO.getWeekRangeStart(),
                    queryDTO.getWeekRangeEnd()
            );

            log.info("高级任务搜索完成: 总数={}, 当前页={}, 每页大小={}",
                    result.getTotal(), result.getCurrent(), result.getSize());

            return PageResult.from(result);

        } catch (Exception e) {
            log.error("高级任务搜索失败", e);
            throw BusinessException.of("搜索任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 自动生成任务编号
     * 格式：x.y，其中x为阶段的sortOrder，y为该阶段下任务编号的最大值+1
     *
     * @param projectId 项目ID
     * @param stageName 阶段名称
     * @return 生成的任务编号
     */
    private String generateTaskNo(Long projectId, String stageName) {
        try {
            // 默认sortOrder为1（如果没有找到阶段或阶段名称为空）
            Integer sortOrder = 1;

            // 如果提供了阶段名称和项目ID，查找对应的sortOrder
            if (stageName != null && !stageName.trim().isEmpty() && projectId != null) {
                List<ProjectProcess> stages = projectProcessService.getStagesByProjectId(projectId);
                for (ProjectProcess stage : stages) {
                    if (stageName.equals(stage.getStageName())) {
                        sortOrder = stage.getSortOrder();
                        break;
                    }
                }
            }

            // 查找该阶段下任务编号的最大y值
            QueryWrapper<ProjectTask> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("project_id", projectId)
                    .likeRight("task_no", sortOrder + ".")
                    .orderByDesc("task_no");

            List<ProjectTask> existingTasks = list(queryWrapper);

            int maxY = 0;
            String prefix = sortOrder + ".";

            for (ProjectTask task : existingTasks) {
                String taskNo = task.getTaskNo();
                if (taskNo != null && taskNo.startsWith(prefix)) {
                    try {
                        String yPart = taskNo.substring(prefix.length());
                        // 只取数字部分，忽略其他字符
                        String[] parts = yPart.split("\\D+");
                        if (parts.length > 0 && !parts[0].isEmpty()) {
                            int y = Integer.parseInt(parts[0]);
                            maxY = Math.max(maxY, y);
                        }
                    } catch (NumberFormatException e) {
                        // 忽略无法解析的任务编号
                        log.debug("无法解析任务编号: {}", taskNo);
                    }
                }
            }

            // 生成新的任务编号
            String newTaskNo = sortOrder + "." + (maxY + 1);
            log.info("生成任务编号: projectId={}, stageName={}, sortOrder={}, newTaskNo={}",
                    projectId, stageName, sortOrder, newTaskNo);

            return newTaskNo;

        } catch (Exception e) {
            log.error("生成任务编号失败: projectId={}, stageName={}", projectId, stageName, e);
            // 如果生成失败，返回一个基于时间戳的编号
            return "1." + System.currentTimeMillis() % 10000;
        }
    }
}