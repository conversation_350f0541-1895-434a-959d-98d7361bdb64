package com.vwatj.ppms.common;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 分页结果
 *
 * @param <T> 数据类型
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PageResult<T> {
    
    /**
     * 数据列表
     */
    private List<T> data;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 当前页码
     */
    private Long page;
    
    /**
     * 每页大小
     */
    private Long pageSize;
    
    /**
     * 总页数
     */
    private Long totalPages;
    
    public PageResult() {}
    
    public PageResult(List<T> data, Long total, Long page, Long pageSize) {
        this.data = data;
        this.total = total;
        this.page = page;
        this.pageSize = pageSize;
        this.totalPages = pageSize > 0 ? (total + pageSize - 1) / pageSize : 0;
    }
    
    /**
     * 从MyBatis-Plus的IPage转换
     */
    public static <T> PageResult<T> from(IPage<T> page) {
        return new PageResult<>(
            page.getRecords(),
            page.getTotal(),
            page.getCurrent(),
            page.getSize()
        );
    }
    
    /**
     * 空分页结果
     */
    public static <T> PageResult<T> empty() {
        return new PageResult<>(List.of(), 0L, 1L, 10L);
    }
}
