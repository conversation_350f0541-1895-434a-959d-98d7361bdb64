package com.vwatj.ppms.entity;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.vwatj.ppms.config.typehandler.JSONObjectTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 项目阶段实体类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("project_process")
public class ProjectProcess {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private Long projectId;

    /**
     * 阶段Key（如：requirement_collect, bbp, tab等）
     */
    @TableField("stage_key")
    private String stageKey;

    /**
     * 阶段名称
     */
    @TableField("stage_name")
    private String stageName;

    /**
     * 阶段描述
     */
    @TableField("description")
    private String description;

    /**
     * 阶段排序号
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 阶段进度（0-100）
     */
    @TableField("progress")
    private Integer progress;

    /**
     * 阶段状态：pending-待开始，active-进行中，completed-已完成，skipped-已跳过
     */
    @TableField("status")
    private String status;

    /**
     * 是否主要流程（true-主要流程，false-其他流程）
     */
    @TableField("is_main_process")
    private Boolean isMainProcess;

    /**
     * 阶段所处流程JSON配置
     * 包含该阶段的子流程表单配置，每个表单有状态标记
     * 格式：{fields: [{"formKey":"requirement_submit_form","formName":"需求提交表单","status":"pending","order":1}] , component: "DynamicForm"}
     */
    @TableField(value = "stage_process", typeHandler = JSONObjectTypeHandler.class)
    private JSONObject stageProcess;

    /**
     * 计划开始时间
     */
    @TableField("planned_start_time")
    private LocalDateTime plannedStartTime;

    /**
     * 计划结束时间
     */
    @TableField("planned_end_time")
    private LocalDateTime plannedEndTime;

    /**
     * 实际开始时间
     */
    @TableField("actual_start_time")
    private LocalDateTime actualStartTime;

    /**
     * 实际结束时间
     */
    @TableField("actual_end_time")
    private LocalDateTime actualEndTime;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;
}
