package com.vwatj.ppms.dto;

import lombok.Data;

/**
 * 用户查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
public class UserQueryDTO {
    
    /**
     * 页码
     */
    private Long page = 1L;
    
    /**
     * 每页大小
     */
    private Long pageSize = 10L;
    
    /**
     * 排序字段
     */
    private String sortBy;
    
    /**
     * 排序方向
     */
    private String sortOrder;
    
    /**
     * 关键词
     */
    private String keyword;
    
    /**
     * 角色
     */
    private String role;
    
    /**
     * 部门
     */
    private String department;
    
    /**
     * 是否激活
     */
    private Boolean isActive;
}
