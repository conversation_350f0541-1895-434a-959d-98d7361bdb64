package com.vwatj.ppms.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 时间戳到 LocalDateTime 的反序列化器
 * 支持多种时间格式：时间戳（毫秒）、ISO 字符串、自定义格式字符串
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
public class TimestampToLocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {

    private static final DateTimeFormatter[] FORMATTERS = {
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS"),
            DateTimeFormatter.ISO_LOCAL_DATE_TIME
    };

    @Override
    public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        // 首先尝试获取数值类型（时间戳）
        if (p.hasToken(com.fasterxml.jackson.core.JsonToken.VALUE_NUMBER_INT)) {
            long timestamp = p.getLongValue();
            return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
        }

        // 然后尝试字符串类型
        String value = p.getValueAsString();

        if (value == null || value.trim().isEmpty()) {
            return null;
        }

        // 尝试解析为时间戳（毫秒）
        try {
            long timestamp = Long.parseLong(value);
            return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
        } catch (NumberFormatException e) {
            // 不是时间戳，继续尝试其他格式
        }

        // 尝试解析为各种日期时间格式
        for (DateTimeFormatter formatter : FORMATTERS) {
            try {
                return LocalDateTime.parse(value, formatter);
            } catch (Exception e) {
                // 继续尝试下一个格式
            }
        }

        // 如果所有格式都失败，抛出异常
        throw new IOException("无法解析日期时间: " + value + ", 支持的格式: 时间戳(毫秒)、ISO格式、yyyy-MM-dd HH:mm:ss等");
    }
}
