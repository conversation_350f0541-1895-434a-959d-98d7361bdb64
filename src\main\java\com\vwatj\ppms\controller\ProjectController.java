package com.vwatj.ppms.controller;

import com.vwatj.ppms.common.ApiResponse;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateProjectDTO;
import com.vwatj.ppms.dto.ProjectQueryDTO;
import com.vwatj.ppms.dto.TaskQueryDTO;
import com.vwatj.ppms.dto.UpdateProjectDTO;
import com.vwatj.ppms.entity.Project;
import com.vwatj.ppms.entity.ProjectTask;
import com.vwatj.ppms.service.ProjectService;
import com.vwatj.ppms.service.ProjectTaskService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目控制器
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@RestController
@RequestMapping("/projects")
@RequiredArgsConstructor
public class ProjectController {

    private final ProjectService projectService;
    private final ProjectTaskService projectTaskService;
    
    /**
     * 分页查询项目
     */
    @GetMapping
    public ApiResponse<PageResult<Project>> getProjects(ProjectQueryDTO queryDTO) {
        PageResult<Project> result = projectService.getProjectPage(queryDTO);
        return ApiResponse.success(result);
    }
    
    /**
     * 根据ID查询项目
     */
    @GetMapping("/{id}")
    public ApiResponse<Project> getProject(@PathVariable Long id) {
        Project project = projectService.getById(id);
        if (project == null) {
            return ApiResponse.notFound("项目不存在");
        }
        return ApiResponse.success(project);
    }
    
    /**
     * 创建项目
     */
    @PostMapping
    public ApiResponse<Project> createProject(@Validated @RequestBody CreateProjectDTO createProjectDTO) {
        try {
            Project project = projectService.createProject(createProjectDTO);
            return ApiResponse.success("项目创建成功", project);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 更新项目
     */
    @PutMapping("/{id}")
    public ApiResponse<Project> updateProject(@PathVariable Long id, @Validated @RequestBody UpdateProjectDTO updateProjectDTO) {
        try {
            updateProjectDTO.setId(id);
            Project project = projectService.updateProject(updateProjectDTO);
            return ApiResponse.success("项目更新成功", project);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 删除项目
     */
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteProject(@PathVariable Long id) {
        try {
            projectService.deleteProject(id);
            return ApiResponse.success("项目删除成功");
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 切换项目关注状态
     */
    @PatchMapping("/{id}/star")
    public ApiResponse<Map<String, Object>> toggleStar(@PathVariable Long id) {
        try {
            boolean starred = projectService.toggleStar(id);
            Map<String, Object> result = new HashMap<>();
            result.put("starred", starred);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 归档/取消归档项目
     */
    @PatchMapping("/{id}/archive")
    public ApiResponse<Map<String, Object>> toggleArchive(@PathVariable Long id) {
        try {
            boolean archived = projectService.toggleArchive(id);
            Map<String, Object> result = new HashMap<>();
            result.put("archived", archived);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 更新项目状态
     */
    @PatchMapping("/{id}/status")
    public ApiResponse<String> updateStatus(@PathVariable Long id, @RequestBody UpdateStatusRequest request) {
        try {
            projectService.updateStatus(id, request.getStatus());
            return ApiResponse.success("项目状态更新成功");
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 获取项目统计信息
     */
    @GetMapping("/stats")
    public ApiResponse<Map<String, Object>> getProjectStats() {
        Map<String, Object> stats = projectService.getProjectStats();
        return ApiResponse.success(stats);
    }

    /**
     * 获取项目详情概览信息
     */
    @GetMapping("/{id}/overview")
    public ApiResponse<Map<String, Object>> getProjectOverview(@PathVariable Long id) {
        try {
            Map<String, Object> overview = projectService.getProjectOverview(id);
            return ApiResponse.success(overview);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 获取项目任务统计
     */
    @GetMapping("/{id}/task-stats")
    public ApiResponse<Map<String, Object>> getProjectTaskStats(@PathVariable Long id) {
        try {
            Map<String, Object> taskStats = projectService.getProjectTaskStats(id);
            return ApiResponse.success(taskStats);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 获取项目任务列表
     */
    @GetMapping("/{id}/tasks")
    public ApiResponse<PageResult<ProjectTask>> getProjectTasks(
            @PathVariable Long id,
            @RequestParam(defaultValue = "1") Long page,
            @RequestParam(defaultValue = "20") Long pageSize) {
        try {
            TaskQueryDTO queryDTO = new TaskQueryDTO();
            queryDTO.setPage(page);
            queryDTO.setPageSize(pageSize);
            queryDTO.setProjectId(id);

            PageResult<ProjectTask> tasks = projectTaskService.getProjectTasks(id, queryDTO);
            return ApiResponse.success(tasks);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 获取项目文档列表
     */
    @GetMapping("/{id}/documents")
    public ApiResponse<List<Map<String, Object>>> getProjectDocuments(@PathVariable Long id) {
        try {
            List<Map<String, Object>> documents = projectService.getProjectDocuments(id);
            return ApiResponse.success(documents);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    
    /**
     * 获取项目成员列表
     */
    @GetMapping("/{id}/members")
    public ApiResponse<List<String>> getProjectMembers(@PathVariable Long id) {
        try {
            List<String> members = projectService.getProjectMembers(id);
            return ApiResponse.success(members);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 更新项目成员
     */
    @PutMapping("/{id}/members")
    public ApiResponse<String> updateProjectMembers(@PathVariable Long id, @RequestBody UpdateMembersRequest request) {
        try {
            projectService.updateProjectMembers(id, request.getMembers());
            return ApiResponse.success("项目成员更新成功");
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 复制项目
     */
    @PostMapping("/{id}/duplicate")
    public ApiResponse<Project> duplicateProject(@PathVariable Long id, @RequestBody DuplicateProjectRequest request) {
        try {
            Project project = projectService.duplicateProject(id, request.getName());
            return ApiResponse.success("项目复制成功", project);
        } catch (Exception e) {
            return ApiResponse.badRequest(e.getMessage());
        }
    }
    
    /**
     * 更新状态请求
     */
    public static class UpdateStatusRequest {
        private String status;
        
        public String getStatus() {
            return status;
        }
        
        public void setStatus(String status) {
            this.status = status;
        }
    }
    
    /**
     * 更新成员请求
     */
    public static class UpdateMembersRequest {
        private List<String> members;
        
        public List<String> getMembers() {
            return members;
        }
        
        public void setMembers(List<String> members) {
            this.members = members;
        }
    }
    
    /**
     * 复制项目请求
     */
    public static class DuplicateProjectRequest {
        private String name;
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
    }
}
