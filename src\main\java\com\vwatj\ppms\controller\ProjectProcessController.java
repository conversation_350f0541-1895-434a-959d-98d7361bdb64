package com.vwatj.ppms.controller;

import com.alibaba.fastjson2.JSONObject;
import com.vwatj.ppms.common.ApiResponse;
import com.vwatj.ppms.entity.ProjectProcess;
import com.vwatj.ppms.service.ProjectProcessService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目流程控制器
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Slf4j
@RestController
@RequestMapping("/project-process")
@RequiredArgsConstructor
public class ProjectProcessController {

    private final ProjectProcessService projectProcessService;

    /**
     * 根据项目ID获取所有流程阶段
     */
    @GetMapping("/project/{projectId}")
    public ApiResponse<List<ProjectProcess>> getStagesByProjectId(@PathVariable Long projectId) {
        try {
            List<ProjectProcess> stages = projectProcessService.getStagesByProjectId(projectId);
            return ApiResponse.success(stages);
        } catch (Exception e) {
            log.error("获取项目流程阶段失败: projectId={}", projectId, e);
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 提交流程表单数据
     */
    @PostMapping("/project/{projectId}/stage/{stageKey}/form/{formKey}/submit")
    public ApiResponse<String> submitStageForm(
            @PathVariable Long projectId,
            @PathVariable String stageKey,
            @PathVariable String formKey,
            @RequestBody JSONObject formData) {
        try {
            projectProcessService.submitStageForm(projectId, stageKey, formKey, formData);
            return ApiResponse.success("表单提交成功");
        } catch (Exception e) {
            log.error("提交流程表单失败: projectId={}, stageKey={}, formKey={}",
                    projectId, stageKey, formKey, e);
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 编辑流程表单数据
     */
    @PutMapping("/project/{projectId}/stage/{stageKey}/form/{formKey}/data")
    public ApiResponse<String> updateStageFormData(
            @PathVariable Long projectId,
            @PathVariable String stageKey,
            @PathVariable String formKey,
            @RequestBody JSONObject formData) {
        try {
            projectProcessService.updateStageFormData(projectId, stageKey, formKey, formData);
            return ApiResponse.success("表单数据更新成功");
        } catch (Exception e) {
            log.error("更新流程表单数据失败: projectId={}, stageKey={}, formKey={}",
                    projectId, stageKey, formKey, e);
            return ApiResponse.badRequest(e.getMessage());
        }
    }

    /**
     * 获取流程表单数据
     */
    @GetMapping("/project/{projectId}/stage/{stageKey}/form/{formKey}/data")
    public ApiResponse<Map<String, Object>> getStageFormData(
            @PathVariable Long projectId,
            @PathVariable String stageKey,
            @PathVariable String formKey) {
        try {
            Map<String, Object> formData = projectProcessService.getStageFormData(projectId, stageKey, formKey);
            return ApiResponse.success(formData);
        } catch (Exception e) {
            log.error("获取流程表单数据失败: projectId={}, stageKey={}, formKey={}",
                    projectId, stageKey, formKey, e);
            return ApiResponse.badRequest(e.getMessage());
        }
    }
}
