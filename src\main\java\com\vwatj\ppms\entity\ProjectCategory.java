package com.vwatj.ppms.entity;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 项目分类实体
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("project_category")
public class ProjectCategory {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 分类代码
     */
    @TableField("code")
    private String code;

    /**
     * 分类名称
     */
    @TableField("name")
    private String name;

    /**
     * 分类描述
     */
    @TableField("description")
    private String description;

    /**
     * 排序号
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 是否启用（1-启用，0-禁用）
     */
    @TableField("enabled")
    private Boolean enabled;

    /**
     * 流程模板JSON配置
     * 包含阶段配置：排序号、名称、描述、是否主要流程、表单配置等
     */
    @TableField("flow_template")
    private JSONObject flowTemplate;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 是否删除（1-已删除，0-未删除）
     */
    @TableLogic
    @TableField("deleted")
    private Boolean deleted;
}
