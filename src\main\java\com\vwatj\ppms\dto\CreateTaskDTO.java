package com.vwatj.ppms.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.vwatj.ppms.config.TimestampToLocalDateTimeDeserializer;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 创建任务DTO
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
public class CreateTaskDTO {

    /**
     * 任务编号（可选，如果为空则自动生成）
     */
    private String taskNo;

    /**
     * 任务标题
     */
    @NotBlank(message = "任务标题不能为空")
    private String title;
    
    /**
     * 任务描述
     */
    private String description;
    
    /**
     * 问题类型
     */
    @NotBlank(message = "问题类型不能为空")
    private String issueType;
    
    /**
     * 指派人
     */
    private String assignee;
    
    /**
     * 报告人
     */
    private String reporter;
    
    /**
     * 任务状态
     */
    private String status;
    
    /**
     * 任务优先级
     */
    @NotBlank(message = "任务优先级不能为空")
    private String priority;
    
    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 阶段名称
     */
    private String stageName;

    /**
     * 计划开始时间
     */
    @JsonDeserialize(using = TimestampToLocalDateTimeDeserializer.class)
    private LocalDateTime plannedStartTime;

    /**
     * 计划结束时间
     */
    @JsonDeserialize(using = TimestampToLocalDateTimeDeserializer.class)
    private LocalDateTime plannedEndTime;

    /**
     * 时长(小时)
     */
    private BigDecimal duration;
}
