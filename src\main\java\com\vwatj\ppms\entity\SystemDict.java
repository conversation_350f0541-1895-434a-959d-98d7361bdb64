package com.vwatj.ppms.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 系统字典实体
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("system_dict")
public class SystemDict {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 字典类型
     */
    @TableField("dict_type")
    private String dictType;

    /**
     * 字典代码
     */
    @TableField("dict_code")
    private String dictCode;

    /**
     * 字典名称
     */
    @TableField("dict_name")
    private String dictName;

    /**
     * 字典描述
     */
    @TableField("description")
    private String description;

    /**
     * 排序号
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 是否启用（1-启用，0-禁用）
     */
    @TableField("enabled")
    private Boolean enabled;

    /**
     * 父级ID（用于层级结构）
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 扩展属性（JSON格式）
     */
    @TableField("extra_props")
    private String extraProps;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 是否删除（1-已删除，0-未删除）
     */
    @TableLogic
    @TableField("deleted")
    private Boolean deleted;
}
