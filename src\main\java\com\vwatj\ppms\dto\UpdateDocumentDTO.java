package com.vwatj.ppms.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.util.List;

/**
 * 更新文档DTO
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
public class UpdateDocumentDTO {
    
    /**
     * 文档ID
     */
    private String id;
    
    /**
     * 文档标题
     */
    private String title;
    
    /**
     * 文档描述
     */
    private String description;
    
    /**
     * 文档类别
     */
    private String category;
    
    /**
     * 标签列表
     */
    private List<String> tags;
    
    /**
     * 是否公开
     */
    private Boolean isPublic;
    
    /**
     * 查看者ID列表
     */
    private List<String> viewers;
    
    /**
     * 编辑者ID列表
     */
    private List<String> editors;
}
