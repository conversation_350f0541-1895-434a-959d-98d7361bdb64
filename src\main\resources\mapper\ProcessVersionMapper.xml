<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vwatj.ppms.mapper.ProcessVersionMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.vwatj.ppms.entity.ProcessVersion">
        <id column="id" property="id" />
        <result column="process_definition_id" property="processDefinitionId" />
        <result column="version" property="version" />
        <result column="description" property="description" />
        <result column="file_name" property="fileName" />
        <result column="file_path" property="filePath" />
        <result column="file_size" property="fileSize" />
        <result column="deployment_id" property="deploymentId" />
        <result column="camunda_process_definition_id" property="camundaProcessDefinitionId" />
        <result column="status" property="status" />
        <result column="is_published" property="isPublished" />
        <result column="publish_time" property="publishTime" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, process_definition_id, version, description, file_name, file_path, file_size,
        deployment_id, camunda_process_definition_id, status, is_published, publish_time,
        created_by, created_time, updated_by, updated_time
    </sql>

    <!-- 分页查询流程版本 -->
    <select id="selectProcessVersionPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM process_version
        <where>
        <if test="processDefinitionId != null">
            AND process_definition_id = #{processDefinitionId}
        </if>
        <if test="version != null">
            AND version = #{version}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="createdBy != null and createdBy != ''">
            AND created_by = #{createdBy}
        </if>
        </where>
        ORDER BY version DESC
    </select>

    <!-- 根据流程定义ID查询最大版本号 -->
    <select id="selectMaxVersionByProcessDefinitionId" resultType="java.lang.Integer">
        SELECT MAX(version)
        FROM process_version
        WHERE process_definition_id = #{processDefinitionId}
    </select>

    <!-- 根据流程定义ID和版本号查询 -->
    <select id="selectByProcessDefinitionIdAndVersion" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM process_version
        WHERE process_definition_id = #{processDefinitionId}
        AND version = #{version}
    </select>

    <!-- 根据流程定义ID查询当前发布版本 -->
    <select id="selectPublishedVersionByProcessDefinitionId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM process_version
        WHERE process_definition_id = #{processDefinitionId}
        AND is_published = 1
        LIMIT 1
    </select>

    <!-- 更新发布状态 -->
    <update id="updatePublishStatus">
        UPDATE process_version 
        SET is_published = #{isPublished},
            updated_time = NOW()
        WHERE process_definition_id = #{processDefinitionId}
        <if test="version != null">
            AND version = #{version}
        </if>
    </update>

</mapper>
