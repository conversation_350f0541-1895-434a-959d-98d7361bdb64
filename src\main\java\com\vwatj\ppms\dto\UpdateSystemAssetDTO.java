package com.vwatj.ppms.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.Map;

/**
 * 更新系统资产DTO
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
public class UpdateSystemAssetDTO {
    
    /**
     * 系统资产ID
     */
    private Long id;
    
    /**
     * 资产编号
     */
    private String assetNo;
    
    /**
     * 项目名称
     */
    private String projectName;
    
    /**
     * 短代码
     */
    private String shortCode;
    
    /**
     * CI名称
     */
    private String ciName;

    /**
     * 业务部门
     */
    private String businessDepartment;

    /**
     * PM负责人
     */
    private String pmOwner;

    /**
     * 描述
     */
    private String description;

    /**
     * 资产状态
     */
    private String assetState;

    /**
     * 区域
     */
    private String region;

    /**
     * 退役日期
     */
    private LocalDate retireDate;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 上线日期
     */
    private LocalDate onlineDate;

    /**
     * 邮箱
     */
    private String mail;

    /**
     * 版本
     */
    private String version;

    /**
     * 语言
     */
    private String language;

    /**
     * 用户数量
     */
    private Integer userCount;

    /**
     * 使用状态
     */
    private String usageStatus;

    /**
     * 业务用户
     */
    private String businessUser;

    /**
     * 额外属性
     */
    private Map<String, Object> extraProperties;
}
