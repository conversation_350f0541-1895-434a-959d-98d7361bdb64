package com.vwatj.ppms.excel.generate;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.vwatj.ppms.dto.TaskQueryDTO;
import com.vwatj.ppms.entity.ProjectTask;
import com.vwatj.ppms.excel.converter.ExcelDataConverter;
import com.vwatj.ppms.excel.converter.impl.ProjectTaskExcelConverter;
import com.vwatj.ppms.excel.core.AbstractExcelGenerate;
import com.vwatj.ppms.excel.core.ExcelImportResult;
import com.vwatj.ppms.service.ProjectTaskService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务Excel服务
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public class TaskExcelGenerate extends AbstractExcelGenerate<ProjectTask> {

    private final ProjectTaskService projectTaskService;

    public TaskExcelGenerate(ProjectTaskService projectTaskService) {
        this.projectTaskService = projectTaskService;
    }

    @Override
    protected ExcelDataConverter<ProjectTask> getDataConverter() {
        return new ProjectTaskExcelConverter();
    }

    @Override
    protected ProjectTask findByUniqueField(Object uniqueValue) {
        QueryWrapper<ProjectTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_no", uniqueValue);
        return projectTaskService.getOne(queryWrapper);
    }

    @Override
    protected ProjectTask findByUniqueFieldWithParams(Object uniqueValue, Map<String, Object> additionalParams) {
        QueryWrapper<ProjectTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_no", uniqueValue);

        // 如果提供了项目ID，则基于TaskNo+ProjectId组合判断
        Long projectId = (Long) additionalParams.get("projectId");
        if (projectId != null) {
            queryWrapper.eq("project_id", projectId);
        }

        return projectTaskService.getOne(queryWrapper);
    }

    @Override
    protected ProjectTask saveEntity(ProjectTask entity) {
        projectTaskService.save(entity);
        return entity;
    }

    @Override
    protected ProjectTask updateEntity(ProjectTask existingEntity, ProjectTask newEntity) {
        newEntity.setId(existingEntity.getId());
        newEntity.setCreatedAt(existingEntity.getCreatedAt());
        projectTaskService.updateById(newEntity);
        return newEntity;
    }

    @Override
    protected List<ProjectTask> queryDataList(Object queryParams) {
        if (queryParams instanceof TaskQueryDTO) {
            TaskQueryDTO queryDTO = (TaskQueryDTO) queryParams;
            // 设置一个较大的页面大小来获取所有数据
            queryDTO.setPageSize(10000L);
            return projectTaskService.getTaskPage(queryDTO).getData();
        } else {
            // 如果没有查询参数，返回所有任务
            return projectTaskService.list();
        }
    }

    @Override
    protected void setEntityProjectId(ProjectTask entity, Long projectId, String projectName) {
        if (projectId != null) {
            entity.setProjectId(projectId);
            entity.setProjectName(projectName);
        }
    }

    /**
     * 将导入结果转换为Map格式
     */
    public Map<String, Object> convertImportResult(Object result) {
        if (result instanceof ExcelImportResult excelResult) {
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("total", excelResult.getTotal());
            resultMap.put("success", excelResult.getSuccess());
            resultMap.put("failed", excelResult.getFailed());
            resultMap.put("skipped", excelResult.getSkipped());

            // 转换错误信息
            List<Map<String, Object>> errors = excelResult.getErrors().stream()
                    .map(error -> {
                        Map<String, Object> errorMap = new HashMap<>();
                        errorMap.put("row", error.getRow());
                        errorMap.put("field", error.getField());
                        errorMap.put("message", error.getMessage());
                        return errorMap;
                    })
                    .toList();
            resultMap.put("errors", errors);

            return resultMap;
        }

        return new HashMap<>();
    }
}
