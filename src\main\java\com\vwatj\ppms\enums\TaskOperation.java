package com.vwatj.ppms.enums;

/**
 * 任务操作枚举
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
public enum TaskOperation {
    
    /**
     * 关闭操作 - 将任务标记为已完成
     */
    CLOSE("close", "关闭", TaskStatusEnum.CLOSE),
    
    /**
     * 重新打开操作 - 将任务重新设置为待办
     */
    REOPEN("reopen", "重新打开", TaskStatusEnum.OPEN),
    
    /**
     * 解决操作 - 将任务标记为待验证
     */
    RESOLVE("resolve", "解决", TaskStatusEnum.TO_VERIFY),
    
    /**
     * 进行中操作 - 将任务标记为进行中
     */
    PROGRESS("progress", "进行中", TaskStatusEnum.IN_PROGRESS);

    private final String code;
    private final String description;
    private final TaskStatusEnum targetStatus;

    TaskOperation(String code, String description, TaskStatusEnum targetStatus) {
        this.code = code;
        this.description = description;
        this.targetStatus = targetStatus;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public TaskStatusEnum getTargetStatus() {
        return targetStatus;
    }

    /**
     * 根据代码获取枚举
     */
    public static TaskOperation fromCode(String code) {
        for (TaskOperation operation : TaskOperation.values()) {
            if (operation.getCode().equals(code)) {
                return operation;
            }
        }
        throw new IllegalArgumentException("Unknown task operation code: " + code);
    }
    
    /**
     * 验证操作代码是否有效
     */
    public static boolean isValidCode(String code) {
        try {
            fromCode(code);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
