package com.vwatj.ppms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateUserDTO;
import com.vwatj.ppms.dto.UpdateUserDTO;
import com.vwatj.ppms.dto.UserQueryDTO;
import com.vwatj.ppms.entity.User;

import java.util.List;
import java.util.Map;

/**
 * 用户服务接口
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface UserService extends IService<User> {
    
    /**
     * 分页查询用户
     */
    PageResult<User> getUserPage(UserQueryDTO queryDTO);
    
    /**
     * 根据用户名查询用户
     */
    User getUserByUsername(String username);
    
    /**
     * 根据邮箱查询用户
     */
    User getUserByEmail(String email);
    
    /**
     * 创建用户
     */
    User createUser(CreateUserDTO createUserDTO);
    
    /**
     * 更新用户
     */
    User updateUser(UpdateUserDTO updateUserDTO);
    
    /**
     * 删除用户
     */
    void deleteUser(String id);
    
    /**
     * 启用/禁用用户
     */
    void toggleUserStatus(String id);
    
    /**
     * 重置用户密码
     */
    void resetPassword(String userId, String newPassword);
    
    /**
     * 修改密码
     */
    void changePassword(String userId, String oldPassword, String newPassword);

    /**
     * 获取用户选项列表（用于下拉选择）
     */
    List<Map<String, String>> getUserOptions();
}
