package com.vwatj.ppms.dto;

import lombok.Data;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 创建用户DTO
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
public class CreateUserDTO {
    
    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    private String username;
    
    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;
    
    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
    private String password;
    
    /**
     * 全名
     */
    @NotBlank(message = "全名不能为空")
    private String fullName;
    
    /**
     * 角色
     */
    @NotBlank(message = "角色不能为空")
    private String role;
    
    /**
     * 部门
     */
    private String department;
    
    /**
     * 职位
     */
    private String position;
    
    /**
     * 电话
     */
    private String phone;
    
    /**
     * 头像
     */
    private String avatar;
}
