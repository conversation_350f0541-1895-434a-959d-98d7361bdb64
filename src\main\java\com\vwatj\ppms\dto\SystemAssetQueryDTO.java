package com.vwatj.ppms.dto;

import lombok.Data;

/**
 * 系统资产查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
public class SystemAssetQueryDTO {
    
    /**
     * 页码
     */
    private Long page = 1L;
    
    /**
     * 每页大小
     */
    private Long pageSize = 10L;
    
    /**
     * 排序字段
     */
    private String sortBy;
    
    /**
     * 排序方向
     */
    private String sortOrder;
    
    /**
     * 关键词
     */
    private String keyword;
    
    /**
     * 区域
     */
    private String region;

    /**
     * 业务部门
     */
    private String businessDepartment;
    
    /**
     * 资产状态
     */
    private String assetState;
    
    /**
     * PM负责人
     */
    private String pmOwner;
}
