package com.vwatj.ppms.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 任务状态枚举
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Getter
public enum TaskStatusEnum implements IEnum<String> {

    /**
     * 待办状态
     */
    OPEN("open", "待办"),

    /**
     * 进行中状态
     */
    IN_PROGRESS("inprogress", "进行中"),

    /**
     * 待验证状态
     */
    TO_VERIFY("toverify", "待验证"),

    /**
     * 已完成状态
     */
    CLOSE("close", "已关闭");

    private final String code;
    private final String description;

    TaskStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据名称获取枚举
     */
    public static TaskStatusEnum fromDescription(String description) {
        for (TaskStatusEnum status : TaskStatusEnum.values()) {
            if (status.getDescription().equals(description)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown task status description: " + description);
    }

    /**
     * 根据代码获取枚举
     */
    public static TaskStatusEnum fromCode(String code) {
        for (TaskStatusEnum status : TaskStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown task status code: " + code);
    }

    /**
     * 验证状态代码是否有效
     */
    public static boolean isValidCode(String code) {
        try {
            fromCode(code);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    @Override
    @JsonValue
    public String getValue() {
        return code;
    }
}
