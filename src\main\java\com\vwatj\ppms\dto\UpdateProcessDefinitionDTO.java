package com.vwatj.ppms.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotBlank;

/**
 * 更新流程定义DTO
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
public class UpdateProcessDefinitionDTO {

    /**
     * 流程ID
     */
    private Long id;

    /**
     * 流程名称
     */
    @NotBlank(message = "流程名称不能为空")
    private String name;

    /**
     * 流程描述
     */
    private String description;

    /**
     * 新版本BPMN文件（可选）
     */
    private MultipartFile bpmnFile;

    /**
     * 是否立即激活新版本
     */
    private Boolean activate = true;
}
