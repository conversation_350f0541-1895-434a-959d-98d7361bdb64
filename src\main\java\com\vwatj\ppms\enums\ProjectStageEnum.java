package com.vwatj.ppms.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 项目阶段枚举
 * 统一管理项目状态和阶段key
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Getter
public enum ProjectStageEnum implements IEnum<String> {

    /**
     * 需求收集阶段
     */
    REQUIREMENT_COLLECT("requirement_collect", "需求收集"),

    /**
     * BBP阶段
     */
    BBP("bbp", "BBP"),

    /**
     * TAB阶段
     */
    TAB("tab", "TAB"),

    /**
     * 开发阶段
     */
    DEVELOPMENT("development", "开发"),

    /**
     * 内部测试阶段
     */
    INNER_TEST("inner_test", "内部测试"),

    /**
     * UAT阶段
     */
    UAT("uat", "UAT"),

    /**
     * CAB阶段
     */
    CAB("cab", "CAB"),

    /**
     * 发布阶段
     */
    RELEASE("release", "发布"),

    /**
     * 计划阶段
     */
    PLANNING("planning", "计划"),

    /**
     * 周报阶段
     */
    WEEK_REPORT("week_report", "周报"),

    /**
     * 安全检查阶段
     */
    SECURITY_CHECK("security_check", "安全检查");

    @JsonValue
    private final String code;
    private final String description;

    ProjectStageEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 阶段代码
     * @return 项目阶段枚举，如果没有找到则返回null
     */
    public static ProjectStageEnum fromCode(String code) {
        if (code == null) {
            return null;
        }

        for (ProjectStageEnum stage : ProjectStageEnum.values()) {
            if (stage.getCode().equals(code)) {
                return stage;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     *
     * @param code 阶段代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return fromCode(code) != null;
    }

    /**
     * 获取所有阶段代码
     *
     * @return 阶段代码数组
     */
    public static String[] getAllCodes() {
        ProjectStageEnum[] stages = ProjectStageEnum.values();
        String[] codes = new String[stages.length];
        for (int i = 0; i < stages.length; i++) {
            codes[i] = stages[i].getCode();
        }
        return codes;
    }

    @Override
    public String getValue() {
        return code;
    }
}
