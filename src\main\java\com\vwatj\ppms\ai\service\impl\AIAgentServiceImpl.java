package com.vwatj.ppms.ai.service.impl;

import com.vwatj.ppms.ai.dto.ChatRequest;
import com.vwatj.ppms.ai.dto.ChatResponse;
import com.vwatj.ppms.ai.function.ProjectManagementFunctions;
import com.vwatj.ppms.ai.function.TaskManagementFunctions;
import com.vwatj.ppms.ai.service.AIAgentService;
import com.vwatj.ppms.entity.Project;
import com.vwatj.ppms.service.ProjectService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI Agent服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AIAgentServiceImpl implements AIAgentService {

    private final ChatClient chatClient;
    private final ProjectService projectService;
    private final ProjectManagementFunctions projectFunctions;
    private final TaskManagementFunctions taskFunctions;

    @Override
    public com.vwatj.ppms.ai.dto.ChatResponse chat(com.vwatj.ppms.ai.dto.ChatRequest request) {
        try {
            log.info("处理AI聊天请求: sessionId={}, userId={}, message={}", 
                    request.getSessionId(), request.getUserId(), request.getMessage());

            // 构建上下文信息
            String contextInfo = buildContextInfo(request);
            
            // 构建完整的提示词
            String fullPrompt = buildFullPrompt(request.getMessage(), contextInfo, request);

            // 调用AI模型
            String aiResponse = chatClient.prompt()
                    .user(fullPrompt)
                    .call()
                    .content();

            // 构建响应
            com.vwatj.ppms.ai.dto.ChatResponse response = new com.vwatj.ppms.ai.dto.ChatResponse();
            response.setMessage(aiResponse);
            response.setSessionId(request.getSessionId());
            response.setTimestamp(System.currentTimeMillis());

            // 检查是否需要调用业务功能
            if (request.getEnableFunctionCalling() && needsFunctionCall(request.getMessage())) {
                handleFunctionCalls(request, response);
            }

            // 添加建议操作
            addSuggestedActions(request, response);

            log.info("AI聊天响应生成成功: sessionId={}", request.getSessionId());
            return response;

        } catch (Exception e) {
            log.error("处理AI聊天请求失败", e);
            com.vwatj.ppms.ai.dto.ChatResponse errorResponse = new com.vwatj.ppms.ai.dto.ChatResponse();
            errorResponse.setMessage("抱歉，处理您的请求时遇到了问题，请稍后再试。");
            errorResponse.setError(e.getMessage());
            errorResponse.setTimestamp(System.currentTimeMillis());
            return errorResponse;
        }
    }

    @Override
    public String streamChat(com.vwatj.ppms.ai.dto.ChatRequest request) {
        try {
            String contextInfo = buildContextInfo(request);
            String fullPrompt = buildFullPrompt(request.getMessage(), contextInfo, request);

            return chatClient.prompt()
                    .user(fullPrompt)
                    .stream()
                    .content()
                    .collectList()
                    .block()
                    .stream()
                    .reduce("", String::concat);

        } catch (Exception e) {
            log.error("流式聊天处理失败", e);
            return "抱歉，处理您的请求时遇到了问题。";
        }
    }

    @Override
    public String analyzeProject(Long projectId) {
        try {
            Project project = projectService.getById(projectId);
            if (project == null) {
                return "项目不存在";
            }

            String projectInfo = projectFunctions.getProjectDetails().apply(projectId);
            String taskStats = taskFunctions.getTaskStats().apply(projectId);

            String analysisPrompt = String.format("""
                请分析以下项目数据并提供专业建议：
                
                项目信息：
                %s
                
                任务统计：
                %s
                
                请从以下角度进行分析：
                1. 项目进度分析
                2. 资源配置建议
                3. 风险识别
                4. 优化建议
                5. 下一步行动计划
                """, projectInfo, taskStats);

            return chatClient.prompt()
                    .user(analysisPrompt)
                    .call()
                    .content();

        } catch (Exception e) {
            log.error("项目分析失败", e);
            return "项目分析时发生错误: " + e.getMessage();
        }
    }

    @Override
    public String generateProjectReport(Long projectId, String reportType) {
        try {
            Project project = projectService.getById(projectId);
            if (project == null) {
                return "项目不存在";
            }

            String projectInfo = projectFunctions.getProjectDetails().apply(projectId);
            String taskStats = taskFunctions.getTaskStats().apply(projectId);

            String reportPrompt = String.format("""
                请生成一份%s类型的项目报告：
                
                项目信息：
                %s
                
                任务统计：
                %s
                
                报告要求：
                - 格式专业、结构清晰
                - 包含关键数据和指标
                - 提供分析和建议
                - 适合管理层阅读
                """, reportType, projectInfo, taskStats);

            return chatClient.prompt()
                    .user(reportPrompt)
                    .call()
                    .content();

        } catch (Exception e) {
            log.error("生成项目报告失败", e);
            return "生成项目报告时发生错误: " + e.getMessage();
        }
    }

    @Override
    public String suggestTaskAssignment(Long projectId) {
        try {
            String taskInfo = taskFunctions.queryTasks().apply(
                new TaskManagementFunctions.TaskQueryRequest() {{
                    projectId = projectId;
                    pageSize = 50; // 获取更多任务信息
                }}
            );

            String suggestionPrompt = String.format("""
                基于以下任务信息，请提供智能的任务分配建议：
                
                任务列表：
                %s
                
                请考虑以下因素：
                1. 任务优先级和依赖关系
                2. 团队成员的工作负载
                3. 技能匹配度
                4. 项目时间线
                5. 资源优化
                
                请提供具体的分配建议和理由。
                """, taskInfo);

            return chatClient.prompt()
                    .user(suggestionPrompt)
                    .call()
                    .content();

        } catch (Exception e) {
            log.error("任务分配建议生成失败", e);
            return "生成任务分配建议时发生错误: " + e.getMessage();
        }
    }

    /**
     * 构建上下文信息
     */
    private String buildContextInfo(com.vwatj.ppms.ai.dto.ChatRequest request) {
        StringBuilder context = new StringBuilder();
        
        context.append("当前时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        
        if (request.getUserId() != null) {
            context.append("用户ID: ").append(request.getUserId()).append("\n");
        }
        
        if (request.getProjectId() != null) {
            try {
                Project project = projectService.getById(request.getProjectId());
                if (project != null) {
                    context.append("当前项目: ").append(project.getName()).append(" (ID: ").append(project.getId()).append(")\n");
                    context.append("项目状态: ").append(project.getStatus()).append("\n");
                }
            } catch (Exception e) {
                log.warn("获取项目上下文信息失败", e);
            }
        }
        
        if (request.getContext() != null && !request.getContext().isEmpty()) {
            context.append("额外上下文: ").append(request.getContext()).append("\n");
        }
        
        return context.toString();
    }

    /**
     * 构建完整的提示词
     */
    private String buildFullPrompt(String userMessage, String contextInfo, com.vwatj.ppms.ai.dto.ChatRequest request) {
        StringBuilder prompt = new StringBuilder();
        
        if (!contextInfo.isEmpty()) {
            prompt.append("上下文信息：\n").append(contextInfo).append("\n");
        }
        
        if (request.getHistory() != null && !request.getHistory().isEmpty()) {
            prompt.append("对话历史：\n");
            for (com.vwatj.ppms.ai.dto.ChatRequest.ChatMessage msg : request.getHistory()) {
                prompt.append(msg.getRole()).append(": ").append(msg.getContent()).append("\n");
            }
            prompt.append("\n");
        }
        
        prompt.append("用户问题: ").append(userMessage);
        
        return prompt.toString();
    }

    /**
     * 检查是否需要调用业务功能
     */
    private boolean needsFunctionCall(String message) {
        String lowerMessage = message.toLowerCase();
        return lowerMessage.contains("创建") || lowerMessage.contains("查询") || 
               lowerMessage.contains("更新") || lowerMessage.contains("删除") ||
               lowerMessage.contains("分配") || lowerMessage.contains("统计");
    }

    /**
     * 处理功能调用
     */
    private void handleFunctionCalls(com.vwatj.ppms.ai.dto.ChatRequest request, com.vwatj.ppms.ai.dto.ChatResponse response) {
        // 这里可以实现更复杂的功能调用逻辑
        // 根据用户消息内容智能判断需要调用哪些业务功能
        List<com.vwatj.ppms.ai.dto.ChatResponse.FunctionCall> functionCalls = new ArrayList<>();
        response.setFunctionCalls(functionCalls);
        response.setFunctionCalled(true);
    }

    /**
     * 添加建议操作
     */
    private void addSuggestedActions(com.vwatj.ppms.ai.dto.ChatRequest request, com.vwatj.ppms.ai.dto.ChatResponse response) {
        List<com.vwatj.ppms.ai.dto.ChatResponse.SuggestedAction> actions = new ArrayList<>();
        
        // 根据上下文添加相关的建议操作
        if (request.getProjectId() != null) {
            actions.add(createSuggestedAction("查看项目详情", "获取项目的详细信息", 
                Map.of("action", "getProjectDetails", "projectId", request.getProjectId())));
            actions.add(createSuggestedAction("查看项目任务", "查看项目下的所有任务", 
                Map.of("action", "queryTasks", "projectId", request.getProjectId())));
        }
        
        response.setSuggestedActions(actions);
    }

    private com.vwatj.ppms.ai.dto.ChatResponse.SuggestedAction createSuggestedAction(String action, String description, Map<String, Object> parameters) {
        com.vwatj.ppms.ai.dto.ChatResponse.SuggestedAction suggestedAction = new com.vwatj.ppms.ai.dto.ChatResponse.SuggestedAction();
        suggestedAction.setAction(action);
        suggestedAction.setDescription(description);
        suggestedAction.setParameters(parameters);
        return suggestedAction;
    }
}
