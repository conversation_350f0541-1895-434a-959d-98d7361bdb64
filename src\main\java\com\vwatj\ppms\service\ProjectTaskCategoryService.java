package com.vwatj.ppms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.vwatj.ppms.common.PageResult;
import com.vwatj.ppms.dto.CreateTaskCategoryDTO;
import com.vwatj.ppms.dto.TaskCategoryQueryDTO;
import com.vwatj.ppms.dto.UpdateTaskCategoryDTO;
import com.vwatj.ppms.entity.ProjectTaskCategory;

import java.util.List;
import java.util.Map;

/**
 * 任务分类服务接口
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public interface ProjectTaskCategoryService extends IService<ProjectTaskCategory> {

    /**
     * 获取任务分类选项列表
     * @return 选项列表
     */
    List<Map<String, String>> getTaskCategoryOptions();

    /**
     * 分页查询任务分类
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<ProjectTaskCategory> getTaskCategoryPage(TaskCategoryQueryDTO queryDTO);

    /**
     * 创建任务分类
     * @param createTaskCategoryDTO 创建参数
     * @return 创建的任务分类
     */
    ProjectTaskCategory createTaskCategory(CreateTaskCategoryDTO createTaskCategoryDTO);

    /**
     * 更新任务分类
     * @param updateTaskCategoryDTO 更新参数
     * @return 更新的任务分类
     */
    ProjectTaskCategory updateTaskCategory(UpdateTaskCategoryDTO updateTaskCategoryDTO);

    /**
     * 删除任务分类
     * @param id 分类ID
     */
    void deleteTaskCategory(Long id);
}
