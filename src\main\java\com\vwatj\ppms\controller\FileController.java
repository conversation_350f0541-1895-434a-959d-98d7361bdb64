package com.vwatj.ppms.controller;

import com.vwatj.ppms.common.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/file")
@Slf4j
public class FileController {

    @Value("${file.upload.cache-path:./uploads/cache}")
    private String cacheUploadPath;

    @Value("${server.domain:http://localhost:8080}")
    private String serverDomain;

    /**
     * 上传文件到缓存区
     */
    @PostMapping("/upload/cache")
    public ApiResponse<Map<String, Object>> uploadToCache(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return ApiResponse.error("文件不能为空");
        }

        try {
            // 创建缓存目录
            Path cacheDir = Paths.get(cacheUploadPath);
            if (!Files.exists(cacheDir)) {
                Files.createDirectories(cacheDir);
            }

            // 生成缓存文件名：cache_时间戳_UUID_原文件名
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String uuid = UUID.randomUUID().toString().substring(0, 8);
            String originalFilename = file.getOriginalFilename();
            String cacheFileName = String.format("cache_%s_%s_%s", timestamp, uuid, originalFilename);

            // 保存文件到缓存目录
            Path targetPath = cacheDir.resolve(cacheFileName);
            Files.copy(file.getInputStream(), targetPath, StandardCopyOption.REPLACE_EXISTING);

            // 构造返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("fileName", cacheFileName);
            result.put("originalName", originalFilename);
            result.put("url", serverDomain + "/api/file-upload/cache/" + cacheFileName);
            result.put("size", file.getSize());
            result.put("type", file.getContentType());

            log.info("文件上传到缓存区成功: {} -> {}", originalFilename, cacheFileName);
            return ApiResponse.success(result);

        } catch (IOException e) {
            log.error("文件上传失败", e);
            return ApiResponse.error("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 下载/预览缓存区文件
     */
    @GetMapping("/down/cache/{fileName}")
    public ResponseEntity<Resource> downloadCacheFile(@PathVariable String fileName) {
        try {
            // 验证文件名格式（安全检查）
            if (!fileName.startsWith("cache_") || fileName.contains("..")) {
                return ResponseEntity.badRequest().build();
            }

            Path filePath = Paths.get(cacheUploadPath).resolve(fileName);
            Resource resource = new UrlResource(filePath.toUri());

            if (!resource.exists() || !resource.isReadable()) {
                return ResponseEntity.notFound().build();
            }

            // 获取文件的MIME类型
            String contentType = Files.probeContentType(filePath);
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            // 提取原始文件名（从缓存文件名中解析）
            String originalFileName = extractOriginalFileName(fileName);

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION,
                           "inline; filename=\"" + originalFileName + "\"")
                    .body(resource);

        } catch (MalformedURLException e) {
            log.error("文件下载失败", e);
            return ResponseEntity.badRequest().build();
        } catch (IOException e) {
            log.error("文件读取失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 从缓存文件名中提取原始文件名
     * 格式：cache_时间戳_UUID_原文件名
     */
    private String extractOriginalFileName(String cacheFileName) {
        String[] parts = cacheFileName.split("_", 4);
        if (parts.length >= 4) {
            return parts[3]; // 返回原始文件名部分
        }
        return cacheFileName; // 如果解析失败，返回缓存文件名
    }
}
