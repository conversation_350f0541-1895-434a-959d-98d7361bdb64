package com.vwatj.ppms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.vwatj.ppms.entity.ProjectProcess;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目阶段Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Mapper
public interface ProjectProcessMapper extends BaseMapper<ProjectProcess> {

    /**
     * 根据项目ID获取所有阶段（按排序号排序）
     *
     * @param projectId 项目ID
     * @return 项目阶段列表
     */
    @Select("SELECT * FROM project_process WHERE project_id = #{projectId} AND deleted = 0 ORDER BY sort_order")
    List<ProjectProcess> selectByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目ID和阶段Key获取阶段
     *
     * @param projectId 项目ID
     * @param stageKey 阶段Key
     * @return 项目阶段
     */
    @Select("SELECT * FROM project_process WHERE project_id = #{projectId} AND stage_key = #{stageKey} AND deleted = 0")
    ProjectProcess selectByProjectIdAndStageKey(@Param("projectId") Long projectId, @Param("stageKey") String stageKey);

    /**
     * 获取项目当前活跃阶段
     *
     * @param projectId 项目ID
     * @return 当前活跃阶段
     */
    @Select("SELECT * FROM project_process WHERE project_id = #{projectId} AND status = 'active' AND deleted = 0 ORDER BY sort_order LIMIT 1")
    ProjectProcess selectActiveStageByProjectId(@Param("projectId") Long projectId);



    /**
     * 更新阶段状态和进度
     *
     * @param projectId 项目ID
     * @param stageKey 阶段Key
     * @param status 状态
     * @param progress 进度
     * @param actualStartTime 实际开始时间
     * @param actualEndTime 实际结束时间
     * @return 更新行数
     */
    @Update("UPDATE project_process SET status = #{status}, progress = #{progress}, " +
            "actual_start_time = CASE WHEN #{actualStartTime} IS NOT NULL THEN #{actualStartTime} ELSE actual_start_time END, " +
            "actual_end_time = CASE WHEN #{actualEndTime} IS NOT NULL THEN #{actualEndTime} ELSE actual_end_time END, " +
            "update_time = NOW() " +
            "WHERE project_id = #{projectId} AND stage_key = #{stageKey} AND deleted = 0")
    int updateStageStatusAndProgress(@Param("projectId") Long projectId,
                                   @Param("stageKey") String stageKey,
                                   @Param("status") String status,
                                   @Param("progress") Integer progress,
                                   @Param("actualStartTime") LocalDateTime actualStartTime,
                                   @Param("actualEndTime") LocalDateTime actualEndTime);

    /**
     * 更新阶段流程配置
     *
     * @param projectId 项目ID
     * @param stageKey 阶段Key
     * @param stageProcess 阶段流程JSON
     * @return 更新行数
     */
    @Update("UPDATE project_process SET stage_process = #{stageProcess}, update_time = NOW() " +
            "WHERE project_id = #{projectId} AND stage_key = #{stageKey} AND deleted = 0")
    int updateStageProcess(@Param("projectId") Long projectId,
                          @Param("stageKey") String stageKey,
                          @Param("stageProcess") String stageProcess);



    /**
     * 批量插入项目阶段
     *
     * @param stages 阶段列表
     * @return 插入行数
     */
    int batchInsertProjectStages(@Param("stages") List<ProjectProcess> stages);
}
