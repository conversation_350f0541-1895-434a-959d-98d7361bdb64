package com.vwatj.ppms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.vwatj.ppms.entity.ProjectTask;
import com.vwatj.ppms.enums.TaskStatusEnum;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 任务Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Mapper
public interface ProjectTaskMapper extends BaseMapper<ProjectTask> {

    /**
     * 分页查询任务
     */
    IPage<ProjectTask> selectTaskPage(Page<ProjectTask> page,
                                      @Param("keyword") String keyword,
                                      @Param("projectId") Long projectId,
                                      @Param("status") String status,
                                      @Param("priority") String priority,
                                      @Param("stageName") String stageName,
                                      @Param("issueType") String issueType,
                                      @Param("assignee") String assignee,
                                      @Param("reporter") String reporter,
                                      @Param("plannedStartTime") LocalDateTime plannedStartTime,
                                      @Param("plannedEndTime") LocalDateTime plannedEndTime,
                                      @Param("actualStartTime") LocalDateTime actualStartTime,
                                      @Param("actualEndTime") LocalDateTime actualEndTime);

    /**
     * 查询项目任务
     */
    IPage<ProjectTask> selectProjectTasks(Page<ProjectTask> page, @Param("projectId") Long projectId);

    /**
     * 查询用户任务
     */
    IPage<ProjectTask> selectUserTasks(Page<ProjectTask> page, @Param("assignee") String assignee);

    /**
     * 高级搜索任务（支持所有筛选条件）
     */
    IPage<ProjectTask> searchTasks(Page<ProjectTask> page,
                                   @Param("keyword") String keyword,
                                   @Param("projectId") Long projectId,
                                   @Param("status") String status,
                                   @Param("priority") String priority,
                                   @Param("stageName") String stageName,
                                   @Param("issueType") String issueType,
                                   @Param("assignee") String assignee,
                                   @Param("reporter") String reporter,
                                   @Param("taskScope") String taskScope,
                                   @Param("currentUser") String currentUser,
                                   @Param("statusList") List<String> statusList,
                                   @Param("dateRangeStart") LocalDateTime dateRangeStart,
                                   @Param("dateRangeEnd") LocalDateTime dateRangeEnd,
                                   @Param("weekRangeStart") LocalDateTime weekRangeStart,
                                   @Param("weekRangeEnd") LocalDateTime weekRangeEnd);

    /**
     * 统计任务数量按状态
     */
    @MapKey("status")
    List<Map<String, Object>> countTasksByStatus(@Param("projectId") Long projectId);

    /**
     * 统计任务数量按优先级
     */
    @MapKey("priority")
    List<Map<String, Object>> countTasksByPriority(@Param("projectId") Long projectId);

    /**
     * 统计任务数量按问题类型
     */
    @MapKey("issueType")
    List<Map<String, Object>> countTasksByIssueType(@Param("projectId") Long projectId);

    /**
     * 统计逾期任务数量
     */
    Long countOverdueTasks(@Param("projectId") Long projectId);

    /**
     * 统计已完成任务数量
     */
    Long countCompletedTasks(@Param("projectId") Long projectId);

    /**
     * 统计项目任务总数
     */
    Long countTasksByProject(@Param("projectId") Long projectId);

    /**
     * 统计项目已完成任务数量
     */
    Long countTasksByProjectAndStatus(@Param("projectId") Long projectId, @Param("taskStatus") TaskStatusEnum taskStatus);
}
